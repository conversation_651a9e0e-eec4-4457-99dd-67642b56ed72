/*
 * Linux 3.4.x 兼容性头文件
 * 为高级优化特性提供向后兼容支持
 */

#ifndef LINUX34_COMPAT_H
#define LINUX34_COMPAT_H

#include <linux/version.h>
#include <linux/list.h>
#include <linux/rculist.h>

// Linux 3.4.x 原子操作兼容性
#ifndef atomic64_t
#define atomic64_t atomic_long_t
#define atomic64_read atomic_long_read
#define atomic64_set atomic_long_set
#define atomic64_inc atomic_long_inc
#define atomic64_add atomic_long_add
#define atomic64_dec atomic_long_dec
#define atomic64_sub atomic_long_sub
#endif

// hlist RCU 宏兼容性 - Linux 3.4.x 使用不同的参数顺序
#if LINUX_VERSION_CODE < KERNEL_VERSION(3,9,0)
// Linux 3.4.x 的 hlist_for_each_entry_rcu 需要 4 个参数
#undef hlist_for_each_entry_rcu
#define hlist_for_each_entry_rcu(tpos, pos, head, member) \
    for (pos = rcu_dereference_raw(hlist_first_rcu(head)); \
         pos && \
         ({ tpos = hlist_entry(pos, typeof(*tpos), member); 1; }); \
         pos = rcu_dereference_raw(hlist_next_rcu(pos)))

// Linux 3.4.x 的 hlist_for_each_entry_safe 需要 5 个参数
#undef hlist_for_each_entry_safe
#define hlist_for_each_entry_safe(tpos, pos, n, head, member) \
    for (pos = (head)->first; \
         pos && ({ n = pos->next; 1; }) && \
         ({ tpos = hlist_entry(pos, typeof(*tpos), member); 1; }); \
         pos = n)
#endif

// RCU 兼容性
#ifndef kfree_rcu
#define kfree_rcu(ptr, rcu_head) call_rcu(&(ptr)->rcu_head, (void (*)(struct rcu_head *))kfree)
#endif

// 网络特性兼容性
#ifndef NETIF_F_HW_CSUM
#define NETIF_F_HW_CSUM NETIF_F_IP_CSUM
#endif

// SKB 兼容性
#ifndef skb_mac_header_was_set
#define skb_mac_header_was_set(skb) ((skb)->mac_header != (typeof((skb)->mac_header))~0U)
#endif

#ifndef skb_transport_header_was_set
#define skb_transport_header_was_set(skb) ((skb)->transport_header != (typeof((skb)->transport_header))~0U)
#endif

// 预取兼容性
#ifndef prefetch
#define prefetch(x) do { } while (0)
#endif

// 分支预测兼容性
#ifndef likely
#define likely(x)   __builtin_expect(!!(x), 1)
#define unlikely(x) __builtin_expect(!!(x), 0)
#endif

// 编译器属性兼容性
#ifndef __aligned
#define __aligned(x) __attribute__((aligned(x)))
#endif

// 哈希函数兼容性
#ifndef hash_32
static inline u32 hash_32(u32 val, unsigned int bits)
{
    /* High bits are more random, so use them. */
    return (val * 0x61C88647) >> (32 - bits);
}
#endif

// 时间兼容性
#ifndef msecs_to_jiffies
#define msecs_to_jiffies(m) ((m) * HZ / 1000)
#endif

// 工作队列兼容性检查
#if LINUX_VERSION_CODE < KERNEL_VERSION(2,6,20)
#error "Linux kernel version too old, minimum required: 2.6.20"
#endif

// 调试宏
#ifdef DEBUG_COMPAT
#define compat_debug(fmt, ...) printk(KERN_DEBUG "COMPAT: " fmt, ##__VA_ARGS__)
#else
#define compat_debug(fmt, ...) do { } while (0)
#endif

#endif /* LINUX34_COMPAT_H */
