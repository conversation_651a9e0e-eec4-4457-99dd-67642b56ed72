/*
 * 简化的编译测试文件
 * 用于验证Linux 3.4.x兼容性
 */

#include <linux/module.h>
#include <linux/kernel.h>
#include <linux/init.h>
#include <linux/hash.h>
#include <linux/rcupdate.h>
#include <linux/slab.h>
#include <linux/timer.h>
#include <linux/workqueue.h>
#include <linux/moduleparam.h>
#include <linux/atomic.h>
#include <linux/list.h>

// 测试数据结构
typedef struct whitelist_node {
    struct hlist_node hnode;
    __be32 ip;
    struct rcu_head rcu;
} __attribute__((aligned(64))) WHITELIST_NODE;

// 测试变量
static struct hlist_head test_hash[32];
static DEFINE_SPINLOCK(test_lock);
static int hw_checksum_enabled = 1;
static int cpu_cache_prefetch_enabled = 1;

// 测试函数
static int test_hlist_operations(void)
{
    struct whitelist_node *node, *existing;
    struct hlist_node *pos, *tmp;
    int i;
    
    // 初始化哈希表
    for (i = 0; i < 32; i++) {
        INIT_HLIST_HEAD(&test_hash[i]);
    }
    
    // 测试添加节点
    node = kmalloc(sizeof(struct whitelist_node), GFP_KERNEL);
    if (!node) {
        return -ENOMEM;
    }
    
    node->ip = 0x01020304; // *******
    INIT_HLIST_NODE(&node->hnode);
    
    spin_lock(&test_lock);
    hlist_add_head_rcu(&node->hnode, &test_hash[0]);
    spin_unlock(&test_lock);
    
    // 测试RCU查找
    rcu_read_lock();
    hlist_for_each_entry_rcu(existing, pos, &test_hash[0], hnode) {
        if (existing->ip == 0x01020304) {
            printk("Found test IP in hash table\n");
            break;
        }
    }
    rcu_read_unlock();
    
    // 测试清理
    spin_lock(&test_lock);
    hlist_for_each_entry_safe(existing, pos, tmp, &test_hash[0], hnode) {
        hlist_del_rcu(&existing->hnode);
        call_rcu(&existing->rcu, (void (*)(struct rcu_head *))kfree);
    }
    spin_unlock(&test_lock);
    
    synchronize_rcu();
    
    return 0;
}

// 模块参数
module_param(hw_checksum_enabled, int, 0644);
MODULE_PARM_DESC(hw_checksum_enabled, "Enable hardware checksum acceleration");

module_param(cpu_cache_prefetch_enabled, int, 0644);
MODULE_PARM_DESC(cpu_cache_prefetch_enabled, "Enable CPU cache prefetching");

// 初始化函数
static int __init test_init(void)
{
    int ret;
    
    printk("Linux 3.4.x compatibility test module loading...\n");
    
    ret = test_hlist_operations();
    if (ret) {
        printk("Hash list operations test failed: %d\n", ret);
        return ret;
    }
    
    printk("All compatibility tests passed!\n");
    printk("Hardware checksum: %s\n", hw_checksum_enabled ? "enabled" : "disabled");
    printk("CPU cache prefetch: %s\n", cpu_cache_prefetch_enabled ? "enabled" : "disabled");
    
    return 0;
}

// 清理函数
static void __exit test_exit(void)
{
    printk("Linux 3.4.x compatibility test module unloading...\n");
}

module_init(test_init);
module_exit(test_exit);

MODULE_LICENSE("GPL");
MODULE_AUTHOR("Captive Portal Team");
MODULE_DESCRIPTION("Linux 3.4.x Compatibility Test Module");
MODULE_VERSION("1.0");
