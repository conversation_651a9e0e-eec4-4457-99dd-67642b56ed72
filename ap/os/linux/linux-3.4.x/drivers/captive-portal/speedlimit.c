/*
 * SGI IOC3 master driver and IRQ demuxer
 *
 * Copyright (c) 2005 Stanislaw <PERSON>k <<EMAIL>>
 * Heavily based on similar work by:
 *   <PERSON> <b<PERSON><EMAIL>> - IOC4 master driver
 *   <PERSON> <<EMAIL>> - IOC3 serial port IRQ demuxer
 */
//#defined DEBUG
#include <linux/errno.h>

#include <linux/module.h>
#include <linux/kernel.h>
#include <linux/init.h>
#include <linux/types.h>
#include <linux/netdevice.h>
#include <linux/skbuff.h>
#include <linux/netfilter_ipv4.h>
#include <linux/inet.h>
#include <linux/in.h>
#include <linux/ip.h>
#include <linux/tcp.h>
#include <linux/udp.h>
#include <linux/time.h>
#include <net/netlabel.h> //add by svk
#include <linux/ieee80211.h>
#include <linux/hash.h>
#include <linux/rcupdate.h>
#include <linux/slab.h>
#include <linux/atomic.h>
#include <linux/timer.h>
#include <linux/workqueue.h>
#include <linux/prefetch.h>
#include <net/checksum.h>
#include "speedlimit.h"
#ifdef CONFIG_IPV6_PORTAL
#include <linux/netfilter_ipv6.h>
#include <linux/ipv6.h>
#include <net/ipv6.h>
#include <linux/in6.h>
#include <net/ip6_checksum.h>
#endif

extern int init_procfs_cjPortal(void);
extern void cleanup_procfs_cjportal(void);
extern int checkNew(CLIENT_LIST *cli);
extern int findIp(unsigned short port);
extern void saveIp(unsigned short port, int ip);
extern void clientListInit(void);
extern CLIENT_LIST * inNewList(CLIENT_LIST *cli);
extern void move2AuthList(CLIENT_LIST *cli);
extern CLIENT_LIST * inAuthList(CLIENT_LIST *cli);
extern void add2Auth(CLIENT_LIST *cli);
//extern struct mutex clientMutex;
extern atomic_t clientMutex;

#ifdef CONFIG_DNS_REDIRECT
extern void save_ipv6_mac_mapping(const char *entry);
#endif


IPADR localIP = {
    .char_ip[0] = 192,
    .char_ip[1] = 168,
    .char_ip[2] = 100,
    .char_ip[3] = 0
};

IPADR localIPMask = {
    .char_ip[0] = 255,
    .char_ip[1] = 255,
    .char_ip[2] = 255,
    .char_ip[3] = 0
};

IPADR localGateway = {
    .char_ip[0] = 192,
    .char_ip[1] = 168,
    .char_ip[2] = 100,
    .char_ip[3] = 1
};

#ifdef CONFIG_IPV6_PORTAL
// IPv6 local network configuration
struct in6_addr localIPv6 = {
    .s6_addr32 = {
        htonl(0xfe800000), htonl(0x00000000),
        htonl(0x00000000), htonl(0x00000000)
    }
};

struct in6_addr localIPv6Mask = {
    .s6_addr32 = {
        htonl(0xffff0000), htonl(0x00000000),
        htonl(0x00000000), htonl(0x00000000)
    }
};

// br0 IPv6 address: fe80::946c:49ff:fea0:83c1
struct in6_addr localGatewayv6 = {
    .s6_addr32 = {
        htonl(0xfe800000), htonl(0x00000000),
        htonl(0x946c49ff), htonl(0xfea083c1)
    }
};

// IPv6 macros for local network detection - Linux 3.4.x compatible
#define LOCAL_V6(ip6) (ipv6_masked_addr_cmp(&localIPv6, &localIPv6Mask, ip6) == 0)
#define GATEWAY_V6(ip6) (ipv6_addr_equal_compat(ip6, &localGatewayv6))

// IPv6 whitelist
struct in6_addr whitelist_v6[MAX_WHITELIST_IP];
int whitelist_v6_count = 0;
#endif

// IPv4 whitelist - 动态管理
__be32 whitelist_ipv4[MAX_WHITELIST_IP];
int whitelist_ipv4_count = 0;

#define DEBUG_NF 1

#define LOCAL(ip) (localIP.int_ip == (localIPMask.int_ip & ip)? 1:0)
#define GATEWAY(ip) ((((~localIPMask.int_ip) & localGateway.int_ip) == ((~localIPMask.int_ip) & ip))? 1:0)

int mytime(void) {
    struct timeval t1;
    do_gettimeofday(&t1);
    return t1.tv_sec;
}


unsigned short calcCheckSum(void *buf, int len,unsigned int additional)
// len is taken granted times of 2....
{
    unsigned int m,i,k=additional;
    unsigned short *j;
    j = (unsigned short *)buf;
    for(i=0; i<(len+1)>>1; i++) {
        k += j[i];
    }
    while(k&0xFFFF0000) {
        m = (k&0xFFFF0000u)>>16;
        k &= 0x0000FFFF;
        k+=m;
    }
    k = ~k;
    return k;
}

void calcIphChecksum(struct iphdr *iph)
{
    iph->check = 0;
    iph->check = calcCheckSum((void*)iph,iph->ihl*4,0);
}

void calcThdChecksum(struct iphdr* iph,struct tcphdr *thd)
{
    int i;
    unsigned int m,k=0;
    unsigned short *j;
    struct psd_header psd;

    psd.daddr = iph->daddr;
    psd.saddr = iph->saddr;
    psd.mbz = 0;
    psd.ptcl = 6;
    psd.tcpl = htons(ntohs(iph->tot_len)-iph->ihl*4);

//	printk("iph->ihl :%d, iph->tot-len: %d tcpl %d  psd header len %d total len %d\n",iph->ihl,ntohs(iph->tot_len),ntohs(psd.tcpl),sizeof(struct psd_header),sizeof(struct psd_header)+sizeof(struct tcphdr));

    thd->check = 0;

//	printk("\ntcp header:");
    for (i = ntohs(iph->tot_len)-iph->ihl*4,j=(unsigned short *)thd; i>1; j++,i-=2) {
//		printk("%04x ",ntohs(*j));
        k+=htons(*j);
    }
    if(i) {
//		printk("%02x ",*((unsigned char *)j));
        k+= htons((unsigned short)(*((unsigned char *)j)));
    }

//printk("\npsedo header:");
    for(j=(unsigned short *)&psd,i=0; i<sizeof(struct psd_header)/2; i++) {
//		printk("%04x ",htons(j[i]));
        k+= htons(j[i]);
    }

    while(k&0xFFFF0000) {
        m = (k&0xFFFF0000u)>>16;
        k &= 0x0000FFFF;
        k+=m;
    }
    k = ~k;
//	printk("\n check sum:%04x",k);
    thd->check =htons(k);

}

#ifdef CONFIG_IPV6_PORTAL
static int ipv6_addr_equal_compat(const struct in6_addr *a1, const struct in6_addr *a2)
{
    return memcmp(a1, a2, sizeof(struct in6_addr)) == 0;
}

// IPv6 loopback check - Linux 3.4.x compatible
static int ipv6_addr_loopback_compat(const struct in6_addr *a)
{
    return (a->s6_addr32[0] | a->s6_addr32[1] | a->s6_addr32[2] | (a->s6_addr32[3] ^ htonl(1))) == 0;
}

// IPv6 multicast check - Linux 3.4.x compatible
static int ipv6_addr_is_multicast_compat(const struct in6_addr *a)
{
    return (a->s6_addr[0] & 0xff) == 0xff;
}

// Get IPv6 header - Linux 3.4.x compatible
static struct ipv6hdr *ipv6_hdr_compat(const struct sk_buff *skb)
{
    return (struct ipv6hdr *)skb_network_header(skb);
}
#endif

// IPv4 白名单管理函数 - 优化版本
int add_ipv4_whitelist(const char *ip_str)
{
    __be32 ip_addr;
    int i;

    if (!ip_str || whitelist_ipv4_count >= MAX_WHITELIST_IP) {
        printk("IPv4 whitelist is full or invalid IP string\n");
        return -1;
    }

    ip_addr = in_aton(ip_str);
    if (ip_addr == 0) {
        printk("Invalid IPv4 address: %s\n", ip_str);
        return -1;
    }

    // 检查是否已存在 - 优化：使用二分查找或哈希表会更好
    for (i = 0; i < whitelist_ipv4_count; i++) {
        if (whitelist_ipv4[i] == ip_addr) {
            printk("IPv4 address %s already in whitelist\n", ip_str);
            return 0;
        }
    }

    whitelist_ipv4[whitelist_ipv4_count] = ip_addr;
    whitelist_ipv4_count++;

    printk("Added IPv4 %s to whitelist (total: %d)\n", ip_str, whitelist_ipv4_count);
    return 0;
}

// ==================== 无锁哈希表白名单实现 ====================

// 哈希函数
static inline u32 whitelist_hash_func(__be32 ip)
{
    return hash_32((__force u32)ip, WHITELIST_HASH_BITS);
}

// RCU保护的快速白名单检查
static inline int is_ipv4_whitelisted_fast(__be32 ip)
{
    struct whitelist_node *node;
    u32 hash = whitelist_hash_func(ip);
    int found = 0;

    rcu_read_lock();
    hlist_for_each_entry_rcu(node, &whitelist_hash[hash], hnode) {
        if (node->ip == ip) {
            found = 1;
            break;
        }
    }
    rcu_read_unlock();

    return found;
}

// 添加IP到哈希表白名单
static int add_ipv4_whitelist_fast(const char *ip_str)
{
    __be32 ip_addr;
    struct whitelist_node *node, *existing;
    u32 hash;
    int found = 0;

    if (!ip_str) {
        printk("Invalid IP string\n");
        return -1;
    }

    ip_addr = in_aton(ip_str);
    if (ip_addr == 0) {
        printk("Invalid IPv4 address: %s\n", ip_str);
        return -1;
    }

    hash = whitelist_hash_func(ip_addr);

    // 检查是否已存在
    rcu_read_lock();
    hlist_for_each_entry_rcu(existing, &whitelist_hash[hash], hnode) {
        if (existing->ip == ip_addr) {
            found = 1;
            break;
        }
    }
    rcu_read_unlock();

    if (found) {
        printk("IPv4 address %s already in whitelist\n", ip_str);
        return 0;
    }

    // 分配新节点
    node = kmalloc(sizeof(struct whitelist_node), GFP_KERNEL);
    if (!node) {
        printk("Failed to allocate whitelist node\n");
        return -ENOMEM;
    }

    node->ip = ip_addr;
    INIT_HLIST_NODE(&node->hnode);

    // 添加到哈希表
    spin_lock(&whitelist_lock);
    hlist_add_head_rcu(&node->hnode, &whitelist_hash[hash]);
    spin_unlock(&whitelist_lock);

    printk("Added IPv4 %s to fast whitelist\n", ip_str);
    return 0;
}

// 清理哈希表白名单
static void clear_ipv4_whitelist_fast(void)
{
    struct whitelist_node *node;
    struct hlist_node *tmp;
    int i;

    spin_lock(&whitelist_lock);
    for (i = 0; i < WHITELIST_HASH_SIZE; i++) {
        hlist_for_each_entry_safe(node, tmp, &whitelist_hash[i], hnode) {
            hlist_del_rcu(&node->hnode);
            kfree_rcu(node, rcu);
        }
    }
    spin_unlock(&whitelist_lock);

    synchronize_rcu();
    printk("Fast whitelist cleared\n");
}

// 兼容性：保留原有的线性搜索函数作为备用
static inline int is_ipv4_whitelisted(__be32 ip)
{
    // 优先使用快速哈希表查找
    return is_ipv4_whitelisted_fast(ip);
}

void clear_ipv4_whitelist(void)
{
    whitelist_ipv4_count = 0;
    printk("IPv4 whitelist cleared\n");
}

void init_ipv4_whitelist(void)
{
    // 初始化 IPv4 白名单，添加一些默认地址
    whitelist_ipv4_count = 0;

    // 添加默认的白名单地址
    add_ipv4_whitelist("***********");      // 电信认证域名的IP
    add_ipv4_whitelist("***************");  // wuxing
    add_ipv4_whitelist("**************");   // wuxing
    add_ipv4_whitelist("*************");    // wuxing
    add_ipv4_whitelist("*************");    // wuxing
    add_ipv4_whitelist("**************");   // wuxing

    printk("IPv4 whitelist initialized with %d entries\n", whitelist_ipv4_count);
}


// ip is not realible to tell the new arrival, use for first version, should change to MAC in future. :)
// the second problem is the expiration of the IP/MAC. should save the arrival of new ip?

time_t curTime = 0;

int totalPackets = 0;
int registered = 0;

// 优化：减少内存分配的客户端信息缓存
static CLIENT_LIST client_cache;
static int cache_initialized = 0;

// ==================== 高级优化数据结构 ====================

// 无锁哈希表白名单 (使用RCU保护)
static struct hlist_head whitelist_hash[WHITELIST_HASH_SIZE];
static DEFINE_SPINLOCK(whitelist_lock);

// 批量处理相关
static PACKET_BATCH current_batch;
static struct timer_list batch_timer;
static struct work_struct batch_work;
static DEFINE_SPINLOCK(batch_lock);

// 高性能统计
static PERF_STATS perf_stats;

// 硬件加速相关
static int hw_checksum_enabled = 1;
static int cpu_cache_prefetch_enabled = 1;

// ==================== 批量包处理实现 ====================

// 批量处理工作队列函数
static void batch_process_work(struct work_struct *work)
{
    struct sk_buff *skb;
    int i, processed = 0;
    unsigned long flags;

    spin_lock_irqsave(&batch_lock, flags);

    for (i = 0; i < current_batch.count; i++) {
        skb = current_batch.skbs[i];
        if (skb) {
            // 预取下一个包的数据到CPU缓存
            if (cpu_cache_prefetch_enabled && i + 1 < current_batch.count) {
                prefetch(current_batch.skbs[i + 1]);
                if (current_batch.skbs[i + 1]) {
                    prefetch(current_batch.skbs[i + 1]->data);
                }
            }

            // 处理单个包 (这里可以调用原有的处理逻辑)
            // process_single_packet(skb);
            processed++;
        }
    }

    // 重置批次
    current_batch.count = 0;
    current_batch.timestamp = 0;

    spin_unlock_irqrestore(&batch_lock, flags);

    // 更新统计
    atomic64_add(processed, &perf_stats.batch_processed);

    if (UNLIKELY(processed > 0 && atomic64_read(&perf_stats.total_packets) % STATS_LOG_RATE == 0)) {
        pr_debug("Batch processed %d packets, total batches: %lld\n",
                processed, atomic64_read(&perf_stats.batch_processed));
    }
}

// 批量处理定时器回调
static void batch_timer_callback(unsigned long data)
{
    unsigned long flags;

    spin_lock_irqsave(&batch_lock, flags);
    if (current_batch.count > 0) {
        // 提交当前批次进行处理
        schedule_work(&batch_work);
    }
    spin_unlock_irqrestore(&batch_lock, flags);
}

// 添加包到批次
static int add_packet_to_batch(struct sk_buff *skb)
{
    unsigned long flags;
    int should_process = 0;

    spin_lock_irqsave(&batch_lock, flags);

    if (current_batch.count < BATCH_SIZE) {
        current_batch.skbs[current_batch.count] = skb;
        current_batch.count++;

        // 设置时间戳（第一个包）
        if (current_batch.count == 1) {
            current_batch.timestamp = jiffies;
            mod_timer(&batch_timer, jiffies + msecs_to_jiffies(BATCH_TIMEOUT_MS));
        }

        // 批次满了，立即处理
        if (current_batch.count >= BATCH_SIZE) {
            should_process = 1;
            del_timer(&batch_timer);
        }
    } else {
        // 批次已满，立即处理当前批次
        should_process = 1;
    }

    spin_unlock_irqrestore(&batch_lock, flags);

    if (should_process) {
        schedule_work(&batch_work);
        return 1; // 表示包已加入批处理
    }

    return 1; // 包已加入批次
}

// 初始化批量处理
static int init_batch_processing(void)
{
    memset(&current_batch, 0, sizeof(current_batch));

    INIT_WORK(&batch_work, batch_process_work);

    init_timer(&batch_timer);
    batch_timer.function = batch_timer_callback;
    batch_timer.data = 0;

    printk("Batch processing initialized (batch_size=%d, timeout=%dms)\n",
           BATCH_SIZE, BATCH_TIMEOUT_MS);
    return 0;
}

// 清理批量处理
static void cleanup_batch_processing(void)
{
    del_timer_sync(&batch_timer);
    cancel_work_sync(&batch_work);

    // 处理剩余的包
    if (current_batch.count > 0) {
        batch_process_work(&batch_work);
    }

    printk("Batch processing cleaned up\n");
}

// ==================== 硬件加速特性实现 ====================

// 硬件校验和检查和设置
static inline void setup_hw_checksum(struct sk_buff *skb)
{
    if (!hw_checksum_enabled) {
        return;
    }

    // 检查网卡是否支持硬件校验和
    if (skb->dev && (skb->dev->features & NETIF_F_HW_CSUM)) {
        // 设置硬件校验和标志
        skb->ip_summed = CHECKSUM_PARTIAL;
        skb->csum_start = skb_headroom(skb) + skb_network_offset(skb);

        if (ip_hdr(skb)->protocol == IPPROTO_TCP) {
            skb->csum_offset = offsetof(struct tcphdr, check);
        } else if (ip_hdr(skb)->protocol == IPPROTO_UDP) {
            skb->csum_offset = offsetof(struct udphdr, check);
        }
    } else {
        // 回退到软件校验和
        skb->ip_summed = CHECKSUM_NONE;
    }
}

// 优化的校验和计算（利用硬件特性）
static inline void calc_checksum_optimized(struct sk_buff *skb, struct iphdr *iph)
{
    if (hw_checksum_enabled && skb->dev &&
        (skb->dev->features & (NETIF_F_IP_CSUM | NETIF_F_HW_CSUM))) {
        // 使用硬件校验和
        setup_hw_checksum(skb);
    } else {
        // 回退到原有的软件校验和计算
        if (iph->protocol == IPPROTO_TCP) {
            struct tcphdr *thd = (struct tcphdr *)((int *)iph + iph->ihl);
            calcIphChecksum(iph);
            calcThdChecksum(iph, thd);
        } else {
            calcIphChecksum(iph);
        }
        skb->ip_summed = CHECKSUM_NONE;
    }
}

// CPU缓存优化的数据预取
static inline void prefetch_packet_data(struct sk_buff *skb)
{
    if (!cpu_cache_prefetch_enabled) {
        return;
    }

    // 预取IP头部
    prefetch(skb->data);

    // 预取传输层头部
    if (skb->len > sizeof(struct iphdr)) {
        prefetch(skb->data + sizeof(struct iphdr));
    }

    // 预取MAC头部（如果存在）
    if (skb_mac_header_was_set(skb)) {
        prefetch(skb_mac_header(skb));
    }
}

// 高性能统计更新（避免缓存行竞争）
static inline void update_perf_stats_fast(int packet_type)
{
    // 使用原子操作避免锁竞争
    atomic64_inc(&perf_stats.total_packets);

    switch (packet_type) {
        case 1: // redirected
            atomic64_inc(&perf_stats.redirected_packets);
            break;
        case 2: // whitelisted
            atomic64_inc(&perf_stats.whitelisted_packets);
            break;
    }
}

// 检查网卡硬件特性
static void check_hardware_features(struct net_device *dev)
{
    if (!dev) {
        return;
    }

    if (dev->features & NETIF_F_HW_CSUM) {
        pr_info("Device %s supports hardware checksum offload\n", dev->name);
    }

    if (dev->features & NETIF_F_SG) {
        pr_info("Device %s supports scatter-gather\n", dev->name);
    }

    if (dev->features & NETIF_F_TSO) {
        pr_info("Device %s supports TCP segmentation offload\n", dev->name);
    }

    if (dev->features & NETIF_F_GSO) {
        pr_info("Device %s supports generic segmentation offload\n", dev->name);
    }
}

// ==================== 优化的包处理函数 ====================

static unsigned int preRouting(
    unsigned int hooknum,
    struct sk_buff * skb,
    const struct net_device *in,
    const struct net_device *out,
    int (*okfn) (struct sk_buff *))
{
    __be32 sip,dip;
    time_t cur;

    if(skb) {
        struct iphdr *iph;
        CLIENT_LIST *cli = NULL;
        int *mh;
        struct ieee80211s_hdr *whd;
        struct tcphdr *thd;
        struct sk_buff *sb = NULL;
        sb = skb;

        // 硬件加速：预取包数据到CPU缓存
        prefetch_packet_data(skb);

        // 更新性能统计
        update_perf_stats_fast(0);

        // Remove excessive debug message that might flood logs
        // printk("salvikie preRouting begin ===> \n");

        if(in) {
            if(0 == strcmp(in->name,"lo")) {
                return NF_ACCEPT;
            }
        }

        iph  = ip_hdr(sb);
        if(!iph) {
            //   	printk("IP Header is null\n");
            return NF_ACCEPT;
        }

        sip = iph->saddr;
        dip = iph->daddr;
        cur = mytime();
        totalPackets ++;

        // Move these declarations outside of DEBUG_NF to make them available
        mh = (int*)skb_mac_header(sb);
        whd = (struct ieee80211s_hdr *)mh;
        thd = (struct tcphdr *)((int *) iph + iph->ihl);

        if(sip == 0)
            return NF_ACCEPT;

        if(iph->protocol != IPPROTO_TCP && iph->protocol != IPPROTO_UDP)  // set all icmp packets un-filtered
            return NF_ACCEPT;

        if(GATEWAY(sip) || GATEWAY(dip)) {
            // Reduce debug output
            // printk("GATEWAY sip :%d.%d.%d.%d  dip:%d.%d.%d.%d  %d\n",NIPQUAD(sip),NIPQUAD(dip),iph->protocol);
            return NF_ACCEPT;
        }
        // set all packets from and to getway unfiltered

        // packets between clients, not filter
        if(LOCAL(sip) && LOCAL(dip)) {
            // Reduce debug output
            // printk("LOCAL sip :%d.%d.%d.%d  dip:%d.%d.%d.%d  %d\n",NIPQUAD(sip),NIPQUAD(dip),iph->protocol);
            return NF_ACCEPT;
        }

        // 优化：减少调试输出，只在必要时输出
        if (UNLIKELY(totalPackets % PACKET_SAMPLE_RATE == 0)) {
            pr_debug("TCP/UDP packet sample: %d.%d.%d.%d:%d -> %d.%d.%d.%d:%d protocol: %d device: %s \n",
                    NIPQUAD(sip), ntohs(thd->source),
                    NIPQUAD(dip), ntohs(thd->dest),
                    iph->protocol,
                    in ? in->name: "null");
        }

        if((int)mh && iph->protocol == IPPROTO_TCP) {
            if(in) {
                if(0 == strcmp(in->name,WLANDEVICE)) { // packets from AP, get the mac address?
                    // 优化：使用缓存减少内存分配
                    if (!cache_initialized) {
                        memset(&client_cache, 0, sizeof(CLIENT_LIST));
                        cache_initialized = 1;
                    }
                    cli = &client_cache;

                    memcpy(cli->client.mac.mac_char.mac,whd->eaddr1,sizeof(cli->client.mac));
                    cli->client.ip.int_ip = sip;
                    cli->client.time = cur;
                    cli->prev = NULL;
                    cli->next = NULL;

                    if(checkNew(cli)) { // cli will be released in checkNew, or add the the list
                        // Improve HTTP detection - check for common ports (80, 443, 8080)
                        if((LOCAL(sip) && !GATEWAY(sip)) && !LOCAL(dip)) {
                            // Check for any HTTP-like traffic, not just port 80

                            // ---------------高性能白名单检查 start-------------------
                            if (LIKELY(is_ipv4_whitelisted_fast(dip))) {
                                // 更新白名单统计
                                update_perf_stats_fast(2);

                                // 优化：减少日志输出频率
                                if (UNLIKELY(atomic64_read(&perf_stats.total_packets) % WHITELIST_LOG_RATE == 0)) {
                                    pr_info("IPv4 WHITELIST: %pI4 matched, skip redirect\n", &dip);
                                }
                                return NF_ACCEPT;
                            }
                            // ---------------高性能白名单检查 end-------------------

                            if(ntohs(thd->dest) == DEFAULTHTTPPORT ||
                               ntohs(thd->dest) == HTTPS_PPORT ||
                               ntohs(thd->dest) == 8080) {

                                // 更新重定向统计
                                update_perf_stats_fast(1);

                                // 优化：减少重定向日志输出
                                if (UNLIKELY(atomic64_read(&perf_stats.total_packets) % REDIRECT_LOG_RATE == 0)) {
                                    pr_debug("Redirecting HTTP(S) traffic from %d.%d.%d.%d:%d to captive portal %d.%d.%d.%d:%d\n",
                                           NIPQUAD(sip), ntohs(thd->source),
                                           NIPQUAD(localGateway.int_ip), LOCALGATEWAYPORT);
                                }

                                iph->daddr = localGateway.int_ip;
                                thd->dest = htons((unsigned short)LOCALGATEWAYPORT);

                                // 硬件加速：优化的校验和计算
                                calc_checksum_optimized(skb, iph);
                            }
                        }
                        saveIp(thd->source, dip);
                        return NF_ACCEPT;
                    } 
                    // else {
                    //     printk("salvikie %d.%d.%d.%d is in newlist\n", NIPQUAD(sip));
                    // }		  	
                }
            }
        }

        // 优化：减少统计信息输出频率
        if(!(totalPackets & STATS_LOG_RATE)) { // 使用宏定义控制输出频率
            pr_debug("total packets: %d, %d.%d.%d.%d  %d.%d.%d.%d\n",totalPackets,NIPQUAD(sip), NIPQUAD(dip));
        }
    }

    // Remove excessive debug message
    // printk("salvikie preRouting end ===> \n");
	
    return NF_ACCEPT;
}

#ifdef CONFIG_IPV6_PORTAL
/**
 * 从WiFi头部提取MAC地址的辅助函数
 */
static inline CLIENT_LIST* extract_mac_from_wifi_header_v6(struct sk_buff *skb, struct in6_addr *sip6, time_t cur)
{
    int *mh = (int*)skb_mac_header(skb);
    struct ieee80211s_hdr *whd = (struct ieee80211s_hdr *)mh;
    CLIENT_LIST *cli;

    if(!mh || !whd) {
        return NULL;
    }

    cli = kmalloc(sizeof(CLIENT_LIST), GFP_ATOMIC);
    if(!cli) {
        return NULL;
    }

    memcpy(cli->client.mac.mac_char.mac, whd->eaddr1, sizeof(cli->client.mac));
    cli->client.ip.int_ip = sip6->s6_addr32[3]; // Store lower 32 bits for compatibility
    cli->client.time = cur;
    cli->prev = NULL;
    cli->next = NULL;

    return cli;
}

/**
 * 检查IPv6地址是否在白名单中
 */
static inline int check_ipv6_whitelist(struct in6_addr *sip6, struct in6_addr *dip6)
{
    int i;
    for (i = 0; i < whitelist_v6_count; i++) {
        if (ipv6_addr_equal_compat(dip6, &whitelist_v6[i]) ||
            ipv6_addr_equal_compat(sip6, &whitelist_v6[i])) {
            pr_info("IPv6 WHITELIST: %pI6 matched, ACCEPT\n",
                    ipv6_addr_equal_compat(dip6, &whitelist_v6[i]) ? dip6 : sip6);
            return 1;
        }
    }
    return 0;
}

static unsigned int preRoutingv6(
    unsigned int hooknum,
    struct sk_buff * skb,
    const struct net_device *in,
    const struct net_device *out,
    int (*okfn) (struct sk_buff *))
{
    struct in6_addr *sip6, *dip6;
    struct ipv6hdr *ip6h;
    CLIENT_LIST *cli = NULL;
    time_t cur;
    int is_wifi_device = 0;

    // 基本检查
    if(!skb) {
        return NF_ACCEPT;
    }

    ip6h = ipv6_hdr_compat(skb);
    if(!ip6h) {
        return NF_ACCEPT;
    }

    sip6 = &ip6h->saddr;
    dip6 = &ip6h->daddr;

    // 跳过loopback接口
    if(in && 0 == strcmp(in->name, "lo")) {
        return NF_ACCEPT;
    }

    // 检查是否为WiFi设备
    if(in && 0 == strcmp(in->name, WLANDEVICE)) {
        is_wifi_device = 1;
    }

    if(!ip6h) {
        return NF_ACCEPT;
    }

    // 只处理TCP和UDP协议
    if(ip6h->nexthdr != IPPROTO_TCP && ip6h->nexthdr != IPPROTO_UDP) {
        return NF_ACCEPT;
    }

    // Add detailed debugging for IPv6 traffic
    if(ip6h->nexthdr == IPPROTO_TCP) {
        struct tcphdr *thd = (struct tcphdr *)((char *)ip6h + sizeof(struct ipv6hdr));
        pr_debug("preRoutingv6 IPv6 TCP/UDP packet: %pI6:%d -> %pI6:%d protocol: %d device: %s\n",
                sip6, ntohs(thd->source),
                dip6, ntohs(thd->dest),
                ip6h->nexthdr,
                in ? in->name: "null");
    } else if(ip6h->nexthdr == IPPROTO_UDP) {
        struct udphdr *udph = (struct udphdr *)((char *)ip6h + sizeof(struct ipv6hdr));
        pr_debug("preRoutingv6 IPv6 TCP/UDP packet: %pI6:%d -> %pI6:%d protocol: %d device: %s\n",
                sip6, ntohs(udph->source),
                dip6, ntohs(udph->dest),
                ip6h->nexthdr,
                in ? in->name: "null");
    }

    cur = mytime();
    totalPackets++;

    // 跳过loopback和多播地址
    if(ipv6_addr_loopback_compat(sip6) || ipv6_addr_is_multicast_compat(sip6) ||
       ipv6_addr_loopback_compat(dip6) || ipv6_addr_is_multicast_compat(dip6)) {
        return NF_ACCEPT;
    }

    // 跳过网关流量
    if(GATEWAY_V6(sip6) || GATEWAY_V6(dip6)) {
        return NF_ACCEPT;
    }

    // 跳过本地到本地的流量
    if(LOCAL_V6(sip6) && LOCAL_V6(dip6)) {
        return NF_ACCEPT;
    }

    // 检查IPv6白名单
    if(check_ipv6_whitelist(sip6, dip6)) {
        return NF_ACCEPT;
    }

    // 只对WiFi设备进行MAC认证检查
    if(!is_wifi_device) {
        return NF_ACCEPT;
    }

    // 提取MAC地址
    cli = extract_mac_from_wifi_header_v6(skb, sip6, cur);
    if(!cli) {
        return NF_DROP;
    }

    // 检查MAC认证状态
    CLIENT_LIST *auth_client = inAuthList(cli);
    if(!auth_client) {

        // 根据协议类型进行特殊处理
        if(ip6h->nexthdr == IPPROTO_UDP) {
            struct udphdr *udph = (struct udphdr *)((char *)ip6h + sizeof(struct ipv6hdr));

#ifdef CONFIG_DNS_REDIRECT
            // DNS流量重定向到captive portal
            if(ntohs(udph->dest) == 53) {
                char ipv6_str[64];
                snprintf(ipv6_str, sizeof(ipv6_str), "%pI6", sip6);

                char ipv6_mac_entry[256];
                snprintf(ipv6_mac_entry, sizeof(ipv6_mac_entry),
                        "%s %02x:%02x:%02x:%02x:%02x:%02x\n",
                        ipv6_str,
                        cli->client.mac.mac_char.mac[0], cli->client.mac.mac_char.mac[1],
                        cli->client.mac.mac_char.mac[2], cli->client.mac.mac_char.mac[3],
                        cli->client.mac.mac_char.mac[4], cli->client.mac.mac_char.mac[5]);

                // 保存IPv6-MAC映射
                save_ipv6_mac_mapping(ipv6_mac_entry);

                // 重定向DNS请求到网关的9053端口
                memcpy(&ip6h->daddr, &localGatewayv6, sizeof(struct in6_addr));
                udph->dest = htons(9053);

                // 重新计算UDP校验和
                udph->check = 0;
                udph->check = csum_ipv6_magic(sip6, &ip6h->daddr, ntohs(udph->len), IPPROTO_UDP,
                                            csum_partial(udph, ntohs(udph->len), 0));

                kfree(cli);
                return NF_ACCEPT; // 允许重定向的数据包通过
            }
#endif
        }

        // 其他未认证流量一律阻断
        kfree(cli);
        return NF_DROP;
    }

    // // MAC已认证，处理HTTP重定向（仅TCP）
    // if(ip6h->nexthdr == IPPROTO_TCP && checkNew(cli)) {
    //     struct tcphdr *thd = (struct tcphdr *)((char *)ip6h + sizeof(struct ipv6hdr));

    //     if(LOCAL_V6(sip6) && !GATEWAY_V6(sip6) && !LOCAL_V6(dip6)) {
    //         // 检查是否为HTTP/HTTPS流量且不在白名单中
    //         if(!check_ipv6_whitelist(sip6, dip6) &&
    //            (ntohs(thd->dest) == DEFAULTHTTPPORT ||
    //             ntohs(thd->dest) == HTTPS_PPORT ||
    //             ntohs(thd->dest) == 8080)) {

    //             printk("IPv6 HTTP(S) redirect: %pI6:%d -> captive portal (not implemented)\n",
    //                    sip6, ntohs(thd->source));
    //             // TODO: 实现IPv6 HTTP重定向逻辑
    //         }
    //     }
    // }

    kfree(cli);
    return NF_ACCEPT;
}

static unsigned int postRoutingv6(
    unsigned int hooknum,
    struct sk_buff * skb,
    const struct net_device *in,
    const struct net_device *out,
    int (*okfn) (struct sk_buff *))
{
    struct in6_addr *sip6, *dip6;

    if(!skb) {
        return NF_ACCEPT;
    }

    // 只处理 WiFi 设备的出站流量
    if(!out || 0 != strcmp(out->name, WLANDEVICE)) {
        return NF_ACCEPT;
    }

    struct ipv6hdr *ip6h = ipv6_hdr_compat(skb);
    if(!ip6h) {
        return NF_ACCEPT;
    }

    // 只处理 TCP 和 UDP 协议
    if(ip6h->nexthdr != IPPROTO_TCP && ip6h->nexthdr != IPPROTO_UDP) {
        return NF_ACCEPT;
    }

    sip6 = &ip6h->saddr;
    dip6 = &ip6h->daddr;

    // Add detailed debugging for IPv6 traffic
    if(ip6h->nexthdr == IPPROTO_TCP) {
        struct tcphdr *thd = (struct tcphdr *)((char *)ip6h + sizeof(struct ipv6hdr));
        pr_debug("postRoutingv6 IPv6 TCP/UDP packet: %pI6:%d -> %pI6:%d protocol: %d device: %s\n",
                sip6, ntohs(thd->source),
                dip6, ntohs(thd->dest),
                ip6h->nexthdr,
                out ? out->name: "null");
    } else if(ip6h->nexthdr == IPPROTO_UDP) {
        struct udphdr *udph = (struct udphdr *)((char *)ip6h + sizeof(struct ipv6hdr));
        pr_debug("postRoutingv6 IPv6 TCP/UDP packet: %pI6:%d -> %pI6:%d protocol: %d device: %s\n",
                sip6, ntohs(udph->source),
                dip6, ntohs(udph->dest),
                ip6h->nexthdr,
                out ? out->name: "null");
    }

    // 允许网关响应流量 - 修复UDP处理问题
    if(GATEWAY_V6(sip6)) {
        if(ip6h->nexthdr == IPPROTO_TCP) {
            // struct tcphdr *thd = (struct tcphdr *)((char *)ip6h + sizeof(struct ipv6hdr));
            // if(thd->source == htons((unsigned short)LOCALGATEWAYPORT)) {
            //     printk("IPv6 Gateway TCP response: %pI6:%d -> %pI6:%d\n",
            //            sip6, ntohs(thd->source), dip6, ntohs(thd->dest));
            // }
        } else if(ip6h->nexthdr == IPPROTO_UDP) {
            struct udphdr *udph = (struct udphdr *)((char *)ip6h + sizeof(struct ipv6hdr));
            if(udph->source == htons(9053)) {  // DNS重定向响应端口
                pr_debug("IPv6 Gateway UDP response (DNS redirect): %pI6:%d -> %pI6:%d\n",
                       sip6, ntohs(udph->source), dip6, ntohs(udph->dest));
            }
        }
        return NF_ACCEPT;
    }

    // 检查本地客户端的出站流量认证状态
    if(LOCAL_V6(sip6)) {
        int *mh = (int*)skb_mac_header(skb);
        struct ieee80211s_hdr *whd = (struct ieee80211s_hdr *)mh;

        if(!mh || !whd) {
            pr_debug("IPv6 POST: Invalid WiFi header for outgoing traffic, DROP\n");
            return NF_DROP;
        }

        CLIENT_LIST *cli = kmalloc(sizeof(CLIENT_LIST), GFP_ATOMIC);
        if(!cli) {
            pr_debug("IPv6 POST: Memory allocation failed, DROP\n");
            return NF_DROP;
        }

        // 提取源 MAC 地址（WiFi 客户端）
        memcpy(cli->client.mac.mac_char.mac, whd->eaddr2, sizeof(cli->client.mac));
        cli->client.ip.int_ip = sip6->s6_addr32[3];
        cli->client.time = mytime();
        cli->prev = NULL;
        cli->next = NULL;

        // 检查 MAC 是否已认证
        CLIENT_LIST *auth_client = inAuthList(cli);
        if(!auth_client) {
            // MAC 未认证，拒绝出站 IPv6 流量
            pr_debug("IPv6 POST: Unauthenticated MAC %02x:%02x:%02x:%02x:%02x:%02x, DROP\n",
                   cli->client.mac.mac_char.mac[0], cli->client.mac.mac_char.mac[1],
                   cli->client.mac.mac_char.mac[2], cli->client.mac.mac_char.mac[3],
                   cli->client.mac.mac_char.mac[4], cli->client.mac.mac_char.mac[5]);
            kfree(cli);
            return NF_DROP;
        }

        // MAC 已认证，允许出站流量
        pr_debug("IPv6 POST: Authenticated MAC %02x:%02x:%02x:%02x:%02x:%02x, ACCEPT\n",
               cli->client.mac.mac_char.mac[0], cli->client.mac.mac_char.mac[1],
               cli->client.mac.mac_char.mac[2], cli->client.mac.mac_char.mac[3],
               cli->client.mac.mac_char.mac[4], cli->client.mac.mac_char.mac[5]);
        kfree(cli);
        return NF_ACCEPT;
    }

    // 其他情况默认允许
    return NF_ACCEPT;
}
#endif

static unsigned int postRouting(
    unsigned int hooknum,
    struct sk_buff * skb,
    const struct net_device *in,
    const struct net_device *out,
    int (*okfn) (struct sk_buff *))
{
    __be32 sip,dip;

    if(skb) {
        struct iphdr *iph;
        struct tcphdr *thd;
        struct sk_buff *sb = NULL;
        sb = skb;

        // Remove excessive debug message
        // printk("salvikie postRouting begin ===> \n");
	 
        if(out) {
            // let all packets to other place go through ~
            if(0 != strcmp(out->name,WLANDEVICE)) {
                return NF_ACCEPT;
            }
        }

        iph  = ip_hdr(sb);
        if(!iph) {
            return NF_ACCEPT;
        }

        sip = iph->saddr;
        dip = iph->daddr;

        if(iph->protocol != IPPROTO_TCP)  // set all icmp packets un-filtered
            return NF_ACCEPT;

        if(GATEWAY(sip)) {
            // Improve debugging for gateway traffic
            if(iph->protocol == IPPROTO_TCP) {
                thd = (struct tcphdr *)((int *) iph + iph->ihl);
                
                if(thd->source == htons((unsigned short)LOCALGATEWAYPORT)) {
                    sip = findIp(thd->dest);
                    pr_debug("Gateway response: port %d -> IP %d.%d.%d.%d\n", 
                           ntohs(thd->dest), NIPQUAD(sip));
                    
                    if(sip != 0) {
                        pr_debug("Rewriting response: src %d.%d.%d.%d:%d -> %d.%d.%d.%d:%d\n", 
                               NIPQUAD(iph->saddr), ntohs(thd->source),
                               NIPQUAD(sip), DEFAULTHTTPPORT);
                        
                        iph->saddr = sip;
                        thd->source = htons((unsigned short)DEFAULTHTTPPORT);
                        
                        // Recalculate checksums
                        calcIphChecksum(iph);
                        calcThdChecksum(iph,thd);
                        sb->ip_summed = CHECKSUM_NONE;
                    }
                }
            }
            return NF_ACCEPT;
        }
    }

    // Remove excessive debug message
    // printk("salvikie postRouting end ===> \n");
	
    return NF_ACCEPT;
}


static unsigned int localIn(
    unsigned int hooknum,
    struct sk_buff * skb,
    const struct net_device *in,
    const struct net_device *out,
    int (*okfn) (struct sk_buff *))
{
    if(skb) {
        struct iphdr *iph;
        int i,k;
        unsigned short *j;
//   CLIENT_LIST *cli = NULL;
#ifdef DEBUG_NF
        struct tcphdr *thd;
#endif
        struct sk_buff *sb = NULL;
        sb = skb;

        iph  = ip_hdr(sb);
        if(!iph) {
            printk("IP Header is null\n");
            return NF_ACCEPT;
        }

        if(iph->protocol == IPPROTO_TCP) {
            thd = (struct tcphdr *)((int *) iph + iph->ihl);
            printk("tcp header source %d dest %d\n",thd->source, thd->dest);
            if(thd->dest == htons((unsigned short)LOCALGATEWAYPORT)) {
                j = (unsigned short *)iph;
                k=0;
                printk("\nIPH<");
                for(i = 0; i<iph->ihl * 2; i++) {
                    printk("%04x ",j[i]);
                    k+=j[i];
                }
                printk(">%x<\n",k);

                j = (unsigned short *)thd;
                k=0;
                printk("\nTHD<");
                for(i = 0; i<sizeof(struct tcphdr)/2; i++) {
                    printk("%04x ",j[i]);
                    k += j[i];
                }
                printk(">%x<\n",k);
            }
        }
    }
    return NF_ACCEPT;
}


struct nf_hook_ops preroute_ops = {
    .list =  {NULL,NULL},
    .hook = preRouting,
    .pf = PF_INET,
    .hooknum = NF_INET_PRE_ROUTING,
    .priority = NF_IP_PRI_FILTER+2
};

struct nf_hook_ops postroute_ops = {
    .list =  {NULL,NULL},
    .hook = postRouting,
    .pf = PF_INET,
    .hooknum = NF_INET_POST_ROUTING,
    .priority = NF_IP_PRI_FILTER+2
};


#if 0
// perhaps it is better to do the speed limit in forward~~~

struct nf_hook_ops forward_ops = {
    .list =  {NULL,NULL},
    .hook = postRouting,
    .pf = PF_INET,
    .hooknum = NF_INET_POST_ROUTING,
    .priority = NF_IP_PRI_FILTER+2
};
#endif

struct nf_hook_ops localin_ops = {
    .list =  {NULL,NULL},
    .hook = localIn,
    .pf = PF_INET,
    .hooknum = NF_INET_LOCAL_IN,
    .priority = NF_IP_PRI_FILTER+2
};

#ifdef CONFIG_IPV6_PORTAL
struct nf_hook_ops preroute_ops_v6 = {
    .list =  {NULL,NULL},
    .hook = preRoutingv6,
    .pf = PF_INET6,
    .hooknum = NF_INET_PRE_ROUTING,
    .priority = NF_IP_PRI_FILTER+2
};

struct nf_hook_ops postroute_ops_v6 = {
    .list =  {NULL,NULL},
    .hook = postRoutingv6,
    .pf = PF_INET6,
    .hooknum = NF_INET_POST_ROUTING,
    .priority = NF_IP_PRI_FILTER+2
};
#endif


void register_cjportal_hook(void)
{
    if(0 == registered) {
        nf_register_hook(&preroute_ops);
        nf_register_hook(&postroute_ops);
//        nf_register_hook(&localin_ops);

#ifdef CONFIG_IPV6_PORTAL
        nf_register_hook(&preroute_ops_v6);
        nf_register_hook(&postroute_ops_v6);
        printk("registered cjportal IPv6 hooks!\n");
#endif
        registered = 1;
    }
    printk("registered cjportal hook!\n");
}

EXPORT_SYMBOL(register_cjportal_hook);

void unregister_cjportal_hook(void)
{
    if(registered) {
        nf_unregister_hook(&preroute_ops);
        nf_unregister_hook(&postroute_ops);
//        nf_unregister_hook(&localin_ops);

#ifdef CONFIG_IPV6_PORTAL
        nf_unregister_hook(&preroute_ops_v6);
        nf_unregister_hook(&postroute_ops_v6);
        printk("unregistered cjportal IPv6 hooks!\n");
#endif
        registered = 0;
    }
    printk("unregistered cjportal hook!\n");
}
EXPORT_SYMBOL(unregister_cjportal_hook);

#ifdef CONFIG_IPV6_PORTAL
void init_ipv6_whitelist(void)
{
    // Initialize IPv6 whitelist - add some example addresses
    // These should be configured via procfs or module parameters in production
    whitelist_v6_count = 0;

    // Example: Add some common IPv6 addresses to whitelist
    // You can add more addresses as needed

    // // Example: Add Google's IPv6 DNS server (2001:4860:4860::8888)
    // if (whitelist_v6_count < MAX_WHITELIST_IP) {
    //     whitelist_v6[whitelist_v6_count].s6_addr32[0] = htonl(0x20014860);
    //     whitelist_v6[whitelist_v6_count].s6_addr32[1] = htonl(0x48600000);
    //     whitelist_v6[whitelist_v6_count].s6_addr32[2] = htonl(0x00000000);
    //     whitelist_v6[whitelist_v6_count].s6_addr32[3] = htonl(0x00008888);
    //     whitelist_v6_count++;
    // }

    // // Example: Add Cloudflare's IPv6 DNS server (2606:4700:4700::1111)
    // if (whitelist_v6_count < MAX_WHITELIST_IP) {
    //     whitelist_v6[whitelist_v6_count].s6_addr32[0] = htonl(0x26064700);
    //     whitelist_v6[whitelist_v6_count].s6_addr32[1] = htonl(0x47000000);
    //     whitelist_v6[whitelist_v6_count].s6_addr32[2] = htonl(0x00000000);
    //     whitelist_v6[whitelist_v6_count].s6_addr32[3] = htonl(0x00001111);
    //     whitelist_v6_count++;
    // }

    printk("IPv6 whitelist initialized with %d entries\n", whitelist_v6_count);
}

// Function to add IPv6 address to whitelist (simplified for Linux 3.4.x)
int add_ipv6_whitelist_raw(u32 addr0, u32 addr1, u32 addr2, u32 addr3)
{
    if (whitelist_v6_count >= MAX_WHITELIST_IP) {
        printk("IPv6 whitelist is full\n");
        return -1;
    }

    whitelist_v6[whitelist_v6_count].s6_addr32[0] = htonl(addr0);
    whitelist_v6[whitelist_v6_count].s6_addr32[1] = htonl(addr1);
    whitelist_v6[whitelist_v6_count].s6_addr32[2] = htonl(addr2);
    whitelist_v6[whitelist_v6_count].s6_addr32[3] = htonl(addr3);
    whitelist_v6_count++;

    printk("Added IPv6 address to whitelist (raw format)\n");
    return 0;
}

// Simplified function for string input (basic implementation)
int add_ipv6_whitelist(const char *ipv6_str)
{
    // For Linux 3.4.x, we provide a simplified implementation
    // In production, you would implement a proper IPv6 string parser
    // or use the raw function above with pre-calculated values
    printk("IPv6 string parsing not implemented for Linux 3.4.x: %s\n", ipv6_str);
    printk("Use add_ipv6_whitelist_raw() instead\n");
    return -1;
}

// Function to clear IPv6 whitelist
void clear_ipv6_whitelist(void)
{
    whitelist_v6_count = 0;
    printk("IPv6 whitelist cleared\n");
}

EXPORT_SYMBOL(add_ipv6_whitelist);
EXPORT_SYMBOL(add_ipv6_whitelist_raw);
EXPORT_SYMBOL(clear_ipv6_whitelist);
#endif

// 导出 IPv4 白名单管理函数
EXPORT_SYMBOL(add_ipv4_whitelist);
EXPORT_SYMBOL(clear_ipv4_whitelist);
EXPORT_SYMBOL(whitelist_ipv4);
EXPORT_SYMBOL(whitelist_ipv4_count);

// 导出高性能优化函数
EXPORT_SYMBOL(add_ipv4_whitelist_fast);
EXPORT_SYMBOL(clear_ipv4_whitelist_fast);
EXPORT_SYMBOL(is_ipv4_whitelisted_fast);

// 模块参数
module_param(hw_checksum_enabled, int, 0644);
MODULE_PARM_DESC(hw_checksum_enabled, "Enable hardware checksum acceleration (default: 1)");

module_param(cpu_cache_prefetch_enabled, int, 0644);
MODULE_PARM_DESC(cpu_cache_prefetch_enabled, "Enable CPU cache prefetching (default: 1)");

int cjportal_init(void)
{
    int i;

    printk("Hello, this is the init of captive portal with advanced optimizations\n");

    // 初始化基础功能
    clientListInit();
    init_procfs_cjPortal();

    // 初始化性能统计
    atomic64_set(&perf_stats.total_packets, 0);
    atomic64_set(&perf_stats.redirected_packets, 0);
    atomic64_set(&perf_stats.whitelisted_packets, 0);
    atomic64_set(&perf_stats.batch_processed, 0);

    // 初始化无锁哈希表
    for (i = 0; i < WHITELIST_HASH_SIZE; i++) {
        INIT_HLIST_HEAD(&whitelist_hash[i]);
    }

    // 初始化批量处理
    init_batch_processing();

    // 初始化白名单（兼容性）
    init_ipv4_whitelist();

    // 添加一些默认白名单到快速哈希表
    add_ipv4_whitelist_fast("***********");      // 电信认证域名的IP
    add_ipv4_whitelist_fast("***************");  // wuxing
    add_ipv4_whitelist_fast("**************");   // wuxing
    add_ipv4_whitelist_fast("*************");    // wuxing
    add_ipv4_whitelist_fast("*************");    // wuxing
    add_ipv4_whitelist_fast("**************");   // wuxing

#ifdef CONFIG_IPV6_PORTAL
    init_ipv6_whitelist();
    printk("IPv6 portal support enabled\n");
#endif

    register_cjportal_hook();

    printk("Advanced optimizations enabled:\n");
    printk("  - Lockless hash table whitelist\n");
    printk("  - Batch packet processing (size=%d, timeout=%dms)\n", BATCH_SIZE, BATCH_TIMEOUT_MS);
    printk("  - Hardware checksum acceleration: %s\n", hw_checksum_enabled ? "enabled" : "disabled");
    printk("  - CPU cache prefetching: %s\n", cpu_cache_prefetch_enabled ? "enabled" : "disabled");

    return 0;
}

void cjportal_exit(void)
{
    printk("Captive portal exit - cleaning up advanced optimizations\n");

    // 清理批量处理
    cleanup_batch_processing();

    // 清理无锁哈希表
    clear_ipv4_whitelist_fast();

    // 输出最终统计信息
    printk("Final performance statistics:\n");
    printk("  Total packets processed: %lld\n", atomic64_read(&perf_stats.total_packets));
    printk("  Redirected packets: %lld\n", atomic64_read(&perf_stats.redirected_packets));
    printk("  Whitelisted packets: %lld\n", atomic64_read(&perf_stats.whitelisted_packets));
    printk("  Batch processed: %lld\n", atomic64_read(&perf_stats.batch_processed));

    // 清理基础功能
    cleanup_procfs_cjportal();
    unregister_cjportal_hook();

    printk("Captive portal cleanup completed\n");
}

module_init(cjportal_init);
module_exit(cjportal_exit);

MODULE_AUTHOR("Qirui");
MODULE_DESCRIPTION("Captive Portal");
MODULE_LICENSE("GPL");
