#!/bin/bash

# Captive Portal 高性能优化测试脚本
# 用于测试批量包处理、无锁数据结构和硬件加速特性

echo "=== Captive Portal Performance Test Suite ==="

# 检查模块是否加载
check_module() {
    if ! lsmod | grep -q "speedlimit"; then
        echo "Error: speedlimit module not loaded"
        exit 1
    fi
    echo "✓ speedlimit module is loaded"
}

# 检查硬件特性
check_hardware_features() {
    echo "=== Hardware Features Check ==="
    
    # 检查网卡硬件特性
    for dev in $(ls /sys/class/net/ | grep -E "(eth|wlan|br)"); do
        if [ -f "/sys/class/net/$dev/features" ]; then
            echo "Device: $dev"
            features=$(cat /sys/class/net/$dev/features)
            
            # 检查硬件校验和支持
            if [ $((features & 0x1)) -ne 0 ]; then
                echo "  ✓ Hardware checksum supported"
            else
                echo "  ✗ Hardware checksum not supported"
            fi
            
            # 检查scatter-gather支持
            if [ $((features & 0x2)) -ne 0 ]; then
                echo "  ✓ Scatter-gather supported"
            else
                echo "  ✗ Scatter-gather not supported"
            fi
            
            # 检查TSO支持
            if [ $((features & 0x4)) -ne 0 ]; then
                echo "  ✓ TCP Segmentation Offload supported"
            else
                echo "  ✗ TCP Segmentation Offload not supported"
            fi
        fi
    done
}

# 性能统计监控
monitor_performance() {
    echo "=== Performance Statistics ==="
    
    # 从dmesg获取统计信息
    dmesg | tail -20 | grep -E "(total packets|Batch processed|IPv4 WHITELIST)"
    
    # 检查CPU使用率
    echo "CPU Usage:"
    top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1
    
    # 检查内存使用
    echo "Memory Usage:"
    free -m | grep "Mem:" | awk '{printf "Used: %dMB (%.1f%%)\n", $3, $3*100/$2}'
    
    # 检查网络统计
    echo "Network Statistics:"
    cat /proc/net/dev | grep -E "(br0|wlan0|eth0)" | while read line; do
        dev=$(echo $line | cut -d: -f1 | tr -d ' ')
        rx_packets=$(echo $line | awk '{print $2}')
        tx_packets=$(echo $line | awk '{print $10}')
        echo "  $dev: RX=$rx_packets TX=$tx_packets"
    done
}

# 白名单性能测试
test_whitelist_performance() {
    echo "=== Whitelist Performance Test ==="
    
    # 测试添加大量白名单条目
    echo "Adding 100 whitelist entries..."
    start_time=$(date +%s.%N)
    
    for i in $(seq 1 100); do
        ip="192.168.$((i/256)).$((i%256))"
        echo "add_ipv4_whitelist_fast $ip" > /proc/cjportal_config 2>/dev/null || true
    done
    
    end_time=$(date +%s.%N)
    duration=$(echo "$end_time - $start_time" | bc)
    echo "Time to add 100 entries: ${duration}s"
    
    # 测试查找性能
    echo "Testing lookup performance..."
    start_time=$(date +%s.%N)
    
    # 生成测试流量（如果iperf3可用）
    if command -v iperf3 >/dev/null 2>&1; then
        echo "Generating test traffic with iperf3..."
        timeout 10s iperf3 -c *********** -t 10 -P 4 >/dev/null 2>&1 || true
    fi
    
    end_time=$(date +%s.%N)
    duration=$(echo "$end_time - $start_time" | bc)
    echo "Lookup test duration: ${duration}s"
}

# 批量处理测试
test_batch_processing() {
    echo "=== Batch Processing Test ==="
    
    # 检查批量处理参数
    if [ -f "/sys/module/speedlimit/parameters/batch_size" ]; then
        batch_size=$(cat /sys/module/speedlimit/parameters/batch_size)
        echo "Batch size: $batch_size"
    fi
    
    # 生成高频流量测试批量处理
    echo "Generating high-frequency traffic..."
    
    # 使用ping生成测试流量
    for i in $(seq 1 10); do
        ping -c 100 -i 0.01 *********** >/dev/null 2>&1 &
    done
    
    sleep 5
    killall ping 2>/dev/null || true
    
    echo "Batch processing test completed"
}

# 硬件加速测试
test_hardware_acceleration() {
    echo "=== Hardware Acceleration Test ==="
    
    # 检查硬件校验和参数
    if [ -f "/sys/module/speedlimit/parameters/hw_checksum_enabled" ]; then
        hw_checksum=$(cat /sys/module/speedlimit/parameters/hw_checksum_enabled)
        echo "Hardware checksum: $([ $hw_checksum -eq 1 ] && echo 'enabled' || echo 'disabled')"
    fi
    
    # 检查CPU缓存预取参数
    if [ -f "/sys/module/speedlimit/parameters/cpu_cache_prefetch_enabled" ]; then
        prefetch=$(cat /sys/module/speedlimit/parameters/cpu_cache_prefetch_enabled)
        echo "CPU cache prefetch: $([ $prefetch -eq 1 ] && echo 'enabled' || echo 'disabled')"
    fi
    
    # 测试大数据传输性能
    echo "Testing large data transfer performance..."
    
    if command -v dd >/dev/null 2>&1; then
        # 生成大文件传输测试
        start_time=$(date +%s.%N)
        dd if=/dev/zero of=/tmp/test_file bs=1M count=100 2>/dev/null
        nc -l 8080 < /tmp/test_file &
        nc_pid=$!
        sleep 1
        timeout 10s nc 127.0.0.1 8080 > /dev/null 2>&1 || true
        kill $nc_pid 2>/dev/null || true
        rm -f /tmp/test_file
        end_time=$(date +%s.%N)
        duration=$(echo "$end_time - $start_time" | bc)
        echo "Large transfer test duration: ${duration}s"
    fi
}

# 压力测试
stress_test() {
    echo "=== Stress Test ==="
    
    echo "Starting stress test for 30 seconds..."
    start_time=$(date +%s)
    
    # 并发连接测试
    for i in $(seq 1 20); do
        (
            for j in $(seq 1 50); do
                curl -s --connect-timeout 1 http://*********** >/dev/null 2>&1 || true
                sleep 0.1
            done
        ) &
    done
    
    # 等待30秒
    sleep 30
    
    # 终止所有后台进程
    jobs -p | xargs -r kill 2>/dev/null || true
    
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    echo "Stress test completed in ${duration}s"
}

# 生成性能报告
generate_report() {
    echo "=== Performance Report ==="
    
    report_file="/tmp/captive_portal_performance_report.txt"
    
    {
        echo "Captive Portal Performance Report"
        echo "Generated: $(date)"
        echo "=================================="
        echo
        
        echo "System Information:"
        uname -a
        echo "CPU: $(cat /proc/cpuinfo | grep 'model name' | head -1 | cut -d: -f2 | xargs)"
        echo "Memory: $(free -h | grep 'Mem:' | awk '{print $2}')"
        echo
        
        echo "Module Parameters:"
        if [ -d "/sys/module/speedlimit/parameters" ]; then
            for param in /sys/module/speedlimit/parameters/*; do
                if [ -f "$param" ]; then
                    param_name=$(basename "$param")
                    param_value=$(cat "$param")
                    echo "  $param_name: $param_value"
                fi
            done
        fi
        echo
        
        echo "Performance Statistics:"
        dmesg | grep -E "(total packets|Batch processed|IPv4 WHITELIST)" | tail -10
        echo
        
        echo "Network Interface Features:"
        for dev in $(ls /sys/class/net/ | grep -E "(eth|wlan|br)"); do
            if [ -f "/sys/class/net/$dev/features" ]; then
                echo "  $dev: $(cat /sys/class/net/$dev/features)"
            fi
        done
        
    } > "$report_file"
    
    echo "Performance report saved to: $report_file"
}

# 主函数
main() {
    check_module
    check_hardware_features
    monitor_performance
    test_whitelist_performance
    test_batch_processing
    test_hardware_acceleration
    stress_test
    generate_report
    
    echo
    echo "=== Performance Test Completed ==="
    echo "Check dmesg for detailed kernel logs"
    echo "Performance report available at /tmp/captive_portal_performance_report.txt"
}

# 运行测试
main "$@"
