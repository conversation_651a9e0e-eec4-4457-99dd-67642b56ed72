# =============================================================================
# 优化的 libcurl Makefile
# =============================================================================
include $(COMMON_BASE_MK)

NAME=libcurl
SRC_DIR=curl-7.86.0
INSTALL_DIR=$(LIB_DIR)/$(NAME)/install
BUILD_DIR=build
GEN_MAKEFILE=$(BUILD_DIR)/Makefile

# 基础配置
CONFIG_OPTS = --target=arm-linux --host=arm-linux
CONFIG_OPTS += --enable-static
ifeq ($(CONFIG_LIBCURL_SHARE_LIB), y)
CONFIG_OPTS += --enable-shared
else
CONFIG_OPTS += --disable-shared
endif

# 优化编译选项 - 减小体积
CONFIG_OPTS += --enable-optimize
CONFIG_OPTS += --disable-debug
CONFIG_OPTS += --disable-curldebug
CONFIG_OPTS += --disable-werror
CONFIG_OPTS += --disable-warnings

# DNS解析优化 - 使用更轻量的解析器
CONFIG_OPTS += --enable-threaded-resolver
CONFIG_OPTS += --disable-ares

ifeq ($(ENABLE_QRZL_APP),yes)
# wolfSSL配置
WOLFSSL_INSTALL_DIR = $(LIB_DIR)/libwolfssl/install/

ifeq ($(CONFIG_LIBCURL_SHARE_LIB), y)
# 修复编译错误的关键：添加编译器标志忽略警告和错误
export CPPFLAGS = -I$(WOLFSSL_INSTALL_DIR)/include -Os -ffunction-sections -fdata-sections
export LDFLAGS = -L$(WOLFSSL_INSTALL_DIR)/lib -Wl,-rpath-link=$(WOLFSSL_INSTALL_DIR)/lib -Wl,--gc-sections -s
export CFLAGS = -Wno-error=implicit-function-declaration -Wno-unused-function -Wno-error
endif

CONFIG_OPTS += --with-wolfssl=$(WOLFSSL_INSTALL_DIR)

# 只启用必要的协议和功能
CONFIG_OPTS += --enable-http --enable-https
CONFIG_OPTS += --disable-ipv6

# 禁用所有不必要的协议
CONFIG_OPTS += --disable-ftp --disable-file --disable-ldap --disable-ldaps
CONFIG_OPTS += --disable-rtsp --disable-proxy --disable-dict --disable-telnet
CONFIG_OPTS += --disable-tftp --disable-pop3 --disable-imap --disable-smb
CONFIG_OPTS += --disable-smtp --disable-gopher --disable-mqtt
CONFIG_OPTS += --disable-doh --disable-netrc --disable-progress-meter

# 禁用不必要的认证方式（保留基本的SSL/TLS）
CONFIG_OPTS += --disable-ntlm --disable-ntlm-wb --disable-digest
CONFIG_OPTS += --disable-negotiate --disable-kerberos --disable-spnego
CONFIG_OPTS += --disable-gsasl

# 禁用其他可选功能
CONFIG_OPTS += --disable-cookies --disable-alt-svc --disable-hsts
CONFIG_OPTS += --disable-manual --disable-libcurl-option
CONFIG_OPTS += --disable-sspi --disable-crypto-auth
CONFIG_OPTS += --disable-tls-srp --disable-unix-sockets
CONFIG_OPTS += --disable-socketpair --disable-verbose

# 禁用外部依赖库
CONFIG_OPTS += --without-zlib --without-brotli --without-zstd
CONFIG_OPTS += --without-libidn2 --without-libpsl --without-nghttp2
CONFIG_OPTS += --without-ngtcp2 --without-nghttp3 --without-quiche
CONFIG_OPTS += --without-libssh2 --without-librtmp
CONFIG_OPTS += --without-winidn --without-libidn
CONFIG_OPTS += --without-gnutls --without-mbedtls --without-nss

else
# 非QRZL_APP模式 - 最小HTTP功能
CONFIG_OPTS += --enable-http --disable-https --enable-ipv6
CONFIG_OPTS += --disable-ftp --disable-file --disable-ldap --disable-ldaps
CONFIG_OPTS += --disable-rtsp --disable-dict --disable-telnet --disable-tftp
CONFIG_OPTS += --disable-pop3 --disable-imap --disable-smb --disable-smtp
CONFIG_OPTS += --disable-gopher --disable-mqtt --disable-doh
CONFIG_OPTS += --without-ssl --without-zlib --without-libidn --without-librtmp
CONFIG_OPTS += --without-gnutls --without-nss --without-libssh2 --without-winidn
endif

all: $(GEN_MAKEFILE)
	make -C build all
	make -C build install
	# 只复制必要的头文件
	cp -v $(SRC_DIR)/lib/curl_md5.h  $(INSTALL_DIR)/include/curl/
	cp -v $(SRC_DIR)/lib/curl_hmac.h $(INSTALL_DIR)/include/curl/
	# 优化：strip符号表减小库文件
ifeq ($(CONFIG_LIBCURL_SHARE_LIB), y)
	arm-linux-strip --strip-unneeded $(INSTALL_DIR)/lib/*.so* 2>/dev/null || true
else
	arm-linux-strip --strip-debug $(INSTALL_DIR)/lib/*.a 2>/dev/null || true
endif

$(GEN_MAKEFILE):
	mkdir -p $(BUILD_DIR)
	cd $(BUILD_DIR); ../$(SRC_DIR)/configure --prefix=$(INSTALL_DIR) $(CONFIG_OPTS)

clean:
	-rm -fr $(BUILD_DIR) $(INSTALL_DIR)

romfs:
	cd $(INSTALL_DIR)/lib; \
	for i in *.so*; do \
		if [ -L $$i ]; then \
			$(ROMFSINST) -s `find $$i -printf %l` /lib/$$i; \
		elif [ -f $$i ]; then \
			$(ROMFSINST) /lib/$$i; \
		fi; \
	done
ifeq ($(ENABLE_QRZL_APP)-$(CONFIG_LIBCURL_SHARE_LIB),yes-y)
	cd $(WOLFSSL_INSTALL_DIR)/lib; \
	for i in *.so*; do \
		if [ -L $$i ]; then \
			$(ROMFSINST) -s `find $$i -printf %l` /lib/$$i; \
		elif [ -f $$i ]; then \
			$(ROMFSINST) /lib/$$i; \
		fi; \
	done
endif