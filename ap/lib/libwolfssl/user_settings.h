/* user_settings.h - wolfSSL 最小化配置用于 HTTPS 客户端 */

#ifndef WOLFSSL_USER_SETTINGS_H
#define WOLFSSL_USER_SETTINGS_H

/* 平台配置 */
#define WOLFSSL_GENERAL_ALIGNMENT   4
#define SIZEOF_LONG_LONG    8

/* 启用必需的加密功能 */
#define NO_DSA
#define NO_RC4
#define NO_MD4
#define NO_MD2
#define NO_DH
#define NO_PSK
#define NO_PWDBASED
#define NO_FILESYSTEM
#define NO_WRITEV

/* 启用现代加密算法 */
#define HAVE_AESGCM
#define HAVE_AES_CBC
#define WOLFSSL_AES_128
#define WOLFSSL_AES_256
#define HAVE_CHACHA
#define HAVE_POLY1305
#define HAVE_CURVE25519
#define HAVE_ED25519

/* TLS 协议版本 */
#define NO_OLD_TLS
#define WOLFSSL_TLS13
#define NO_SSL_v3
#define NO_TLS_v1
#define NO_TLS_v1_1

/* 优化设置 */
#define WOLFSSL_SMALL_STACK
#define NO_INLINE
#define NO_64BIT
#define SMALL_SESSION_CACHE
#define NO_SESSION_CACHE

/* 禁用不需要的功能 */
#define NO_CERTS
#define NO_CERT_GEN
#define NO_KEY_GEN
#define NO_ASN_TIME
#define NO_CODING
#define NO_SIG_WRAPPER
#define NO_HASH_WRAPPER

/* 网络相关 */
#define NO_WOLFSSL_SERVER
#define WOLFSSL_CLIENT_ONLY
#define SINGLE_THREADED

/* 内存优化 */
#define WOLFSSL_STATIC_MEMORY
#define WOLFSSL_MALLOC_CHECK

/* 错误处理优化 */
#define NO_ERROR_STRINGS
#define NO_WOLFSSL_MEMORY

/* 随机数生成 */
#define NO_DEV_RANDOM
#define CUSTOM_RAND_GENERATE_SEED

#endif /* WOLFSSL_USER_SETTINGS_H */