/* user_settings.h - wolfSSL 最小化配置用于 HTTPS 客户端 */
#ifndef WOLFSSL_USER_SETTINGS_H
#define WOLFSSL_USER_SETTINGS_H

/* 平台配置 */
#define WOLFSSL_GENERAL_ALIGNMENT   4
#define SIZEOF_LONG_LONG    8

/* 禁用不需要的加密算法和功能 */
#define NO_MD2
#define NO_MD4
#define NO_WRITEV
#define NO_DES3
#define NO_RC4
#define NO_RABBIT
#define NO_HC128
#define NO_IDEA
#define NO_CAMELLIA
#define NO_BLAKE2
#define NO_SHA3
#define NO_CHACHA
#define NO_POLY1305
#define NO_CURVE25519
#define NO_ED25519
#define NO_PKCS7
#define NO_PKCS12
#define NO_PSK
#define NO_SRP

/* AES配置 - 只启用必需的模式 */
#define HAVE_AES_CBC
#define HAVE_AESGCM
#define WOLFSSL_AES_128
#define WOLFSSL_AES_256

/* 椭圆曲线支持 - libcurl需要 */
#define HAVE_ECC
#define HAVE_SUPPORTED_CURVES
#define HAVE_TLS_EXTENSIONS

/* OpenSSL兼容性 - libcurl需要 */
#define OPENSSL_EXTRA
#define OPENSSL_ALL
#define WOLFSSL_CERT_GEN
#define WOLFSSL_KEY_GEN

/* TLS版本控制 - 只支持TLS 1.2和1.3 */
#define NO_SSL_v3
#define NO_TLS_v1
#define NO_TLS_v1_1

/* 内存和性能优化 */
#define SMALL_SESSION_CACHE
#define WOLFSSL_SMALL_STACK

/* 客户端专用配置 */
#define WOLFSSL_CLIENT_ONLY
#define SINGLE_THREADED

/* 随机数生成 - 使用系统默认 */
/* #define NO_DEV_RANDOM  // 保持注释以使用系统随机数 */

#endif /* WOLFSSL_USER_SETTINGS_H */
