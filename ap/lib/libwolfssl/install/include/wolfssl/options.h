/* wolfssl options.h
 * generated from configure options
 *
 * Copyright (C) 2006-2020 wolfSSL Inc.
 *
 * This file is part of wolfSSL. (formerly known as CyaSSL)
 *
 */

#ifndef WOLFSSL_OPTIONS_H
#define WOLFSSL_OPTIONS_H


#ifdef __cplusplus
extern "C" {
#endif

#undef  WOLFSSL_PUBLIC_MP
#define WOLFSSL_PUBLIC_MP

#undef  WOLFSSL_MULTICAST
#define WOLFSSL_MULTICAST

#undef  WOLFSSL_TLS13
#define WOLFSSL_TLS13

#undef  HAVE_TLS_EXTENSIONS
#define HAVE_TLS_EXTENSIONS

#undef  HAVE_SUPPORTED_CURVES
#define HAVE_SUPPORTED_CURVES

#undef  HAVE_FFDHE_2048
#define HAVE_FFDHE_2048

#undef  WOLFSSL_SEP
#define WOLFSSL_SEP

#undef  KEEP_PEER_CERT
#define KEEP_PEER_CERT

#undef  HAVE_EXT_CACHE
#define HAVE_EXT_CACHE

#undef  WOLFSSL_VERIFY_CB_ALL_CERTS
#define WOLFSSL_VERIFY_CB_ALL_CERTS

#undef  WOLFSSL_EXTRA_ALERTS
#define WOLFSSL_EXTRA_ALERTS

#undef  OPENSSL_EXTRA
#define OPENSSL_EXTRA

#undef  WOLFSSL_ALWAYS_VERIFY_CB
#define WOLFSSL_ALWAYS_VERIFY_CB

#undef  OPENSSL_ALL
#define OPENSSL_ALL

#undef  WOLFSSL_EITHER_SIDE
#define WOLFSSL_EITHER_SIDE

#undef  WC_RSA_NO_PADDING
#define WC_RSA_NO_PADDING

#undef  WC_RSA_PSS
#define WC_RSA_PSS

#undef  WOLFSSL_PSS_LONG_SALT
#define WOLFSSL_PSS_LONG_SALT

#undef  WOLFSSL_SEND_HRR_COOKIE
#define WOLFSSL_SEND_HRR_COOKIE

#undef  WOLFSSL_POST_HANDSHAKE_AUTH
#define WOLFSSL_POST_HANDSHAKE_AUTH

#ifndef WOLFSSL_OPTIONS_IGNORE_SYS
#undef  _POSIX_THREADS
#define _POSIX_THREADS
#endif

#undef  WOLFSSL_MULTI_ATTRIB
#define WOLFSSL_MULTI_ATTRIB

#undef  HAVE_THREAD_LS
#define HAVE_THREAD_LS

#undef  HAVE_CRL_IO
#define HAVE_CRL_IO

#undef  HAVE_IO_TIMEOUT
#define HAVE_IO_TIMEOUT

#undef  WOLFSSL_VALIDATE_ECC_IMPORT
#define WOLFSSL_VALIDATE_ECC_IMPORT

#undef  HAVE_FFDHE_2048
#define HAVE_FFDHE_2048

#undef  HAVE_FFDHE_3072
#define HAVE_FFDHE_3072

#undef  FP_MAX_BITS
#define FP_MAX_BITS 8192

#undef  HAVE_AES_DECRYPT
#define HAVE_AES_DECRYPT

#undef  HAVE_AES_ECB
#define HAVE_AES_ECB

#undef  WOLFSSL_ALT_NAMES
#define WOLFSSL_ALT_NAMES

#undef  WOLFSSL_DER_LOAD
#define WOLFSSL_DER_LOAD

#undef  KEEP_OUR_CERT
#define KEEP_OUR_CERT

#undef  KEEP_PEER_CERT
#define KEEP_PEER_CERT

#undef  WOLFSSL_VALIDATE_ECC_KEYGEN
#define WOLFSSL_VALIDATE_ECC_KEYGEN

#undef  WOLFSSL_LIBWEBSOCKETS
#define WOLFSSL_LIBWEBSOCKETS

#undef  HAVE_EX_DATA
#define HAVE_EX_DATA

#undef  OPENSSL_NO_EC
#define OPENSSL_NO_EC

#undef  WOLFSSL_OPENSSH
#define WOLFSSL_OPENSSH

#undef  HAVE_EX_DATA
#define HAVE_EX_DATA

#undef  WOLFSSL_BASE16
#define WOLFSSL_BASE16

#undef  TFM_TIMING_RESISTANT
#define TFM_TIMING_RESISTANT

#undef  ECC_TIMING_RESISTANT
#define ECC_TIMING_RESISTANT

#undef  WC_RSA_BLINDING
#define WC_RSA_BLINDING

#undef  FORTRESS
#define FORTRESS

#undef  WOLFSSL_ALWAYS_VERIFY_CB
#define WOLFSSL_ALWAYS_VERIFY_CB

#undef  WOLFSSL_AES_COUNTER
#define WOLFSSL_AES_COUNTER

#undef  WOLFSSL_AES_DIRECT
#define WOLFSSL_AES_DIRECT

#undef  WOLFSSL_DER_LOAD
#define WOLFSSL_DER_LOAD

#undef  WOLFSSL_KEY_GEN
#define WOLFSSL_KEY_GEN

#undef  PERSIST_SESSION_CACHE
#define PERSIST_SESSION_CACHE

#undef  PERSIST_CERT_CACHE
#define PERSIST_CERT_CACHE

#undef  ATOMIC_USER
#define ATOMIC_USER

#undef  HAVE_PK_CALLBACKS
#define HAVE_PK_CALLBACKS

#undef  WOLFSSL_AES_CBC_LENGTH_CHECKS
#define WOLFSSL_AES_CBC_LENGTH_CHECKS

#undef  HAVE_AESCCM
#define HAVE_AESCCM

#undef  WOLFSSL_AES_OFB
#define WOLFSSL_AES_OFB

#undef  WOLFSSL_AES_DIRECT
#define WOLFSSL_AES_DIRECT

#undef  WOLFSSL_AES_CFB
#define WOLFSSL_AES_CFB

#undef  WOLFSSL_USE_ALIGN
#define WOLFSSL_USE_ALIGN

#undef  HAVE_CAMELLIA
#define HAVE_CAMELLIA

#undef  WOLFSSL_MD2
#define WOLFSSL_MD2

#undef  HAVE_NULL_CIPHER
#define HAVE_NULL_CIPHER

#undef  WOLFSSL_RIPEMD
#define WOLFSSL_RIPEMD

#undef  HAVE_BLAKE2S
#define HAVE_BLAKE2S

#undef  WOLFSSL_SHA224
#define WOLFSSL_SHA224

#undef  WOLFSSL_SHA512
#define WOLFSSL_SHA512

#undef  WOLFSSL_SHA384
#define WOLFSSL_SHA384

#undef  SESSION_CERTS
#define SESSION_CERTS

#undef  WOLFSSL_KEY_GEN
#define WOLFSSL_KEY_GEN

#undef  WOLFSSL_CERT_GEN
#define WOLFSSL_CERT_GEN

#undef  WOLFSSL_CERT_REQ
#define WOLFSSL_CERT_REQ

#undef  WOLFSSL_CERT_EXT
#define WOLFSSL_CERT_EXT

#undef  HAVE_HKDF
#define HAVE_HKDF

#undef  HAVE_X963_KDF
#define HAVE_X963_KDF

#undef  HAVE_ECC
#define HAVE_ECC

#undef  TFM_ECC256
#define TFM_ECC256

#undef  ECC_SHAMIR
#define ECC_SHAMIR

#undef  WOLFSSL_CUSTOM_CURVES
#define WOLFSSL_CUSTOM_CURVES

#undef  HAVE_ECC_SECPR2
#define HAVE_ECC_SECPR2

#undef  HAVE_ECC_SECPR3
#define HAVE_ECC_SECPR3

#undef  HAVE_ECC_BRAINPOOL
#define HAVE_ECC_BRAINPOOL

#undef  HAVE_ECC_KOBLITZ
#define HAVE_ECC_KOBLITZ

#undef  HAVE_ECC_CDH
#define HAVE_ECC_CDH

#undef  HAVE_COMP_KEY
#define HAVE_COMP_KEY

#undef  HAVE_CURVE25519
#define HAVE_CURVE25519

#undef  HAVE_ED25519
#define HAVE_ED25519

#undef  HAVE_CURVE448
#define HAVE_CURVE448

#undef  HAVE_ED448
#define HAVE_ED448

#undef  FP_ECC
#define FP_ECC

#undef  HAVE_ECC_ENCRYPT
#define HAVE_ECC_ENCRYPT

#undef  WOLFCRYPT_HAVE_ECCSI
#define WOLFCRYPT_HAVE_ECCSI

#undef  WOLFSSL_PUBLIC_MP
#define WOLFSSL_PUBLIC_MP

#undef  WOLFCRYPT_HAVE_SAKKE
#define WOLFCRYPT_HAVE_SAKKE

#undef  NO_ERROR_STRINGS
#define NO_ERROR_STRINGS

#undef  NO_OLD_TLS
#define NO_OLD_TLS

#undef  WOLFSSL_QT
#define WOLFSSL_QT

#undef  SESSION_CERTS
#define SESSION_CERTS

#undef  OPENSSL_NO_SSL2
#define OPENSSL_NO_SSL2

#undef  WOLFSSL_KEY_GEN
#define WOLFSSL_KEY_GEN

#undef  HAVE_EX_DATA
#define HAVE_EX_DATA

#undef  WOLFSSL_CUSTOM_CURVES
#define WOLFSSL_CUSTOM_CURVES

#undef  HAVE_ECC_SECPR2
#define HAVE_ECC_SECPR2

#undef  HAVE_ECC_SECPR3
#define HAVE_ECC_SECPR3

#undef  HAVE_ECC_BRAINPOOL
#define HAVE_ECC_BRAINPOOL

#undef  HAVE_ECC_KOBLITZ
#define HAVE_ECC_KOBLITZ

#undef  WC_RSA_PSS
#define WC_RSA_PSS

#undef  HAVE_ANON
#define HAVE_ANON

#undef  WOLFSSL_BASE64_ENCODE
#define WOLFSSL_BASE64_ENCODE

#undef  HAVE_IDEA
#define HAVE_IDEA

#undef  WOLFSSL_CMAC
#define WOLFSSL_CMAC

#undef  WOLFSSL_AES_DIRECT
#define WOLFSSL_AES_DIRECT

#undef  WOLFSSL_AES_XTS
#define WOLFSSL_AES_XTS

#undef  WOLFSSL_AES_DIRECT
#define WOLFSSL_AES_DIRECT

#undef  HAVE_WEBSERVER
#define HAVE_WEBSERVER

#undef  HAVE_HC128
#define HAVE_HC128

#undef  HAVE_RABBIT
#define HAVE_RABBIT

#undef  WOLFSSL_SHA3
#define WOLFSSL_SHA3

#undef  WOLFSSL_SHAKE256
#define WOLFSSL_SHAKE256

#undef  HAVE_POLY1305
#define HAVE_POLY1305

#undef  HAVE_ONE_TIME_AUTH
#define HAVE_ONE_TIME_AUTH

#undef  HAVE_CHACHA
#define HAVE_CHACHA

#undef  HAVE_HASHDRBG
#define HAVE_HASHDRBG

#undef  HAVE_OCSP
#define HAVE_OCSP

#undef  HAVE_OPENSSL_CMD
#define HAVE_OPENSSL_CMD

#undef  HAVE_TLS_EXTENSIONS
#define HAVE_TLS_EXTENSIONS

#undef  HAVE_CERTIFICATE_STATUS_REQUEST
#define HAVE_CERTIFICATE_STATUS_REQUEST

#undef  HAVE_TLS_EXTENSIONS
#define HAVE_TLS_EXTENSIONS

#undef  HAVE_CERTIFICATE_STATUS_REQUEST_V2
#define HAVE_CERTIFICATE_STATUS_REQUEST_V2

#undef  HAVE_CRL
#define HAVE_CRL

#undef  HAVE_TLS_EXTENSIONS
#define HAVE_TLS_EXTENSIONS

#undef  HAVE_SNI
#define HAVE_SNI

#undef  HAVE_TLS_EXTENSIONS
#define HAVE_TLS_EXTENSIONS

#undef  HAVE_TRUNCATED_HMAC
#define HAVE_TRUNCATED_HMAC

#undef  HAVE_RENEGOTIATION_INDICATION
#define HAVE_RENEGOTIATION_INDICATION

#undef  HAVE_FALLBACK_SCSV
#define HAVE_FALLBACK_SCSV

#undef  HAVE_TLS_EXTENSIONS
#define HAVE_TLS_EXTENSIONS

#undef  HAVE_SUPPORTED_CURVES
#define HAVE_SUPPORTED_CURVES

#undef  HAVE_TLS_EXTENSIONS
#define HAVE_TLS_EXTENSIONS

#undef  HAVE_SESSION_TICKET
#define HAVE_SESSION_TICKET

#undef  HAVE_EXTENDED_MASTER
#define HAVE_EXTENDED_MASTER

#undef  HAVE_TLS_EXTENSIONS
#define HAVE_TLS_EXTENSIONS

#undef  HAVE_SNI
#define HAVE_SNI

#undef  HAVE_MAX_FRAGMENT
#define HAVE_MAX_FRAGMENT

#undef  HAVE_TRUNCATED_HMAC
#define HAVE_TRUNCATED_HMAC

#undef  HAVE_ALPN
#define HAVE_ALPN

#undef  HAVE_TRUSTED_CA
#define HAVE_TRUSTED_CA

#undef  HAVE_SUPPORTED_CURVES
#define HAVE_SUPPORTED_CURVES

#undef  HAVE_SMIME
#define HAVE_SMIME

#undef  WOLFCRYPT_HAVE_SRP
#define WOLFCRYPT_HAVE_SRP

#undef  ASN_BER_TO_DER
#define ASN_BER_TO_DER

#undef  WOLFSSL_SMALL_STACK
#define WOLFSSL_SMALL_STACK

#undef  WOLFSSL_HAVE_CERT_SERVICE
#define WOLFSSL_HAVE_CERT_SERVICE

#undef  WOLFSSL_JNI
#define WOLFSSL_JNI

#undef  HAVE_EX_DATA
#define HAVE_EX_DATA

#undef  WOLFSSL_DTLS
#define WOLFSSL_DTLS

#undef  HAVE_CRL_MONITOR
#define HAVE_CRL_MONITOR

#undef  HAVE_LIGHTY
#define HAVE_LIGHTY

#undef  HAVE_WOLFSSL_SSL_H
#define HAVE_WOLFSSL_SSL_H 1

#undef  HAVE_EX_DATA
#define HAVE_EX_DATA

#undef  OPENSSL_ALL
#define OPENSSL_ALL

#undef  WOLFSSL_NGINX
#define WOLFSSL_NGINX

#undef  WOLFSSL_SIGNER_DER_CERT
#define WOLFSSL_SIGNER_DER_CERT

#undef  WOLFSSL_HAPROXY
#define WOLFSSL_HAPROXY

#undef  WOLFSSL_OPENVPN
#define WOLFSSL_OPENVPN

#undef  HAVE_KEYING_MATERIAL
#define HAVE_KEYING_MATERIAL

#undef  WOLFSSL_DES_ECB
#define WOLFSSL_DES_ECB

#undef  HAVE_EX_DATA
#define HAVE_EX_DATA

#undef  WOLFSSL_KEY_GEN
#define WOLFSSL_KEY_GEN

#undef  WOLFSSL_ALWAYS_VERIFY_CB
#define WOLFSSL_ALWAYS_VERIFY_CB

#undef  WOLFSSL_ALWAYS_KEEP_SNI
#define WOLFSSL_ALWAYS_KEEP_SNI

#undef  KEEP_OUR_CERT
#define KEEP_OUR_CERT

#undef  KEEP_PEER_CERT
#define KEEP_PEER_CERT

#undef  HAVE_EXT_CACHE
#define HAVE_EXT_CACHE

#undef  HAVE_EX_DATA
#define HAVE_EX_DATA

#undef  WOLFSSL_CERT_GEN
#define WOLFSSL_CERT_GEN

#undef  WOLFSSL_ASIO
#define WOLFSSL_ASIO

#undef  ASIO_USE_WOLFSSL
#define ASIO_USE_WOLFSSL

#undef  WOLFSSL_KEY_GEN
#define WOLFSSL_KEY_GEN

#undef  BOOST_ASIO_USE_WOLFSSL
#define BOOST_ASIO_USE_WOLFSSL

#undef  HAVE_EX_DATA
#define HAVE_EX_DATA

#undef  SSL_TXT_TLSV1_2
#define SSL_TXT_TLSV1_2

#undef  SSL_TXT_TLSV1_1
#define SSL_TXT_TLSV1_1

#undef  OPENSSL_NO_SSL2
#define OPENSSL_NO_SSL2

#undef  OPENSSL_NO_SSL3
#define OPENSSL_NO_SSL3

#undef  HAVE_ENCRYPT_THEN_MAC
#define HAVE_ENCRYPT_THEN_MAC

#undef  HAVE_STUNNEL
#define HAVE_STUNNEL

#undef  WOLFSSL_ALWAYS_VERIFY_CB
#define WOLFSSL_ALWAYS_VERIFY_CB

#undef  WOLFSSL_ALWAYS_KEEP_SNI
#define WOLFSSL_ALWAYS_KEEP_SNI

#undef  HAVE_EX_DATA
#define HAVE_EX_DATA

#undef  WOLFSSL_DES_ECB
#define WOLFSSL_DES_ECB

#undef  WOLFSSL_SIGNER_DER_CERT
#define WOLFSSL_SIGNER_DER_CERT

#undef  WOLFSSL_ENCRYPTED_KEYS
#define WOLFSSL_ENCRYPTED_KEYS

#undef  USE_FAST_MATH
#define USE_FAST_MATH

#undef  TFM_SMALL_SET
#define TFM_SMALL_SET

#undef  NO_PKCS12
#define NO_PKCS12

#undef  WC_NO_ASYNC_THREADING
#define WC_NO_ASYNC_THREADING

#undef  HAVE_AES_KEYWRAP
#define HAVE_AES_KEYWRAP

#undef  WOLFSSL_AES_DIRECT
#define WOLFSSL_AES_DIRECT

#undef  NO_OLD_RNGNAME
#define NO_OLD_RNGNAME

#undef  NO_OLD_WC_NAMES
#define NO_OLD_WC_NAMES

#undef  NO_OLD_SSL_NAMES
#define NO_OLD_SSL_NAMES

#undef  NO_OLD_SHA_NAMES
#define NO_OLD_SHA_NAMES

#undef  NO_OLD_MD5_NAME
#define NO_OLD_MD5_NAME

#undef  WOLFSSL_HASH_FLAGS
#define WOLFSSL_HASH_FLAGS

#undef  HAVE_DH_DEFAULT_PARAMS
#define HAVE_DH_DEFAULT_PARAMS

#undef  WOLFSSL_HAVE_WOLFSCEP
#define WOLFSSL_HAVE_WOLFSCEP

#undef  HAVE_PKCS7
#define HAVE_PKCS7

#undef  GCM_TABLE_4BIT
#define GCM_TABLE_4BIT

#undef  HAVE_AESGCM
#define HAVE_AESGCM

#undef  HAVE_WC_INTROSPECTION
#define HAVE_WC_INTROSPECTION


#ifdef __cplusplus
}
#endif


#endif /* WOLFSSL_OPTIONS_H */

