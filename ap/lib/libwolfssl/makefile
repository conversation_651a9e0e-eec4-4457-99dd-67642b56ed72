# =============================================================================
# 优化的 libwolfssl Makefile  
# =============================================================================
include $(COMMON_BASE_MK)

NAME := libwolfssl
SRC_DIR := wolfssl-4.8.1-stable
INSTALL_DIR := $(LIB_DIR)/$(NAME)/install

# 默认启用自定义配置以获得最小体积
USE_CUSTOM_WOLFSSL_CONFIG ?= y

CONFIG_OPTS := --host=arm-linux --build=x86_64-unknown-linux-gnu --target=arm-linux
CONFIG_OPTS += --prefix=$(INSTALL_DIR)

ifeq ($(CONFIG_LIBCURL_SHARE_LIB), y)
CONFIG_OPTS += --enable-shared
else
CONFIG_OPTS += --disable-shared
endif
CONFIG_OPTS += --enable-static

# 基础优化选项
CONFIG_OPTS += --disable-nls --enable-reproducible-build
CONFIG_OPTS += --disable-crypttests --disable-examples --disable-jobserver

# 编译优化 - 减小体积
CONFIG_OPTS += --enable-fastmath
CONFIG_OPTS += --disable-debug --disable-errorstrings
CONFIG_OPTS += --disable-trackmemory --disable-memorylog

# 网络协议优化
CONFIG_OPTS += --disable-ipv6 --disable-dtls --enable-tls13
CONFIG_OPTS += --disable-sslv3 --disable-tlsv10

# CPU特定优化
CONFIG_OPTS += --disable-armasm --disable-afalg
CONFIG_OPTS += --enable-devcrypto=no --disable-cryptodev

# HTTPS必需的基本加密功能 - 修复libcurl兼容性 (更保守的配置)
CONFIG_OPTS += --enable-opensslall --enable-opensslextra
CONFIG_OPTS += --enable-sni --enable-tlsext
CONFIG_OPTS += --enable-aes --enable-aesgcm --enable-aesccm
CONFIG_OPTS += --enable-sha --enable-sha224 --enable-sha256 --enable-sha384 --enable-sha512
CONFIG_OPTS += --enable-rsa --enable-ecc --enable-supportedcurves
CONFIG_OPTS += --enable-curve25519 --enable-ed25519
CONFIG_OPTS += --enable-sessioncerts --enable-keygen --enable-certgen --enable-certreq
CONFIG_OPTS += --enable-des3 --enable-dh
CONFIG_OPTS += --enable-hkdf --enable-x963kdf
# 启用完整的OpenSSL API兼容
CONFIG_OPTS += --enable-ssl --enable-all

# 禁用不必要功能以减小体积
CONFIG_OPTS += --disable-oldtls --disable-pwdbased --disable-scrypt
CONFIG_OPTS += --disable-md4 --disable-blake2
CONFIG_OPTS += --disable-sha3 --disable-shake256
CONFIG_OPTS += --disable-xchacha --disable-cryptocb
CONFIG_OPTS += --disable-pkcs7 --disable-pkcs12 --disable-cavium
CONFIG_OPTS += --disable-cavium-v --disable-xmss --disable-dilithium

# 禁用其他不必要功能
CONFIG_OPTS += --disable-ocsp --disable-ocspstapling --disable-ocspstapling2
CONFIG_OPTS += --disable-crl --disable-crl-monitor
CONFIG_OPTS += --disable-maxfragment --disable-alpn --disable-trustpeer

# 优化内存使用
CONFIG_OPTS += --enable-smallstack --disable-stacksize
CONFIG_OPTS += --enable-renegotiation-indication

all: $(SRC_DIR)/Makefile
	make -C $(SRC_DIR)
	make -C $(SRC_DIR) install
	# 优化：strip符号表减小库文件
ifeq ($(CONFIG_LIBCURL_SHARE_LIB), y)
	arm-linux-strip --strip-unneeded $(INSTALL_DIR)/lib/*.so* 2>/dev/null || true
else  
	arm-linux-strip --strip-debug $(INSTALL_DIR)/lib/*.a 2>/dev/null || true
endif

$(SRC_DIR)/Makefile:
	cd $(SRC_DIR) && ./autogen.sh
ifeq ($(USE_CUSTOM_WOLFSSL_CONFIG), y)
	@echo "创建自定义 user_settings.h 配置文件..."
	@echo '/* user_settings.h - wolfSSL 最小化配置用于 HTTPS 客户端 */' > $(SRC_DIR)/user_settings.h
	@echo '#ifndef WOLFSSL_USER_SETTINGS_H' >> $(SRC_DIR)/user_settings.h
	@echo '#define WOLFSSL_USER_SETTINGS_H' >> $(SRC_DIR)/user_settings.h
	@echo '' >> $(SRC_DIR)/user_settings.h
	@echo '/* 平台配置 */' >> $(SRC_DIR)/user_settings.h
	@echo '#define WOLFSSL_GENERAL_ALIGNMENT   4' >> $(SRC_DIR)/user_settings.h
	@echo '#define SIZEOF_LONG_LONG    8' >> $(SRC_DIR)/user_settings.h
	@echo '' >> $(SRC_DIR)/user_settings.h
	@echo '/* 禁用不需要的加密算法 - 只定义未在configure中定义的 */' >> $(SRC_DIR)/user_settings.h
	@echo '#ifndef NO_MD2' >> $(SRC_DIR)/user_settings.h
	@echo '#define NO_MD2' >> $(SRC_DIR)/user_settings.h
	@echo '#endif' >> $(SRC_DIR)/user_settings.h
	@echo '#ifndef NO_WRITEV' >> $(SRC_DIR)/user_settings.h
	@echo '#define NO_WRITEV' >> $(SRC_DIR)/user_settings.h
	@echo '#endif' >> $(SRC_DIR)/user_settings.h
	@echo '' >> $(SRC_DIR)/user_settings.h
	@echo '/* AES配置 */' >> $(SRC_DIR)/user_settings.h
	@echo '#ifndef HAVE_AES_CBC' >> $(SRC_DIR)/user_settings.h
	@echo '#define HAVE_AES_CBC' >> $(SRC_DIR)/user_settings.h
	@echo '#endif' >> $(SRC_DIR)/user_settings.h
	@echo '#ifndef WOLFSSL_AES_128' >> $(SRC_DIR)/user_settings.h
	@echo '#define WOLFSSL_AES_128' >> $(SRC_DIR)/user_settings.h
	@echo '#endif' >> $(SRC_DIR)/user_settings.h
	@echo '#ifndef WOLFSSL_AES_256' >> $(SRC_DIR)/user_settings.h
	@echo '#define WOLFSSL_AES_256' >> $(SRC_DIR)/user_settings.h
	@echo '#endif' >> $(SRC_DIR)/user_settings.h
	@echo '' >> $(SRC_DIR)/user_settings.h
	@echo '/* 启用椭圆曲线支持 - libcurl需要 */' >> $(SRC_DIR)/user_settings.h
	@echo '#ifndef HAVE_ECC' >> $(SRC_DIR)/user_settings.h
	@echo '#define HAVE_ECC' >> $(SRC_DIR)/user_settings.h
	@echo '#endif' >> $(SRC_DIR)/user_settings.h
	@echo '#ifndef HAVE_SUPPORTED_CURVES' >> $(SRC_DIR)/user_settings.h
	@echo '#define HAVE_SUPPORTED_CURVES' >> $(SRC_DIR)/user_settings.h
	@echo '#endif' >> $(SRC_DIR)/user_settings.h
	@echo '#ifndef HAVE_TLS_EXTENSIONS' >> $(SRC_DIR)/user_settings.h
	@echo '#define HAVE_TLS_EXTENSIONS' >> $(SRC_DIR)/user_settings.h
	@echo '#endif' >> $(SRC_DIR)/user_settings.h
	@echo '#ifndef OPENSSL_EXTRA' >> $(SRC_DIR)/user_settings.h
	@echo '#define OPENSSL_EXTRA' >> $(SRC_DIR)/user_settings.h
	@echo '#endif' >> $(SRC_DIR)/user_settings.h
	@echo '#ifndef OPENSSL_ALL' >> $(SRC_DIR)/user_settings.h
	@echo '#define OPENSSL_ALL' >> $(SRC_DIR)/user_settings.h
	@echo '#endif' >> $(SRC_DIR)/user_settings.h
	@echo '#ifndef WOLFSSL_CERT_GEN' >> $(SRC_DIR)/user_settings.h
	@echo '#define WOLFSSL_CERT_GEN' >> $(SRC_DIR)/user_settings.h
	@echo '#endif' >> $(SRC_DIR)/user_settings.h
	@echo '#ifndef WOLFSSL_KEY_GEN' >> $(SRC_DIR)/user_settings.h
	@echo '#define WOLFSSL_KEY_GEN' >> $(SRC_DIR)/user_settings.h
	@echo '#endif' >> $(SRC_DIR)/user_settings.h
	@echo '' >> $(SRC_DIR)/user_settings.h
	@echo '/* TLS版本控制 */' >> $(SRC_DIR)/user_settings.h
	@echo '#ifndef NO_SSL_v3' >> $(SRC_DIR)/user_settings.h
	@echo '#define NO_SSL_v3' >> $(SRC_DIR)/user_settings.h
	@echo '#endif' >> $(SRC_DIR)/user_settings.h
	@echo '#ifndef NO_TLS_v1' >> $(SRC_DIR)/user_settings.h
	@echo '#define NO_TLS_v1' >> $(SRC_DIR)/user_settings.h
	@echo '#endif' >> $(SRC_DIR)/user_settings.h
	@echo '#ifndef NO_TLS_v1_1' >> $(SRC_DIR)/user_settings.h
	@echo '#define NO_TLS_v1_1' >> $(SRC_DIR)/user_settings.h
	@echo '#endif' >> $(SRC_DIR)/user_settings.h
	@echo '' >> $(SRC_DIR)/user_settings.h
	@echo '/* 内存和性能优化 */' >> $(SRC_DIR)/user_settings.h
	@echo '#ifndef SMALL_SESSION_CACHE' >> $(SRC_DIR)/user_settings.h
	@echo '#define SMALL_SESSION_CACHE' >> $(SRC_DIR)/user_settings.h
	@echo '#endif' >> $(SRC_DIR)/user_settings.h
	@echo '' >> $(SRC_DIR)/user_settings.h
	@echo '/* 客户端专用配置 */' >> $(SRC_DIR)/user_settings.h
	@echo '#ifndef WOLFSSL_CLIENT_ONLY' >> $(SRC_DIR)/user_settings.h
	@echo '#define WOLFSSL_CLIENT_ONLY' >> $(SRC_DIR)/user_settings.h
	@echo '#endif' >> $(SRC_DIR)/user_settings.h
	@echo '#ifndef SINGLE_THREADED' >> $(SRC_DIR)/user_settings.h
	@echo '#define SINGLE_THREADED' >> $(SRC_DIR)/user_settings.h
	@echo '#endif' >> $(SRC_DIR)/user_settings.h
	@echo '' >> $(SRC_DIR)/user_settings.h
	@echo '/* 随机数生成 - 使用系统默认，不禁用 /dev/random */' >> $(SRC_DIR)/user_settings.h
	@echo '/* #define NO_DEV_RANDOM  // 注释掉以避免随机数生成错误 */' >> $(SRC_DIR)/user_settings.h
	@echo '' >> $(SRC_DIR)/user_settings.h
	@echo '#endif /* WOLFSSL_USER_SETTINGS_H */' >> $(SRC_DIR)/user_settings.h
	cd $(SRC_DIR) && ./configure \
		CFLAGS="-Os -ffunction-sections -fdata-sections -DNDEBUG" \
		CPPFLAGS="-DWOLFSSL_USER_SETTINGS" \
		LDFLAGS="-Wl,--gc-sections -s" \
		$(CONFIG_OPTS)
else
	cd $(SRC_DIR) && ./configure \
		CFLAGS="-Os -ffunction-sections -fdata-sections -DNDEBUG" \
		LDFLAGS="-Wl,--gc-sections -s" \
		$(CONFIG_OPTS)
endif

clean:
	-make -C $(SRC_DIR) distclean
	-rm -rf $(INSTALL_DIR)
	-rm -f $(SRC_DIR)/user_settings.h

romfs:
	cd $(INSTALL_DIR)/lib; \
	for i in *.so*; do \
		if [ -L $$i ]; then \
			$(ROMFSINST) -s `find $$i -printf %l` /lib/$$i; \
		elif [ -f $$i ]; then \
			$(ROMFSINST) /lib/$$i; \
		fi; \
	done