include $(COMMON_BASE_MK)

NAME := libwolfssl
SRC_DIR := wolfssl-4.8.1-stable
INSTALL_DIR := $(LIB_DIR)/$(NAME)/install

# 默认启用自定义配置以获得最小体积
USE_CUSTOM_WOLFSSL_CONFIG ?= y

CONFIG_OPTS := --host=arm-linux --build=x86_64-unknown-linux-gnu --target=arm-linux
CONFIG_OPTS += --prefix=$(INSTALL_DIR)

ifeq ($(CONFIG_LIBCURL_SHARE_LIB), y)
CONFIG_OPTS += --enable-shared
else
CONFIG_OPTS += --disable-shared
endif
CONFIG_OPTS += --enable-static

# 基础优化选项
CONFIG_OPTS += --disable-nls --enable-reproducible-build
CONFIG_OPTS += --disable-crypttests --disable-examples --disable-jobserver

# 编译优化 - 减小体积
CONFIG_OPTS += --enable-fastmath
CONFIG_OPTS += --disable-debug --disable-errorstrings
CONFIG_OPTS += --disable-trackmemory --disable-memorylog

# 网络协议优化 - 支持TLS 1.0-1.3以确保兼容性
CONFIG_OPTS += --disable-ipv6 --disable-dtls --enable-tls13
CONFIG_OPTS += --disable-sslv3

# CPU特定优化
CONFIG_OPTS += --disable-armasm --disable-afalg
CONFIG_OPTS += --enable-devcrypto=no --disable-cryptodev

# HTTPS必需的加密功能 - 增强兼容性配置
CONFIG_OPTS += --enable-opensslextra --enable-opensslall
CONFIG_OPTS += --enable-sni --enable-tlsext
CONFIG_OPTS += --enable-aes --enable-aesgcm --enable-aesccm
CONFIG_OPTS += --enable-sha --enable-sha256 --enable-sha384 --enable-sha512
CONFIG_OPTS += --enable-rsa --enable-ecc --enable-supportedcurves
CONFIG_OPTS += --enable-sessioncerts --enable-keygen --enable-certgen --enable-certreq
CONFIG_OPTS += --enable-dh --enable-hkdf --enable-x963kdf
CONFIG_OPTS += --enable-des3 --enable-hmac
# 启用SSL/TLS支持和会话票据
CONFIG_OPTS += --enable-ssl --enable-sessiontickets

# 禁用不必要的加密算法以减小体积（保留更多兼容性）
CONFIG_OPTS += --disable-pwdbased --disable-scrypt
CONFIG_OPTS += --disable-md2 --disable-md4 --disable-blake2 --disable-blake2s
CONFIG_OPTS += --disable-sha3 --disable-shake256
CONFIG_OPTS += --disable-xchacha --disable-cryptocb --disable-poly1305
CONFIG_OPTS += --disable-chacha --disable-chacha20poly1305
CONFIG_OPTS += --disable-curve25519 --disable-ed25519 --disable-curve448 --disable-ed448
CONFIG_OPTS += --disable-pkcs7 --disable-pkcs12 --disable-cavium
CONFIG_OPTS += --disable-cavium-v --disable-xmss --disable-dilithium
CONFIG_OPTS += --disable-arc4 --disable-rabbit --disable-hc128
CONFIG_OPTS += --disable-idea --disable-camellia --disable-rc2

# 禁用其他不必要功能（保留重要的TLS功能）
CONFIG_OPTS += --disable-ocsp --disable-ocspstapling --disable-ocspstapling2
CONFIG_OPTS += --disable-crl --disable-crl-monitor
CONFIG_OPTS += --disable-maxfragment --disable-alpn --disable-trustpeer
CONFIG_OPTS += --disable-srp --disable-psk
CONFIG_OPTS += --enable-secure-renegotiation --disable-fallback-scsv

# 优化内存使用
CONFIG_OPTS += --enable-smallstack --disable-stacksize

all: $(SRC_DIR)/Makefile
	make -C $(SRC_DIR)
	make -C $(SRC_DIR) install
	# 复制user_settings.h到安装目录
ifeq ($(USE_CUSTOM_WOLFSSL_CONFIG), y)
	cp $(SRC_DIR)/user_settings.h $(INSTALL_DIR)/include/
endif
	# 优化：strip符号表减小库文件
ifeq ($(CONFIG_LIBCURL_SHARE_LIB), y)
	arm-linux-strip --strip-unneeded $(INSTALL_DIR)/lib/*.so* 2>/dev/null || true
else
	arm-linux-strip --strip-debug $(INSTALL_DIR)/lib/*.a 2>/dev/null || true
endif

$(SRC_DIR)/Makefile:
	cd $(SRC_DIR) && ./autogen.sh
ifeq ($(USE_CUSTOM_WOLFSSL_CONFIG), y)
	@echo "创建自定义 user_settings.h 配置文件..."
	@echo '/* user_settings.h - wolfSSL 最小化配置用于 HTTPS 客户端 */' > $(SRC_DIR)/user_settings.h
	@echo '#ifndef WOLFSSL_USER_SETTINGS_H' >> $(SRC_DIR)/user_settings.h
	@echo '#define WOLFSSL_USER_SETTINGS_H' >> $(SRC_DIR)/user_settings.h
	@echo '' >> $(SRC_DIR)/user_settings.h
	@echo '/* 平台配置 */' >> $(SRC_DIR)/user_settings.h
	@echo '#define WOLFSSL_GENERAL_ALIGNMENT   4' >> $(SRC_DIR)/user_settings.h
	@echo '#define SIZEOF_LONG_LONG    8' >> $(SRC_DIR)/user_settings.h
	@echo '' >> $(SRC_DIR)/user_settings.h
	@echo '/* 禁用不需要的加密算法和功能（保留兼容性） */' >> $(SRC_DIR)/user_settings.h
	@echo '#define NO_MD2' >> $(SRC_DIR)/user_settings.h
	@echo '#define NO_MD4' >> $(SRC_DIR)/user_settings.h
	@echo '#define NO_WRITEV' >> $(SRC_DIR)/user_settings.h
	@echo '#define NO_RC4' >> $(SRC_DIR)/user_settings.h
	@echo '#define NO_RABBIT' >> $(SRC_DIR)/user_settings.h
	@echo '#define NO_HC128' >> $(SRC_DIR)/user_settings.h
	@echo '#define NO_IDEA' >> $(SRC_DIR)/user_settings.h
	@echo '#define NO_CAMELLIA' >> $(SRC_DIR)/user_settings.h
	@echo '#define NO_BLAKE2' >> $(SRC_DIR)/user_settings.h
	@echo '#define NO_SHA3' >> $(SRC_DIR)/user_settings.h
	@echo '#define NO_CHACHA' >> $(SRC_DIR)/user_settings.h
	@echo '#define NO_POLY1305' >> $(SRC_DIR)/user_settings.h
	@echo '#define NO_CURVE25519' >> $(SRC_DIR)/user_settings.h
	@echo '#define NO_ED25519' >> $(SRC_DIR)/user_settings.h
	@echo '#define NO_PKCS7' >> $(SRC_DIR)/user_settings.h
	@echo '#define NO_PKCS12' >> $(SRC_DIR)/user_settings.h
	@echo '#define NO_PSK' >> $(SRC_DIR)/user_settings.h
	@echo '#define NO_SRP' >> $(SRC_DIR)/user_settings.h
	@echo '' >> $(SRC_DIR)/user_settings.h
	@echo '/* AES配置 - 启用必需的模式 */' >> $(SRC_DIR)/user_settings.h
	@echo '#define HAVE_AES_CBC' >> $(SRC_DIR)/user_settings.h
	@echo '#define HAVE_AESGCM' >> $(SRC_DIR)/user_settings.h
	@echo '#define HAVE_AESCCM' >> $(SRC_DIR)/user_settings.h
	@echo '#define WOLFSSL_AES_128' >> $(SRC_DIR)/user_settings.h
	@echo '#define WOLFSSL_AES_256' >> $(SRC_DIR)/user_settings.h
	@echo '' >> $(SRC_DIR)/user_settings.h
	@echo '/* DES3和HMAC支持 - 兼容性需要 */' >> $(SRC_DIR)/user_settings.h
	@echo '#define HAVE_DES3' >> $(SRC_DIR)/user_settings.h
	@echo '#define HAVE_HMAC' >> $(SRC_DIR)/user_settings.h
	@echo '' >> $(SRC_DIR)/user_settings.h
	@echo '/* 椭圆曲线支持 - libcurl需要 */' >> $(SRC_DIR)/user_settings.h
	@echo '#define HAVE_ECC' >> $(SRC_DIR)/user_settings.h
	@echo '#define HAVE_SUPPORTED_CURVES' >> $(SRC_DIR)/user_settings.h
	@echo '#define HAVE_TLS_EXTENSIONS' >> $(SRC_DIR)/user_settings.h
	@echo '' >> $(SRC_DIR)/user_settings.h
	@echo '/* OpenSSL兼容性 - libcurl需要 */' >> $(SRC_DIR)/user_settings.h
	@echo '#define OPENSSL_EXTRA' >> $(SRC_DIR)/user_settings.h
	@echo '#define OPENSSL_ALL' >> $(SRC_DIR)/user_settings.h
	@echo '#define WOLFSSL_CERT_GEN' >> $(SRC_DIR)/user_settings.h
	@echo '#define WOLFSSL_KEY_GEN' >> $(SRC_DIR)/user_settings.h
	@echo '' >> $(SRC_DIR)/user_settings.h
	@echo '/* TLS版本控制 - 支持TLS 1.0-1.3以确保兼容性 */' >> $(SRC_DIR)/user_settings.h
	@echo '#define NO_SSL_v3' >> $(SRC_DIR)/user_settings.h
	@echo '/* 保留TLS 1.0和1.1支持以确保兼容性 */' >> $(SRC_DIR)/user_settings.h
	@echo '/* #define NO_TLS_v1 */' >> $(SRC_DIR)/user_settings.h
	@echo '/* #define NO_TLS_v1_1 */' >> $(SRC_DIR)/user_settings.h
	@echo '' >> $(SRC_DIR)/user_settings.h
	@echo '/* 内存和性能优化 */' >> $(SRC_DIR)/user_settings.h
	@echo '#define SMALL_SESSION_CACHE' >> $(SRC_DIR)/user_settings.h
	@echo '#define WOLFSSL_SMALL_STACK' >> $(SRC_DIR)/user_settings.h
	@echo '' >> $(SRC_DIR)/user_settings.h
	@echo '/* 客户端专用配置 */' >> $(SRC_DIR)/user_settings.h
	@echo '#define WOLFSSL_CLIENT_ONLY' >> $(SRC_DIR)/user_settings.h
	@echo '#define SINGLE_THREADED' >> $(SRC_DIR)/user_settings.h
	@echo '' >> $(SRC_DIR)/user_settings.h
	@echo '/* 随机数生成 - 使用系统默认 */' >> $(SRC_DIR)/user_settings.h
	@echo '/* #define NO_DEV_RANDOM  // 保持注释以使用系统随机数 */' >> $(SRC_DIR)/user_settings.h
	@echo '' >> $(SRC_DIR)/user_settings.h
	@echo '#endif /* WOLFSSL_USER_SETTINGS_H */' >> $(SRC_DIR)/user_settings.h
	cd $(SRC_DIR) && ./configure \
		CFLAGS="-Os -ffunction-sections -fdata-sections -DNDEBUG" \
		CPPFLAGS="-DWOLFSSL_USER_SETTINGS" \
		LDFLAGS="-Wl,--gc-sections -s" \
		$(CONFIG_OPTS)
else
	cd $(SRC_DIR) && ./configure \
		CFLAGS="-Os -ffunction-sections -fdata-sections -DNDEBUG" \
		LDFLAGS="-Wl,--gc-sections -s" \
		$(CONFIG_OPTS)
endif

clean:
	-make -C $(SRC_DIR) distclean
	-rm -rf $(INSTALL_DIR)
	-rm -f $(SRC_DIR)/user_settings.h

romfs:
	cd $(INSTALL_DIR)/lib; \
	for i in *.so*; do \
		if [ -L $$i ]; then \
			$(ROMFSINST) -s `find $$i -printf %l` /lib/$$i; \
		elif [ -f $$i ]; then \
			$(ROMFSINST) /lib/$$i; \
		fi; \
	done
