# =============================================================================
# 优化的 libwolfssl Makefile  
# =============================================================================
include $(COMMON_BASE_MK)

NAME := libwolfssl
SRC_DIR := wolfssl-4.8.1-stable
INSTALL_DIR := $(LIB_DIR)/$(NAME)/install

CONFIG_OPTS := --host=arm-linux --build=x86_64-unknown-linux-gnu --target=arm-linux
CONFIG_OPTS += --prefix=$(INSTALL_DIR)

ifeq ($(CONFIG_LIBCURL_SHARE_LIB), y)
CONFIG_OPTS += --enable-shared
else
CONFIG_OPTS += --disable-shared
endif
CONFIG_OPTS += --enable-static

# 基础优化选项
CONFIG_OPTS += --disable-nls --enable-reproducible-build
CONFIG_OPTS += --disable-crypttests --disable-examples --disable-jobserver

# 编译优化 - 减小体积
CONFIG_OPTS += --enable-fastmath --enable-fasthugemath
CONFIG_OPTS += --disable-debug --disable-errorstrings
CONFIG_OPTS += --disable-trackmemory --disable-memorylog

# 网络协议优化
CONFIG_OPTS += --disable-ipv6 --disable-dtls --enable-tls13
CONFIG_OPTS += --disable-sslv3 --disable-tlsv10  # 禁用老旧不安全协议

# CPU特定优化
CONFIG_OPTS += --disable-armasm --disable-afalg
CONFIG_OPTS += --enable-devcrypto=no --disable-cryptodev

# HTTPS必需的最小加密功能
CONFIG_OPTS += --enable-opensslall --enable-sni
CONFIG_OPTS += --enable-aes --enable-aesgcm --enable-aesccm
CONFIG_OPTS += --enable-des3 --enable-sha --enable-sha224 --enable-sha256
CONFIG_OPTS += --enable-sha384 --enable-sha512 --enable-rsa --enable-dh

# 现代加密算法（推荐）
CONFIG_OPTS += --enable-chacha --enable-poly1305
CONFIG_OPTS += --enable-curve25519 --enable-ed25519

# 禁用不必要的加密功能以减小体积
CONFIG_OPTS += --disable-oldtls --disable-tlsx
CONFIG_OPTS += --disable-pwdbased --disable-scrypt
CONFIG_OPTS += --disable-md4 --disable-md2 --disable-blake2
CONFIG_OPTS += --disable-sha3 --disable-shake256
CONFIG_OPTS += --disable-poly1305 --disable-chacha
CONFIG_OPTS += --disable-xchacha --disable-cryptocb
CONFIG_OPTS += --disable-pkcs7 --disable-pkcs12 --disable-cavium
CONFIG_OPTS += --disable-cavium-v --disable-xmss --disable-dilithium

# 禁用其他不必要功能
CONFIG_OPTS += --disable-filesystem --disable-inline
CONFIG_OPTS += --disable-ocsp --disable-ocspstapling --disable-ocspstapling2
CONFIG_OPTS += --disable-crl --disable-crl-monitor --disable-sni
CONFIG_OPTS += --disable-maxfragment --disable-alpn --disable-trustpeer
CONFIG_OPTS += --disable-sessioncerts --disable-keygen --disable-certgen
CONFIG_OPTS += --disable-certreq --disable-certext --disable-certgencache

# 优化内存使用
CONFIG_OPTS += --enable-smallstack --disable-stacksize
CONFIG_OPTS += --enable-renegotiation-indication

all: $(SRC_DIR)/Makefile
	make -C $(SRC_DIR)
	make -C $(SRC_DIR) install
	# 优化：strip符号表减小库文件
ifeq ($(CONFIG_LIBCURL_SHARE_LIB), y)
	arm-linux-strip --strip-unneeded $(INSTALL_DIR)/lib/*.so*
else  
	arm-linux-strip --strip-debug $(INSTALL_DIR)/lib/*.a
endif

$(SRC_DIR)/Makefile:
	cd $(SRC_DIR) && ./autogen.sh
	# 添加额外的编译优化标志
	cd $(SRC_DIR) && ./configure \
		CFLAGS="-Os -ffunction-sections -fdata-sections -DNDEBUG" \
		CPPFLAGS="-DWOLFSSL_USER_SETTINGS" \
		LDFLAGS="-Wl,--gc-sections -s" \
		$(CONFIG_OPTS)

clean:
	-make -C $(SRC_DIR) distclean
	-rm -rf $(INSTALL_DIR)

romfs:
	cd $(INSTALL_DIR)/lib; \
	for i in *.so*; do \
		if [ -L $$i ]; then \
			$(ROMFSINST) -s `find $$i -printf %l` /lib/$$i; \
		elif [ -f $$i ]; then \
			$(ROMFSINST) /lib/$$i; \
		fi; \
	done