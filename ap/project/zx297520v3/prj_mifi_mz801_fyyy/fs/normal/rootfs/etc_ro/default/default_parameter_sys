
######SOFTAP专用 START###################################
check_roam=yes
gsm_signalbar0_low=0
gsm_signalbar0_high=0
gsm_signalbar1_low=1
gsm_signalbar1_high=2
gsm_signalbar2_low=3
gsm_signalbar2_high=6
gsm_signalbar3_low=7
gsm_signalbar3_high=9
gsm_signalbar4_low=10
gsm_signalbar4_high=12
gsm_signalbar5_low=13
gsm_signalbar5_high=31
wcdma_signalbar0_low=0
wcdma_signalbar0_high=0
wcdma_signalbar1_low=1
wcdma_signalbar1_high=8
wcdma_signalbar2_low=9
wcdma_signalbar2_high=16
wcdma_signalbar3_low=17
wcdma_signalbar3_high=22
wcdma_signalbar4_low=23
wcdma_signalbar4_high=28
wcdma_signalbar5_low=29
wcdma_signalbar5_high=91
tds_signalbar0_low=0
tds_signalbar0_high=0
tds_signalbar1_low=1
tds_signalbar1_high=8
tds_signalbar2_low=9
tds_signalbar2_high=16
tds_signalbar3_low=17
tds_signalbar3_high=22
tds_signalbar4_low=23
tds_signalbar4_high=28
tds_signalbar5_low=29
tds_signalbar5_high=91
lte_signalbar0_low=0
lte_signalbar0_high=0
lte_signalbar1_low=1
lte_signalbar1_high=25
lte_signalbar2_low=26
lte_signalbar2_high=32
lte_signalbar3_low=33
lte_signalbar3_high=38
lte_signalbar4_low=39
lte_signalbar4_high=44
lte_signalbar5_low=45
lte_signalbar5_high=97
need_cops_number_format=yes
need_display_searching_status=yes
need_restart_when_sim_insert=no
need_support_pb=no
need_support_sms=yes
network_need_gsm=yes
network_need_tds=yes
product_model=MF910W
roam_setting_option=on
use_lock_net=no
network_category_based_on=act
admin_user=admin
appKeyMobile=A100000853
Brand=DEMO
hostName=dm.wo.com.cn
Intype=DEMO
Manuf=
Model=
portNum=6001
registerDmType=0
secsTime=1
versionPrevious=
wa_version=
OSVersion=1
OS=TOS
urlMobile=
DNS_proxy=
dnsmasqfile=/var/log/dnsmasq.log
dnsmasqfileSize=1024
lan_domain_Enabled=1
auto_connect_when_limited=no
is_traffic_aline_on=no
is_traffic_alining=no
is_traffic_limit_on=no
keep_online_when_limited=no
traffic_sms_number=0
update_type=mifi_fota

fota_device_type=mifi
fota_models=ZTE7520V3
fota_oem=ZTE
fota_platform=ZX297520
fota_dm_vendor=rs
fota_token_rs=8msgzdxxftrtys0irnuifh7o
fota_product_id=1549891427
fota_product_secret=f631eba30dc94546ad3f1cd38327af32
fota_app_version=2
fota_network_type=WIFI
fota_token_zx=7CBE016400F65621740A04E742E6FB12
fota_dl_url_zx=
fota_chk_url_zx=
fota_reg_url_zx=
fota_report_dlr_url_zx=
fota_report_upgr_url_zx=
fota_report_sales_url_zx=
fota_token_gs=97a53ee9f45adfe53c762a72f83f6f43
fota_dl_url_gs=
fota_chk_url_gs=
fota_reg_url_gs=
fota_report_dlr_url_gs=
fota_report_upgr_url_gs=
fota_report_sales_url_gs=
fota_update_space_threshold=200
Login=admin
Password=
sntp_server_count=3
MAX_Station_num=32
MAX_Station_num_bak=0
MAX_Access_num_bbak=0
MAX_Chip_Capability=32
wifi_key_gen_type=DEFAULT
wifi_key_len=8
wifi_key_only_digit=y
wifi_lte_intr=1
##qirui ##
# type, MAC SN IMEI
wifi_ssid_gen_type=MAC
wifi_ssid_gen_with_mac_lastbyte=4
wifi_key_gen_with_mac_lastbyte=8
wifi_mac_num=2
wifiwan=wlan0-vxd
wifiwan_mode=dhcp
idle_time="600"
product_type=1
errnofile=/usr/netlog/errno.log
errnofileSize=1024
hotplugfile=/usr/netlog/hotplug.log
hotplugfileSize=1024
mynetlinkfile=/usr/netlog/mynetlink.log
mynetlinkfileSize=1024
telnetd_enable=y
print_level=1
syslog_level=4
telog_path=/dev/ttyS1
skb_debug=
seclog_switch=
################SOFTAP专用 END#####################################

######USB端口配置#######

##########usb/rj45等热拔插相关路径##############
#rj45初始状态路径
rj45_plugstate_path=/sys/kernel/eth_debug/eth_state
#usb网口名路径
usb_name_path=/sys/dwc_usb/usbconfig/netname
#usb各网口状态路径
usb_plugstate_path=/sys/dwc_usb/usbconfig/
################################################

#配置是否有光盘
cdrom_state=1
#cdrom第二阶段枚举：0为不需要枚举，1为需要枚举且需要挂载介质，2为不需要挂载介质#
need_support_cdrom_step2=0

#配置Windows下网卡类型
select_type=select_rndis

####
#配置研发模式usb设备端口组合
usb_devices_debug=diag,adb,serial

#配置用户模式usb设备端口组合
usb_devices_user=

#配置生产模式usb设备端口组合
usb_devices_factory=serial,diag

#配置AMT模式usb设备端口组合
usb_devices_amt=serial,diag

#配置研发模式acm串口个数
usb_acm_num_debug=0

#配置研发模式serial串口个数
usb_serial_num_debug=2

#配置用户模式acm串口个数
usb_acm_num_user=0

#配置用户模式serial串口个数
usb_serial_num_user=0

#配置生产模式acm串口个数
usb_acm_num_factory=0

#配置生产模式serial串口个数
usb_serial_num_factory=1

#配置AMT模式acm串口个数
usb_acm_num_amt=0

#配置AMT模式serial串口个数
usb_serial_num_amt=2

#wangzhen
#配置debug模式mass_storage的lun的模式
usb_lun_type_debug=

#配置user模式mass_storage的lun的模式
usb_lun_type_user=

#配置cdrom模式mass_storage的lun的模式
usb_lun_type_cdrom=
###

#配置研发模式网口为NDIS时设备的PID
PID_TSP_NDIS=0581
###

#配置研发模式网口为RNDIS时设备的PID
PID_TSP_RNDIS=0581

#配置研发模式网口为ECM时设备的PID
PID_TSP_ECM=0581

#配置研发模式异常时设备的PID
PID_TEST=ff00

#配置眼图模式时设备的PID
PID_YT=0580

#配置研发模式光盘CDROM的PID
PID_TSP_CDROM=0548

#####

#配置用户模式模式光盘CDROM的PID
PID_USER_CDROM=1225

#####
#######
#配置用户模式网口为RNDIS时设备的PID
PID_USER_RNDIS=1557

#配置用户模式网口为ECM时设备的PID
PID_USER_ECM=1557

#配置用户模式网口为NDIS时设备的PID
PID_USER_NDIS=1557

#配置用户模式没有网口时设备的PID
PID_USER_NOVNIC=0580
######

#配置死机trap时设备的PID
PID_TRAP=0197

#配置生产模式时设备的PID
PID_FACTORY=0534

#配置AMT模式时设备的PID
PID_AMT=0201

#配置关机充电时设备的PID
PID_FASTPOWEROFF=2004

#配置设备的VID
VID_TSP=19D2

#配置设备的iSerial字符串
SERIAL_TSP=1234567890ABCDEF

#配置研发模式设备的ReleaseID
RELEASEID_TSP=0100

#配置用户模式设备的ReleaseID
RELEASEID_USER=0101

#配置设备的manufacturer字符串
MANUFACTURER_TSP=DEMO,Incorporated

#配置设备的product字符串
PRODUCT_TSP=DEMO Mobile Boardband

#配置设备的config字符串
CONFIG_TSP=DEMO Configuration

#cdrom lun的vendor字符串
VENDOR_MS_CDROM=DEMO

#cdrom lun的product字符串
PRODUCT_MS_CDROM=USB SCSI CD-ROM

#cdrom lun的release字符串
RELEASE_MS_CDROM=2.31

#cdrom lun的inquiry字符串
INQUIRY_MS_CDROM=DEMO USB SCSI CD-ROM 2.31
#mmc lun的vendor字符串
VENDOR_MS_MMC=DEMO

#mmc lun的product字符串
PRODUCT_MS_MMC=MMC Storage

#mmc lun的release字符串
RELEASE_MS_MMC=2.31

#SD介质路径
usb_tcard_lun_path=/dev/mmcblk0

#mmc lun的inquiry字符串
INQUIRY_MS_MMC=DEMO MMC Storage 2.31

#配置ECM网卡个数
ECM_NUM=1

#配置RNDIS网卡最大组包数
RNDIS_PKT_NUM=10


###波特率自适应开关###
self_adaption_port=
###某些端口不需要向应用上报端口准备好消息###
notify_forbiden_ports=

######USB端口配置END#######

######CP侧NV配置信息#######
#内部软件版本号
zversion=K318V1.0.0B03
#外部软件版本号
zcgmr=DEMO_V1.0.0B03
#硬件版本号
zhver=V0.1
#内部机型
zcgmm=K318
#外部机型
zcgmw=K318
#厂商信息
zcgmi=DEMO
#SSID号
zssid=0
#WifiKey的值
zwifikey=0
#锁网最大解锁次数，锁网模块使用
zunlocktimes=3
#版本模式类型，正常版本/挂侧版本
zversionmode=0
#智能短信功能开关
zisms=0
#DM功能开关,0:关闭，1移动，2联通，3电信
zdmreg=0
#联通DM 注册开机等待时间, 1~60 有效，单位分钟，其他数值: 无效值
zdmregtime=0
#自动搜网方式网络接入次序
auto_acqorder=
#工具log 存取
zcat_mode=
#照相机图片分辨率显示控制
zcamera_interprolation=1
######CP侧NV配置信息END#######

#for volte
MTNET_TEST=0
NSIOT_TEST=0
amrw=1
cvmod_ims=3
EM_CALL_SUPPORT=0
EMCALL_TO_NORMAL=0
IMS_APN=
IMS_CONFURI=
IMS_URI_TYPE=0
IMS_REG_EXPIRES=600000
IMS_REG_SUB_EXPIRES=600000
NET_IMS_CAP=0
UDP_THRESH=1300
IPSEC_DISABLE=0
PRECONDTION_NOT_SUPPORT=0
WAIT_RPT_TIME=1000
OCT_ALGN_AMR=0
SMS_OVER_IP=1
IMS_USER_AGENT=
BSF_PORT=8080
XCAP_PORT=80
UT_APN=
380_THEN_CSFB=0
DTMF_RFC4733=0
DIALOG_FORK=0
DATA_CENTRIC=0
not_supp_hmac_sha1_96=0
not_supp_hmac_md5_96=0
not_supp_des_ede3_cbc=0
not_supp_aes_cbc=0
IPV4_PCSCF_ADDR=
IPV6_PCSCF_ADDR=
IMS_KOREA_TEST=0
PCSCF_TEST_ENABLE=0
G_IMS_CMGF=0
IMS_SMS_URI_TYPE=0
IMS_CC_URI_TYPE=0
T3396_SUPPORT=0
T3396_CAUSE_REASON=8,27,32,33
T3396_DURATION=720
T3396_COUNTS=3
IMS_SIP_START_PORT=5060
#for volte end
fastbr_level=1
# qrzl web
sim_select_show_iccid=0
sim_select_num_type=ESIM1_only,ESIM2_only
is_sim_select_show=1
is_sim_select_password=0
is_show_sn=0
is_show_current_sim=0
rsim_is_esim3=0
# china_telecom china_mobile china_united
esim1_mno=
esim2_mno=
esim3_mno=
esim1_iccid=
esim2_iccid=
esim3_iccid=

# qrzl app
# 认证类型，多个用,分割 【 CMP_HTTP | ONE_LINK_HTTP 】
qrzl_cloud_authentic_type=ONE_LINK_HTTP

# 认证开关，在存在认证类型的条件中，再增加一个给客户控制认证的开关
qrzl_cloud_authentic_switch=1

### 认证mac持久化
one_link_authed_mac=
cmp_authed_mac=
uninet_authed_mac=
# 电信认证
## 官方地址
cmp_auth_http_url=https://cmp-api.ctwing.cn
## 官方地址端口
cmp_auth_http_port=
## 客户信息（认证程序逻辑分支会用到） 原生请求：ORIGIN； 定制请求：【 WUXING | XUNYOU | ....】 
cmp_customer_info=
## 客户返回appKey、sign信息的接口
cmp_customers_get_token_url=https://iot.xkwei.com/adminapi/openapi/v1.0
## 客户自定义认证页面
cmp_customers_auth_page_url=
# appkey 原生请求 需填写此信息
cmp_auth_appkey=
# 电信密钥 原生请求 需填写此信息
cmp_auth_secretKey=

# 移动认证
## 官方地址
ONE_LINK_auth_http_url=https://wireless.api.cmonelink.com
## 官方地址端口
ONE_LINK_auth_http_port=80
## 客户信息（认证程序逻辑分支会用到）
ONE_LINK_customer_info=FYYY
## 客户返回APPID、TOKEN信息的接口
ONE_LINK_customers_get_token_url=http://ww.iotyunyilian.cn/index/index/getToken
## 客户自定义认证页面
ONE_LINK_customers_auth_page_url=
ONE_LINK_auth_appid=
ONE_LINK_auth_password=


# TYPE HTTP MQTT
qrzl_cloud_protocol_type=HTTP

# type XUNJI QICHENG
qrzl_cloud_http_request_type=QICHENG
qrzl_cloud_http_path=http://ww.iotyunyilian.cn/wifi/qi_rui/statusUpdate
qrzl_cloud_request_interval_time=300

# TYPE MQTT_TYPE_1
qrzl_cloud_mqtt_type=MQTT_TYPE_1
qrzl_cloud_mqtt_server=tcp://mqtt-qr202501.cz12.cn:8031
qrzl_cloud_mqtt_username=QR202411
qrzl_cloud_mqtt_password=ZAQ!@WSX123

qrzl_user_net_disconn=0

# auto_switch_esim_type 0: don't switch, 1: only esim switch, 2: esim out card switch
auto_switch_esim_type=1
switch_esim_detection_interval=30

qrzl_limit_down_speed=0
qrzl_limit_up_speed=0
qrzl_lte_band_auto_switch=0
