# qrzl web
sim_select_show_iccid=0
sim_select_num_type=ESIM1_only,ESIM2_only
is_sim_select_show=0
is_sim_select_password=0
is_show_sn=0
is_show_current_sim=0
rsim_is_esim3=0
# china_telecom china_mobile china_united
esim1_mno=
esim2_mno=
esim3_mno=
esim1_iccid=
esim2_iccid=
esim3_iccid=

# qrzl app

# 认证类型，多个用,分割 【 CMP_HTTP | ONE_LINK_HTTP 】
qrzl_cloud_authentic_type=CMP_HTTP,ONE_LINK_HTTP

# 认证开关，在存在认证类型的条件中，再增加一个给客户控制认证的开关
qrzl_cloud_authentic_switch=1

### 认证mac持久化
one_link_authed_mac=
cmp_authed_mac=
uninet_authed_mac=

# 电信认证
## 官方地址
cmp_auth_http_url=https://cmp-api.ctwing.cn
## 官方地址端口
cmp_auth_http_port=20164
## 客户信息（认证程序逻辑分支会用到） 原生请求：ORIGIN； 定制请求：【 WUXING | XUNYOU | ....】 
cmp_customer_info=CHUANGSAN
## 客户返回appKey、sign信息的接口
cmp_customers_get_token_url=https://qrui.api.sentry.bs-iot.com/v1
## 客户自定义认证页面
cmp_customers_auth_page_url=
# appkey 原生请求 需填写此信息
cmp_auth_appkey=
# 电信密钥 原生请求 需填写此信息
cmp_auth_secretKey=

# 移动认证
## 官方地址
ONE_LINK_auth_http_url=https://wireless.api.cmonelink.com
## 官方地址端口
ONE_LINK_auth_http_port=80
## 客户信息（认证程序逻辑分支会用到）
ONE_LINK_customer_info=CHUANGSAN
## 客户返回APPID、TOKEN信息的接口
ONE_LINK_customers_get_token_url=https://qrui.api.sentry.bs-iot.com/v1
## 客户自定义认证页面
ONE_LINK_customers_auth_page_url=
ONE_LINK_auth_appid=
ONE_LINK_auth_password=

# TYPE HTTP MQTT
qrzl_cloud_protocol_type=CS_HTTP

# type XUNJI QICHENG
qrzl_cloud_http_request_type=CS_HTTP
qrzl_cloud_http_path=https://qrui.api.sentry.bs-iot.com/v1/startup_event?locally=false
qrzl_cloud_http_cs_result_path=https://qrui.api.sentry.bs-iot.com/v1/startup_event/cmd_result
qrzl_cloud_request_interval_time=300

# TYPE MQTT_TYPE_1
qrzl_cloud_mqtt_type=MQTT_TYPE_1
qrzl_cloud_mqtt_server=tcp://mqtt-qr202501.cz12.cn:8031
qrzl_cloud_mqtt_username=QR202411
qrzl_cloud_mqtt_password=ZAQ!@WSX123

qrzl_user_net_disconn=0

# auto_switch_esim_type 0: don't switch, 1: only esim switch, 2: esim out card switch
auto_switch_esim_type=1
switch_esim_detection_interval=30
number_of_ppp_status_checks=45

qrzl_limit_down_speed=0
qrzl_limit_up_speed=0
qrzl_lte_band_auto_switch=0

# ����ͳ�����
rsim_flux_total=0
rsim_flux_day_total=0
rsim_flux_month_total=0
esim1_flux_total=0
esim1_flux_day_total=0
esim1_flux_month_total=0
esim2_flux_total=0
esim2_flux_day_total=0
esim2_flux_month_total=0

# 主板温度梯度
board_pa_temperature_line=2389+2456+2536+2630+2741+2872+3024+3202+3406+3638+3898+4186+4496+4823+5161+5498+5825+6132+6411+6658+6869+7046+7191+7307+7397+7467+7519
board_temperature=0
