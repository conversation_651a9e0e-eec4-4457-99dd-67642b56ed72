/*****************************************************************************
*  �汾���� (C)����ͨѶ�ɷ����޹�˾
*  ģ����    ��MMI
*  �ļ���    ��mmi_lcd.c
*  �ļ���ʶ  ��
*  ����ļ�  ��
*  ʵ�ֹ���  ��
*  ����      ��
*  �汾      ��V1.0
*  �������  ��2014-6-20
*  ����˵��  ��
*
*******************************************************************************/

/***********************************LCDͳһ����********************************/



/*****************************************************************************
��Ļ�����С����
******************************************************************************/
#define MMI_LCD_SHOW_STRING_LEN 			68
#define MMI_LCD_CHAR8_MAX_LEN    			18	//ÿ����ʾ����󳤶ȣ����ڴ�ֵ��������ʾ��������仯���޸�
#define MMI_LCD_CHAR9_MAX_LEN    			18	//ÿ����ʾ����󳤶ȣ����ڴ�ֵ��������ʾ��������仯���޸�

/*****************************************************************************
�ļ���Դ·������
******************************************************************************/
#define MMI_RESOURCE_PATH  					"/etc_ro/mmi/"

/*****************************************************************************
 LED����˸Ƶ������
******************************************************************************/

#define LED_BLINK_TIME_INTERVAL			1000

//LED�ƿ���/����ʱ��    ��λ:ms
#define LED_FAST_BLINK_ON_TIME          100//1000
#define LED_FAST_BLINK_OFF_TIME         100//1000
#define LED_SLOW_BLINK_ON_TIME          250//2000
#define LED_SLOW_BLINK_OFF_TIME         250//5000

#define LED_BAT_BLINK_ON_TIME			800
#define LED_BAT_BLINK_OFF_TIME			1200
#define LED_LAN_BLINK_ON_TIME			400
#define LED_LAN_BLINK_OFF_TIME			1600
#define LED_SMS_BLINK_ON_TIME			800
#define LED_SMS_BLINK_OFF_TIME			1200
#define LED_WAN_BLINK_ON_TIME			200
#define LED_WAN_BLINK_OFF_TIME			1400
#define LED_VOIP_BLINK_ON_TIME			400
#define LED_VOIP_BLINK_OFF_TIME			1600

#define LED_BREATH_BLINK_ON_TIME		300
#define LED_BREATH_BLINK_OFF_TIME		5000
//������ǿ:PDP����SOCKETδ����1.8s��0.2s����SOCKET�����ӷ�֮
#define LED_WAN_FAST_BLINK_ON_TIME		200
#define LED_WAN_FAST_BLINK_OFF_TIME		1800
#define LED_WAN_SLOW_BLINK_ON_TIME		1800
#define LED_WAN_SLOW_BLINK_OFF_TIME		200
//yaoyuan cpe
#define LED_WAN_CPE_FAST_BLINK_ON_TIME		500
#define LED_WAN_CPE_FAST_BLINK_OFF_TIME		500
#define LED_WAN_CPE_SLOW_BLINK_ON_TIME		500
#define LED_WAN_CPE_SLOW_BLINK_OFF_TIME		1000



/*******************************************************************************
  LED�ƹ�������//���漸���Ƕ����˺�����ʱ�õ���·��
*******************************************************************************/
#define ALL_LED_PATH					"/sys/class/leds/battery_led/brightness"
#define BATTERY_LED_PATH				"/sys/class/leds/battery_led/ledSwitch"
#define WAN_LED_PATH					"/sys/class/leds/net_led/ledSwitch"
#define LAN_LED_PATH					"/sys/class/leds/wifi_led/ledSwitch"
#define SMS_LED_PATH					"/sys/class/leds/sms_led/ledSwitch"
#define TRAFFIC_LED_PATH				"/sys/class/leds/traffic_led/ledSwitch"

#ifdef JCV_CHARLIEPLEX_LED_DIGITS
#define CHARLIEPLEX_LED_DIGITS_DEV_PATH				"/dev/charlieplex_display"
#endif

/*
/�ַ�������:��һλ: ��ɫ 1-RED 2-GREEN 3-YELLOW 4-BLUE;
			�ڶ�λ: ״̬ 0-OFF 1-ON 2-BLINK;
			������λ: ���������ĸ�����00-09�ֱ�Ϊ��1����10�������ƣ�10Ϊ��Чֵ*/
#define LED_STATE_GREEN_OFF				"2010"
#define LED_STATE_YELLOW_OFF			"3010"
#define LED_STATE_BLUE_OFF				"4010"
#define LED_TRAFFIC_ALL_OFF				"2009"

#define LED_ALL_POWER_ON				"1"
#define LED_ALL_POWER_OFF				"2"

#define LED_STATE_RED_ON				"1110"
#define LED_STATE_RED_BLINK				"1210"
#define LED_STATE_GREEN_ON				"2110"
#define LED_STATE_GREEN_BLINK			"2210"
#define LED_STATE_YELLOW_ON				"3110"
#define LED_STATE_YELLOW_BLINK			"3210"
#define LED_STATE_BLUE_ON				"4110"
#define LED_STATE_BLUE_BLINK			"4210"

#define TRAFIIC_LED_1_ON				"2100"
#define TRAFIIC_LED_1_BLINK				"2200"
#define TRAFIIC_LED_2_ON				"2101"
#define TRAFIIC_LED_2_BLINK				"2201"
#define TRAFIIC_LED_3_ON				"2102"
#define TRAFIIC_LED_3_BLINK				"2202"
#define TRAFIIC_LED_4_ON				"2103"
#define TRAFIIC_LED_4_BLINK				"2203"
#define TRAFIIC_LED_5_ON				"2104"
#define TRAFIIC_LED_5_BLINK				"2204"
#define TRAFIIC_LED_6_ON				"2105"
#define TRAFIIC_LED_6_BLINK				"2205"
#define TRAFIIC_LED_7_ON				"2106"
#define TRAFIIC_LED_7_BLINK				"2206"
#define TRAFIIC_LED_8_ON				"2107"
#define TRAFIIC_LED_8_BLINK				"2207"
#define TRAFIIC_LED_9_ON				"2108"
#define TRAFIIC_LED_9_BLINK				"2208"
#define TRAFIIC_LED_10_ON				"2109"
#define TRAFIIC_LED_10_BLINK			"2209"


#define LED_WAN_RED_BRIGHTNESS			"/sys/class/leds/modem_r_led/brightness"
#define LED_WAN_RED_BLINKSWITCH			"/sys/class/leds/modem_r_led/trigger"
#define LED_WAN_RED_BLINKTIMEON			"/sys/class/leds/modem_r_led/delay_on"
#define LED_WAN_RED_BLINKTIMEOFF		"/sys/class/leds/modem_r_led/delay_off"

#define LED_WAN_GREEN_BRIGHTNESS        "/sys/class/leds/modem_g_led/brightness"
#define LED_WAN_GREEN_BLINKSWITCH		"/sys/class/leds/modem_g_led/trigger"
#define LED_WAN_GREEN_BLINKTIMEON		"/sys/class/leds/modem_g_led/delay_on"
#define LED_WAN_GREEN_BLINKTIMEOFF		"/sys/class/leds/modem_g_led/delay_off"


#define LED_WAN_BLUE_BRIGHTNESS			"/sys/class/leds/modem_b_led/brightness"
#define LED_WAN_BLUE_BLINKSWITCH		"/sys/class/leds/modem_b_led/trigger"
#define LED_WAN_BLUE_BLINKTIMEON		"/sys/class/leds/modem_b_led/delay_on"
#define LED_WAN_BLUE_BLINKTIMEOFF		"/sys/class/leds/modem_b_led/delay_off"

#define LED_BATTERY_GREEN_BRIGHTNESS	"/sys/class/leds/battery_g_led/brightness"
#define LED_BATTERY_GREEN_BLINKSWITCH	"/sys/class/leds/battery_g_led/trigger"
#define LED_BATTERY_GREEN_BLINKTIMEON	"/sys/class/leds/battery_g_led/delay_on"
#define LED_BATTERY_GREEN_BLINKTIMEOFF	"/sys/class/leds/battery_g_led/delay_off"

#define LED_BATTERY_RED_BRIGHTNESS		"/sys/class/leds/battery_r_led/brightness"
#define LED_BATTERY_RED_BLINKSWITCH		"/sys/class/leds/battery_r_led/trigger"
#define LED_BATTERY_RED_BLINKTIMEON		"/sys/class/leds/battery_r_led/delay_on"
#define LED_BATTERY_RED_BLINKTIMEOFF 	"/sys/class/leds/battery_r_led/delay_off"

#if defined(QRZL_UE) && defined(JCV_HW_MZ901_V1_0)
#define LED_BATTERY_1_BRIGHTNESS	"/sys/class/leds/battery_1_led/brightness"
#define LED_BATTERY_1_BLINKSWITCH	"/sys/class/leds/battery_1_led/trigger"
#define LED_BATTERY_1_BLINKTIMEON	"/sys/class/leds/battery_1_led/delay_on"
#define LED_BATTERY_1_BLINKTIMEOFF	"/sys/class/leds/battery_1_led/delay_off"

#define LED_BATTERY_2_BRIGHTNESS	"/sys/class/leds/battery_2_led/brightness"
#define LED_BATTERY_2_BLINKSWITCH	"/sys/class/leds/battery_2_led/trigger"
#define LED_BATTERY_2_BLINKTIMEON	"/sys/class/leds/battery_2_led/delay_on"
#define LED_BATTERY_2_BLINKTIMEOFF	"/sys/class/leds/battery_2_led/delay_off"

#define LED_BATTERY_3_BRIGHTNESS	"/sys/class/leds/battery_3_led/brightness"
#define LED_BATTERY_3_BLINKSWITCH	"/sys/class/leds/battery_3_led/trigger"
#define LED_BATTERY_3_BLINKTIMEON	"/sys/class/leds/battery_3_led/delay_on"
#define LED_BATTERY_3_BLINKTIMEOFF	"/sys/class/leds/battery_3_led/delay_off"

#define LED_BATTERY_4_BRIGHTNESS	"/sys/class/leds/battery_4_led/brightness"
#define LED_BATTERY_4_BLINKSWITCH	"/sys/class/leds/battery_4_led/trigger"
#define LED_BATTERY_4_BLINKTIMEON	"/sys/class/leds/battery_4_led/delay_on"
#define LED_BATTERY_4_BLINKTIMEOFF	"/sys/class/leds/battery_4_led/delay_off"
#endif

#define LED_SMS_GREEN_BRIGHTNESS		"/sys/class/leds/sms_led/brightness"
#define LED_SMS_GREEN_BLINKSWITCH		"/sys/class/leds/sms_led/trigger"
#define LED_SMS_GREEN_BLINKTIMEON		"/sys/class/leds/sms_led/delay_on"
#define LED_SMS_GREEN_BLINKTIMEOFF		"/sys/class/leds/sms_led/delay_off"

#define LED_SMS_BLUE_BRIGHTNESS		    "/sys/class/leds/sms_led/brightness"
#define LED_SMS_BLUE_BLINKSWITCH		"/sys/class/leds/sms_led/trigger"
#define LED_SMS_BLUE_BLINKTIMEON		"/sys/class/leds/sms_led/delay_on"
#define LED_SMS_BLUE_BLINKTIMEOFF		"/sys/class/leds/sms_led/delay_off"

#ifdef JCV_HW_MZ803_V3_2
#define LED_LAN_GREEN_BRIGHTNESS		"/sys/class/leds/wifi_g_led/brightness"
#define LED_LAN_GREEN_BLINKSWITCH		"/sys/class/leds/wifi_g_led/trigger"
#define LED_LAN_GREEN_BLINKTIMEON		"/sys/class/leds/wifi_g_led/delay_on"
#define LED_LAN_GREEN_BLINKTIMEOFF		"/sys/class/leds/wifi_g_led/delay_off"
#else
#define LED_LAN_GREEN_BRIGHTNESS		"/sys/class/leds/wifi_led/brightness"
#define LED_LAN_GREEN_BLINKSWITCH		"/sys/class/leds/wifi_led/trigger"
#define LED_LAN_GREEN_BLINKTIMEON		"/sys/class/leds/wifi_led/delay_on"
#define LED_LAN_GREEN_BLINKTIMEOFF		"/sys/class/leds/wifi_led/delay_off"
#endif

#define LED_LAN_BLUE_BRIGHTNESS		    "/sys/class/leds/wifi_led/brightness"
#define LED_LAN_BLUE_BLINKSWITCH		"/sys/class/leds/wifi_led/trigger"
#define LED_LAN_BLUE_BLINKTIMEON		"/sys/class/leds/wifi_led/delay_on"
#define LED_LAN_BLUE_BLINKTIMEOFF		"/sys/class/leds/wifi_led/delay_off"

#define LED_VOIP_BLUE_BRIGHTNESS		"/sys/class/leds/sms_led/brightness"
#define LED_VOIP_BLUE_BLINKSWITCH		"/sys/class/leds/sms_led/trigger"
#define LED_VOIP_BLUE_BLINKTIMEON		"/sys/class/leds/sms_led/delay_on"
#define LED_VOIP_BLUE_BLINKTIMEOFF		"/sys/class/leds/sms_led/delay_off"

#define LED_SIGNAL1_BLUE_BRIGHTNESS		"/sys/class/leds/4g_1_led/brightness"
#define LED_SIGNAL1_BLUE_BLINKSWITCH	"/sys/class/leds/4g_1_led/trigger"
#define LED_SIGNAL1_BLUE_BLINKTIMEON	"/sys/class/leds/4g_1_led/delay_on"
#define LED_SIGNAL1_BLUE_BLINKTIMEOFF	"/sys/class/leds/4g_1_led/delay_off"

#define LED_SIGNAL2_BLUE_BRIGHTNESS		"/sys/class/leds/4g_2_led/brightness"
#define LED_SIGNAL2_BLUE_BLINKSWITCH	"/sys/class/leds/4g_2_led/trigger"
#define LED_SIGNAL2_BLUE_BLINKTIMEON	"/sys/class/leds/4g_2_led/delay_on"
#define LED_SIGNAL2_BLUE_BLINKTIMEOFF	"/sys/class/leds/4g_2_led/delay_off"

#define LED_SIGNAL3_BLUE_BRIGHTNESS		"/sys/class/leds/4g_3_led/brightness"
#define LED_SIGNAL3_BLUE_BLINKSWITCH	"/sys/class/leds/4g_3_led/trigger"
#define LED_SIGNAL3_BLUE_BLINKTIMEON	"/sys/class/leds/4g_3_led/delay_on"
#define LED_SIGNAL3_BLUE_BLINKTIMEOFF	"/sys/class/leds/4g_3_led/delay_off"

#define LED_SIGNAL4_BLUE_BRIGHTNESS		"/sys/class/leds/4g_4_led/brightness"
#define LED_SIGNAL4_BLUE_BLINKSWITCH	"/sys/class/leds/4g_4_led/trigger"
#define LED_SIGNAL4_BLUE_BLINKTIMEON	"/sys/class/leds/4g_4_led/delay_on"
#define LED_SIGNAL4_BLUE_BLINKTIMEOFF	"/sys/class/leds/4g_4_led/delay_off"

#define LED_SIGNAL5_BLUE_BRIGHTNESS		"/sys/class/leds/4g_5_led/brightness"
#define LED_SIGNAL5_BLUE_BLINKSWITCH	"/sys/class/leds/4g_5_led/trigger"
#define LED_SIGNAL5_BLUE_BLINKTIMEON	"/sys/class/leds/4g_5_led/delay_on"
#define LED_SIGNAL5_BLUE_BLINKTIMEOFF	"/sys/class/leds/4g_5_led/delay_off"

#define LED_WPS_BLUE_BRIGHTNESS		"/sys/class/leds/wps_led/brightness"
#define LED_WPS_BLUE_BLINKSWITCH	"/sys/class/leds/wps_led/trigger"
#define LED_WPS_BLUE_BLINKTIMEON	"/sys/class/leds/wps_led/delay_on"
#define LED_WPS_BLUE_BLINKTIMEOFF	"/sys/class/leds/wps_led/delay_off"

#define LED_RJ11_BLUE_BRIGHTNESS	"/sys/class/leds/rj11_led/brightness"
#define LED_RJ11_BLUE_BLINKSWITCH	"/sys/class/leds/rj11_led/trigger"
#define LED_RJ11_BLUE_BLINKTIMEON	"/sys/class/leds/rj11_led/delay_on"
#define LED_RJ11_BLUE_BLINKTIMEOFF	"/sys/class/leds/rj11_led/delay_off"


#define LED_BLINKON_STATE               "timer"
#define LED_BLINKOFF_STATE              "none"


//����·������
#define BOOST_LOAD_STATUS_PATH			"/sys/class/power_supply/boost/online"

//��ѹ�ٽ�ֵ

//zdm �������漸����ֵ���ݲ�����ƽ̨��������һ������������޸ģ���ֱ�����nv���п���
#ifdef QRZL_LIANXIANG_MZ806

#define POWEROFFLEVEL           		3380        //���ڴ˵�ѹֵ�Զ��ػ�
#define POWERONLEVEL           			3400       //���ڴ˵�ѹֵ���ܿ���
#define DISCHARGELEVEL					3700		//���ڴ˵�ѹֵ�ŵ������Ϊ0.5A
#define CHARGINGLEVEL					3800		//���ڴ˵�ѹֵ�ŵ������Ϊ1.5A

#else

#define POWEROFFLEVEL           		3300        //���ڴ˵�ѹֵ�Զ��ػ�
#define POWERONLEVEL           			3400       //���ڴ˵�ѹֵ���ܿ���
#define DISCHARGELEVEL					3600		//���ڴ˵�ѹֵ�ŵ������Ϊ0.5A
#define CHARGINGLEVEL					3800		//���ڴ˵�ѹֵ�ŵ������Ϊ1.5A

#endif



#define CHECK_POWER_TIME_INTERVAL      	20000
#define GET_TEMP_INTERVAL_TIME		    20000//ms

//WIFI��Ϣ����
#define WIFI_STATE_PATH				"/etc_rw/wifiStatus"
#define WPS_STATE_PATH				"/etc_rw/wpsStatus"
#define WPS_STATEEX_PATH			"/etc_rw/wpsdisplayStatus"
#define QRCODE_STATE_PATH			"/etc_rw/qrStatus"
#define WIFI_STATION_PATH			"/etc_rw/staStatus"
#define WIFI_DATA_VA0		        "wlan0-va0"
#define WIFI_DATA_VA1		        "wlan0-va1"
#define WIFI_TXBYTE		            0//��������
#define WIFI_RXBYTE		            2//��������
#define WIFICODE_MAIN_PATH          "/etc_rw/wifi/ssid_wifikey.bmp"
#define WIFICODE_GUST1_PATH         "/etc_rw/wifi/multi_ssid_wifikey.bmp"



/*******************************************************************************
������ʾָʾ��״̬����
*******************************************************************************/
#define VOIP_IN_CALL                "ledon"
#define VOIP_IN_CONNECTION          "ledblink"
#define VOIP_HANG_UP                "ledoff"

