#include <stdio.h>
#include <stdlib.h>
#include <assert.h>
#include <string.h>
#include "fs_check.h"

extern int mount_fs_partition(struct mtd_fs *p_fs);
extern int mtd_erase_partition(const char* partition_name);
extern int mtd_write_partition(const char* partition_name, const char* image_file);
extern int unmount_fs_partition(struct mtd_fs *p_fs);
extern int check_userdata_is_normal();
extern char *g_path_prefix;

static struct mtd_fs fs_array[] = {
#ifdef USE_UBIFS
#ifdef CONFIG_SYSTEM_CAP
	{
		.patition_name="capuserdata",
		.mtd_index=-1,
		.mount_point="/mnt/userdata",
		.image_file="/etc_ro/ap_capuserdata.img",
		.fs_type="ubifs",
		.mount_opt="",
		.sh_cmd=NULL
	},
#else
	{
		.patition_name="userdata",
		.mtd_index=-1,
		.mount_point="/mnt/userdata",
		.image_file="/etc_ro/ap_userdata.img",
		.fs_type="ubifs",
		.mount_opt="",
		.sh_cmd=NULL
	},
#endif
#else
#ifdef CONFIG_SYSTEM_CAP
	{
		.patition_name="capuserdata",
		.mtd_index=-1,
		.mount_point="/mnt/userdata",
		.image_file="/etc_ro/ap_capuserdata.img",
		.fs_type="jffs2",
		.mount_opt="",
		.sh_cmd=NULL
	},
#else
	{
		.patition_name="userdata",
		.mtd_index=-1,
		.mount_point="/mnt/userdata",
		.image_file="/etc_ro/ap_userdata.img",
		.fs_type="jffs2",
		.mount_opt="",
		.sh_cmd=NULL
	},
#endif
#endif
#ifndef CONFIG_SYSTEM_CAP
	{
		.patition_name="nvrofs",
		.mtd_index=-1,
		.mount_point="/mnt/nvrofs",
		.image_file=NULL,
		.fs_type="jffs2",
		.mount_opt="-o ro",
		.sh_cmd=NULL
	}
#endif
};

int main(int argc, char *argv[]) {

	int i;
	int ret;
	int result;
	int fs_cnt = sizeof(fs_array)/sizeof(fs_array[0]);
	
	//recovery版本文件系统挂载
	if (0 == strcmp(argv[1], "recovery"))
	{
		g_path_prefix = "/recovery";
		for(i = 0; i < fs_cnt; i++) {
			printf("fs_check mount_fs_partition begin\n"); 
			ret = mount_fs_partition(&fs_array[i]);
			printf("fs_check mount_fs_partition mount result %d\n",ret);
		}
		return 0;
	}

	//normal版本文件系统挂载
	for(i = 0; i < fs_cnt; i++) {
		printf("fs_check mount_fs_partition begin\n"); 
		ret = mount_fs_partition(&fs_array[i]);
		if(ret)
		{
			unmount_fs_partition(&fs_array[i]);
			mtd_erase_partition(fs_array[i].patition_name);
			printf("fs_check mtd_erase %s\n", fs_array[i].patition_name);
			ret = mtd_write_partition(fs_array[i].patition_name, fs_array[i].image_file);
			printf("fs_check mtd_write %s ret = %d\n", fs_array[i].patition_name, ret);
			ret = mount_fs_partition(&fs_array[i]);
			if(ret)
				assert(0);
		}
#ifndef CONFIG_SYSTEM_CAP
		else
		{
			//挂载成功后检查文件是否被破坏，如果破坏则恢复
			if ((0 == strcmp(fs_array[i].patition_name, "userdata")))
			{
				result = check_userdata_is_normal();
			}
			else
			{
				result = 0;
			}
			if(result)
			{
				unmount_fs_partition(&fs_array[i]);
				//printf("fs_check mount %s fail\n", fs_array[i].patition_name);
				mtd_erase_partition(fs_array[i].patition_name);
				printf("fs_check mtd_erase %s\n", fs_array[i].patition_name);
				ret = mtd_write_partition(fs_array[i].patition_name, fs_array[i].image_file);
				printf("fs_check mtd_write %s ret = %d\n", fs_array[i].patition_name, ret);
				ret = mount_fs_partition(&fs_array[i]);
				if(ret)
					assert(0);
			}
		}
#endif
		printf("fs_check mount %s success!\n", fs_array[i].patition_name);
	}

	return 0;
}


