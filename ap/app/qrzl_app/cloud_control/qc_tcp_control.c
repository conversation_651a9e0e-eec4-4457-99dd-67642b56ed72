#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <signal.h>
#include <errno.h>
#include <pthread.h>
#include <math.h>
#include <netinet/tcp.h>
#include "qc_tcp_control.h"
#include "../qrzl_utils.h"
#include "../common_utils/cjson.h"

#define ZC_MSG_MAX_LENGTH  sizeof(uint16_t) + sizeof(uint32_t)  + 0xffff
#define MAX_CMD_ID_CACHE 60
/* tcp请求的路径 */ 
static char tcp_request_path[256] = {0};
/* 请求间隔时间 */
static int request_interval_time = 60;
//tcp请求的端口
static int tcp_request_port = 0;

extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

static uint32_t sent_cmd_id_cache[MAX_CMD_ID_CACHE] = {0};
static int sent_cmd_id_index = 0;


static pthread_mutex_t send_message_lock;

static int zc_sockfd = -1;
static uint8_t zc_is_login = 0;
static uint8_t heartbeat_pack_loss_num = 0;
static uint16_t zc_send_serial_number = 0;


// ============= 添加开始 =============
static time_t last_receive_time = 0; // 记录最后一次收到消息的时间
#define SERVER_TIMEOUT (3 * 60)      // 3分钟超时（180秒）
// ============= 添加结束 =============


// 数据包结构体定义
typedef struct {
    /*
    uint8_t start_flag;        // 起始位
    uint8_t cmd;               // 命令号
    uint16_t checksum;         // 校验码
    uint16_t serial_number;    // 序列号
     */
    uint16_t body_length;      // 包体长度
    uint32_t  cmd_id;         //请求ID
    char *body;             // 包体
} zc_data_packet_t;


// 添加 cmd_id 到缓存（循环方式）
void cache_sent_cmd_id(uint32_t cmd_id) {
    sent_cmd_id_cache[sent_cmd_id_index] = cmd_id;
    sent_cmd_id_index = (sent_cmd_id_index + 1) % MAX_CMD_ID_CACHE;
}
// 判断某个 cmd_id 是否为客户端自己发的
int is_cmd_id_sent_by_me(uint32_t cmd_id) {
    int i;
    for ( i = 0; i < MAX_CMD_ID_CACHE; ++i) {
        if (sent_cmd_id_cache[i] == cmd_id) {
            return 1;
        }
    }
    return 0;
}
// 重新连接 socket 后清空缓存
void clear_cmd_id_cache() {
    memset(sent_cmd_id_cache, 0, sizeof(sent_cmd_id_cache));
    sent_cmd_id_index = 0;
}
/**
 * 初始化一些配置信息 
 * */
static int init_config_data() 
{
    int cfg_ret;
    cfg_ret = cfg_get_item("qrzl_cloud_tcp_path", tcp_request_path, 256);
    printf("tcp_request_path: %s\n", tcp_request_path);
    if (cfg_ret != 0 || tcp_request_path == NULL || strcmp(tcp_request_path, "") == 0)
    {
        qrzl_log("tcp_request_path is NULL");
        return -1;
    }
    char uc_qrzl_cloud_tcp_port[10]= {0};
    cfg_ret=cfg_get_item("qrzl_cloud_tcp_port", uc_qrzl_cloud_tcp_port, sizeof(uc_qrzl_cloud_tcp_port));
    tcp_request_port = atoi(uc_qrzl_cloud_tcp_port);
    printf("tcp_request_port: %d\n", tcp_request_port);
    if (cfg_ret != 0 || tcp_request_port <= 0)
    {
        qrzl_log("tcp_request_port is NULL");
        return -1;
    }
    // strcpy(http_request_path, "192.168.0.111:8080/api/devicesev/statusUpdate");
    char cloud_request_interval_time[10] = {0};
    cfg_get_item(NV_QRZL_CLOUD_REQUEST_INTERVAL_TIME, cloud_request_interval_time, 10);
    request_interval_time = atoi(cloud_request_interval_time);
    if (request_interval_time == 0) {
        request_interval_time = 300;
    }
    return 0;
}

// 释放包体内存的函数
static void zc_free_packet(zc_data_packet_t *packet) {
    if (packet->body) {
        free(packet->body);
    }
}
// 命令号生成函数：生成请求ID
uint32_t generate_cmd_id() {
    static uint32_t last_time_part = 0;  // 保存上一次的时间（小时+分钟）
    static uint32_t counter = 0;         // 当前分钟内的计数器

    time_t now = time(NULL);
    struct tm tm_info;

    localtime_r(&now, &tm_info);  // 获取本地时间（线程安全）

    // 生成时+分格式（HHMM），如15:38 → "1538"
    char time_str[5] = {0};
    snprintf(time_str, sizeof(time_str), "%02d%02d", tm_info.tm_hour, tm_info.tm_min);
    uint32_t current_time_part = (uint32_t)strtoul(time_str, NULL, 10);

    // 如果跨分钟，重置计数器
    if (current_time_part != last_time_part) {
        last_time_part = current_time_part;
        counter = 0;
    }

    counter++;

    // 拼接格式为 HHMMCCCC，如 15380001，最多支持每分钟9999个请求
    uint32_t cmd_id = current_time_part * 10000 + (counter % 10000);

    return cmd_id;
}
/** 
 * 转换时间戳为字符串yyyymmddhhmmss
 */
static void timestamp_to_string(time_t timestamp, char *buffer, size_t buffer_size)
{
    // 将时间戳转换为 tm 结构
    struct tm *time_info = localtime(&timestamp);

    // 格式化时间为 yyyymmddhhmmss
    strftime(buffer, buffer_size, "%Y%m%d%H%M%S", time_info);
}

static void close_and_reset_zc_socket()
{
    //qrzl_log("close_and_reset_zc_socket");
    printf("close_and_reset_zc_socket\n");
    if (zc_sockfd != -1) {
        shutdown(zc_sockfd, SHUT_RDWR);
        close(zc_sockfd);
        zc_sockfd = -1;
    }
    heartbeat_pack_loss_num = 0;
    zc_is_login = 0;
    zc_send_serial_number = 0;
}

static int create_socket(const char *hostname, int port_int)
{
    qrzl_log("create_socket");
    struct addrinfo hints, *res;
    int sockfd;

    memset(&hints, 0, sizeof(hints));
    hints.ai_family = AF_INET;          // IPv4
    hints.ai_socktype = SOCK_STREAM;    // TCP
    char port[6] = {0};
    snprintf(port, sizeof(port), "%d", port_int);
    
    int ret = getaddrinfo(hostname, port, &hints, &res);
    if (ret != 0) {
        printf("getaddrinfo failed for %s:%d - %s\n", hostname, port_int, gai_strerror(ret));
        return -1;
    }
 
    sockfd = socket(res->ai_family, res->ai_socktype, res->ai_protocol);
    if (sockfd == -1) {
        perror("socket creation failed");
        freeaddrinfo(res);
        return -1;
    }
     // 设置接收超时（读超时）为120秒
    struct timeval rcv_tv = {120, 0};
    if (setsockopt(sockfd, SOL_SOCKET, SO_RCVTIMEO, &rcv_tv, sizeof(rcv_tv)) == -1) {
        qrzl_log("setsockopt(SO_RCVTIMEO) failed: %s", strerror(errno));
    }
    if (connect(sockfd, res->ai_addr, res->ai_addrlen) == -1) {
        perror("socket 连接失败");
        close(sockfd);
        freeaddrinfo(res);
        return -1;
    }else {
        printf("socket 连接成功\n");
           // 打印目标地址和端口
        char ipstr[INET_ADDRSTRLEN];
        struct sockaddr_in *ipv4 = (struct sockaddr_in *)res->ai_addr;
        inet_ntop(AF_INET, &(ipv4->sin_addr), ipstr, sizeof(ipstr));
        printf("连接目标 IP: %s, 端口: %d\n", ipstr, ntohs(ipv4->sin_port));
        //暂时没有登录验证，连接成功代表登录成功
        zc_is_login=1;
    }

    freeaddrinfo(res);
    return sockfd;
}

static void  qicheng_build_send_buffer(char *send_buffer,size_t buffer_len)
{
    snprintf(send_buffer, buffer_len, "{");
    snprintf(send_buffer, buffer_len, "%s\"imei\":\"%s\"",send_buffer,g_qrzl_device_static_data.imei);
    snprintf(send_buffer, buffer_len, "%s,\"iccid\":\"%s\"", send_buffer, g_qrzl_device_dynamic_data.iccid);
    snprintf(send_buffer, buffer_len, "%s,\"mac\":\"%s\"", send_buffer, g_qrzl_device_static_data.mac);
    //KB单位
    snprintf(send_buffer, buffer_len, "%s,\"amount_end\":\"%llu\"", send_buffer, g_qrzl_device_dynamic_data.flux_month_total_bytes * 8 / 1024);
    char time_string[15] = {0}; // 长度需要能存储 "yyyymmddhhmmss" 和一个 '\0'
    time_t current_time = time(NULL);
    timestamp_to_string(current_time, time_string, sizeof(time_string));
    snprintf(send_buffer, buffer_len, "%s,\"current_time\":\"%s\"", send_buffer, time_string);
    snprintf(send_buffer, buffer_len, "%s,\"ssid\":\"%s\"", send_buffer, g_qrzl_device_dynamic_data.wifi_ssid);
    snprintf(send_buffer, buffer_len, "%s,\"key\":\"%s\"", send_buffer, g_qrzl_device_dynamic_data.wifi_key_base64);
    snprintf(send_buffer, buffer_len, "%s,\"remainPwr\":\"%s\"", send_buffer, g_qrzl_device_dynamic_data.remain_power);
    snprintf(send_buffer, buffer_len, "%s,\"conn_cnt\":\"%d\"", send_buffer, g_qrzl_device_dynamic_data.conn_num);
    snprintf(send_buffer, buffer_len, "%s,\"mcc\":\"%s\"", send_buffer, g_qrzl_device_dynamic_data.mcc);
    snprintf(send_buffer, buffer_len, "%s,\"mnc\":\"%s\"", send_buffer, g_qrzl_device_dynamic_data.mnc);
    snprintf(send_buffer, buffer_len, "%s,\"lac\":\"%s\"", send_buffer, g_qrzl_device_dynamic_data.tac);
    snprintf(send_buffer, buffer_len, "%s,\"cid\":\"%s\"", send_buffer, g_qrzl_device_dynamic_data.cid);
    snprintf(send_buffer, buffer_len, "%s,\"soft_version\":\"%s\"", send_buffer, g_qrzl_device_static_data.soft_version);
    int wifistatus = 1;
    if (g_qrzl_device_dynamic_data.wifi_enable == 0) {
        wifistatus = 0;
    } else if (g_qrzl_device_dynamic_data.wifi_enable == 1 && g_qrzl_device_dynamic_data.wifi_hide == 1)
    {
        wifistatus = 2;
    }
    snprintf(send_buffer, buffer_len, "%s,\"wifistatus\":\"%d\"", send_buffer, wifistatus);
    snprintf(send_buffer, buffer_len, "%s,\"sn\":\"%s\"", send_buffer, g_qrzl_device_static_data.sn);
    snprintf(send_buffer, buffer_len, "%s,\"upspeed\":\"%llu\"", send_buffer, get_up_limit_net_speed() * 1000);
    snprintf(send_buffer, buffer_len, "%s,\"downspeed\":\"%llu\"", send_buffer, get_down_limit_net_speed() * 1000);
    snprintf(send_buffer, buffer_len, "%s,\"RSSI\":\"%s\"", send_buffer, g_qrzl_device_dynamic_data.rssi);
    snprintf(send_buffer, buffer_len, "%s,\"webPassword\":\"%s\"", send_buffer, g_qrzl_device_dynamic_data.web_password);
    snprintf(send_buffer, buffer_len, "%s,\"SINR\":\"%s\"", send_buffer, g_qrzl_device_dynamic_data.sinr);
    snprintf(send_buffer, buffer_len, "%s,\"RSRQ\":\"%s\"", send_buffer, g_qrzl_device_dynamic_data.rsrq);
    snprintf(send_buffer, buffer_len, "%s,\"PCI\":\"%s\"", send_buffer, g_qrzl_device_dynamic_data.pci);
    snprintf(send_buffer, buffer_len, "%s,\"IMSI\":\"%s\"", send_buffer, g_qrzl_device_dynamic_data.imsi);
    snprintf(send_buffer, buffer_len, "%s,\"Devicetype\":\"%s\"", send_buffer, g_qrzl_device_static_data.device_type);
    char apn_config_name[65] = {0};
    cfg_get_item("m_profile_name", apn_config_name, sizeof(apn_config_name));
    remove_spaces(apn_config_name);
    char apn_username[65] = {0};
    cfg_get_item("ppp_username", apn_username, sizeof(apn_username));
    char apn_password[65] = {0};
    cfg_get_item("ppp_passtmp", apn_password, sizeof(apn_password));
    char apn_login_number[32] = {0};
    cfg_get_item("wan_dial", apn_login_number, sizeof(apn_login_number));
    char apn_apn[32] = {0};
    cfg_get_item("wan_apn", apn_apn, sizeof(apn_apn));
    char apn_pdp_type[32] = {0};
    cfg_get_item("pdp_type", apn_pdp_type, sizeof(apn_pdp_type));
    char apn_auth_typee[32] = {0};
    cfg_get_item("ppp_auth_mode", apn_auth_typee, sizeof(apn_auth_typee));
    char apn[512] = {0};
    snprintf(apn, sizeof(apn), 
    "MCCMNC:%s%s;ConfigFileName:%s;UserName:%sPassword:%s;LoginNumber:%s;APN:%s;PDPType:%s;AuthType:%s;", 
    g_qrzl_device_dynamic_data.mcc, g_qrzl_device_dynamic_data.mnc, apn_config_name, apn_username, apn_password, apn_login_number, apn_apn, apn_pdp_type, apn_auth_typee);
    snprintf(send_buffer, buffer_len, "%s,\"apn\":\"%s\"", send_buffer, apn);
    snprintf(send_buffer, buffer_len, "%s,\"Wifi_filter_type\":\"%d\"", send_buffer, g_qrzl_device_dynamic_data.wifi_filter_type);
    snprintf(send_buffer, buffer_len, "%s,\"Blacklist\":\"%s\"", send_buffer, g_qrzl_device_dynamic_data.mac_black_list);
    snprintf(send_buffer, buffer_len, "%s,\"Whitelist\":\"%s\"", send_buffer, g_qrzl_device_dynamic_data.mac_white_list);
    snprintf(send_buffer, buffer_len, "%s,\"CurrentIp\":\"%s\"", send_buffer, g_qrzl_device_dynamic_data.current_wan_ip);
    snprintf(send_buffer, buffer_len, "%s,\"DualSIM\":\"%s\"", send_buffer, g_qrzl_device_static_data.dual_sim);
    int main_sim = 0;
#ifdef QRZL_QICHENG_SWITCHCARD_ZERO_IS_OUTCARD
    if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM1_only") == 0) {
        main_sim = 1;
    } else if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM2_only") == 0) {
        main_sim = 2;
    } else {
        main_sim = 0;
    }
#else
    if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM1_only") == 0) {
        main_sim = 0;
    } else if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM2_only") == 0) {
        main_sim = 1;
    } else {
        main_sim = 2;
    }
#endif
    snprintf(send_buffer, buffer_len, "%s,\"mainSIM\":\"%d\"", send_buffer, main_sim);
    char card_status_list[8];
    // 根据卡槽状态构建卡槽状态列表
    int esim1_status=0;
    int esim2_status=0;
    int esim3_status=0;
    if(strcmp(g_qrzl_device_static_data.nvro_esim1_iccid,"") != 0 && g_qrzl_device_static_data.nvro_esim1_iccid!= NULL) {
        esim1_status = 1; // esim1 有效
    }
    if(strcmp(g_qrzl_device_static_data.nvro_esim2_iccid,"") != 0 && g_qrzl_device_static_data.nvro_esim2_iccid!= NULL) {
        esim2_status = 1; // esim2 有效
    } 
    if (strcmp(g_qrzl_device_static_data.nvro_esim3_iccid,"") != 0 && g_qrzl_device_static_data.nvro_esim3_iccid!= NULL) {
        esim3_status = 1; // esim3 有效
    } 
    snprintf(card_status_list, sizeof(card_status_list), "[%d,%d,%d]", esim1_status, esim2_status, esim3_status);
/*
#ifdef QRZL_ESIM2_ON_SIM_SLOT

        snprintf(card_status_list, sizeof(card_status_list), "[1,0,1]");
#elif QRZL_HAVE_3_ESIM_CARD
        snprintf(card_status_list, sizeof(card_status_list), "[1,1,1]");
#endif
*/
    snprintf(send_buffer, buffer_len, "%s,\"sim_list\":%s", send_buffer, card_status_list);
    snprintf(send_buffer, buffer_len, "%s}", send_buffer);
}
// 构建完整数据包,根据cmd_id觉得是否自动生成
static void qicheng_build_all_data_packet_with_cmdid(zc_data_packet_t *packet, char *body_json, size_t body_json_len, uint32_t cmd_id)
{
    if (!packet || !body_json || body_json_len == 0) return;
    size_t total_body_len = sizeof(uint32_t) + body_json_len;
    packet->body = (char *)malloc(body_json_len);
    if (!packet->body) {
        perror("malloc failed");
        return;
    }
    packet->body_length = total_body_len;
    packet->cmd_id = (cmd_id == 0) ? generate_cmd_id() : cmd_id;  // 如果传入 0，则自动生成
    memcpy(packet->body, body_json, body_json_len);
}
int send_data(int zc_sockfd, zc_data_packet_t *send_packet)
{

    qrzl_log("send_data");
    if (zc_sockfd == -1 || send_packet == NULL ) {
        return -1;
    }
    size_t total_sent = 0;
    ssize_t bytes_sent;
    size_t total_len = sizeof(uint16_t) + send_packet->body_length; // body_length 字段 + 整个body部分
    char *send_message = (char *)malloc(total_len);
    if (!send_message) {
        qrzl_log("malloc failed");
        return -1;
    }
    
    // 将数据拷贝到发送缓冲区
    size_t offset = 0;
     // 拷贝 body_length（2字节）
    //  转换 body_length 为大端格式
    uint16_t net_body_length = htons(send_packet->body_length);
    memcpy(send_message + offset, &net_body_length, sizeof(uint16_t));
    offset += sizeof(uint16_t);
    //  2. 转换 cmd_id 为大端格式
    uint32_t net_cmd_id = htonl(send_packet->cmd_id);
    memcpy(send_message + offset, &net_cmd_id, sizeof(uint32_t));
    offset += sizeof(uint32_t);
    //  3. 拷贝 body
    size_t body_content_len = send_packet->body_length - sizeof(uint32_t);
    memcpy(send_message + offset, send_packet->body, body_content_len);
    offset += body_content_len;
    qrzl_log("total_len: %zu", total_len);
    qrzl_log("send_cmd: %d", send_packet->cmd_id);
    qrzl_log("send_body_len: %zu", send_packet->body_length);
    qrzl_log("send_body: %.*s", send_packet->body_length - sizeof(uint32_t), send_packet->body);

     while (total_sent < total_len) {
        bytes_sent = send(zc_sockfd, send_message + total_sent, total_len - total_sent, 0);
        if (bytes_sent < 0) {
            perror("send error");
            free(send_message);
            return -1;
        }
        total_sent += bytes_sent;
    }
    cache_sent_cmd_id(send_packet->cmd_id);  // <--- 添加这行
    qrzl_log("cmd_id: %u已加入缓存", send_packet->cmd_id);
    free(send_message);
    return total_sent;
}
/*
//此方法为上报一次全部数据
// 表示这是响应服务端时可传入原始 cmd_id，0 表示使本地生成的命令ID
*/
static int send_all_data(uint32_t reuse_cmd_id)
{
    zc_data_packet_t send_datapacket = {};
    qrzl_log("开始上报全部数据");
    int ret;
    char send_buffer[ZC_MSG_MAX_LENGTH] = {0};
    if(zc_sockfd == -1) {
        qrzl_log("zc_sockfd is -1, cannot send data");
        return -1;
    }// 更新设备动态数据
    update_device_dynamic_data();

    // 清空缓冲区
    memset(&send_datapacket, 0, sizeof(send_datapacket));
    memset(send_buffer, 0, sizeof(send_buffer));

    // 构建发送数据
    qicheng_build_send_buffer(send_buffer, sizeof(send_buffer));

    // 构建完整数据包
    qicheng_build_all_data_packet_with_cmdid(&send_datapacket, send_buffer, strlen(send_buffer),reuse_cmd_id);

    pthread_mutex_lock(&send_message_lock);
    if (zc_sockfd != -1) {
        qrzl_log("send_device_status");
        ret = send_data(zc_sockfd, &send_datapacket);
        if (ret > 0) {
            qrzl_log("send_device_status success");
            qrzl_log("总共发送： %d 字节", ret);
            cache_sent_cmd_id(send_datapacket.cmd_id);
            zc_send_serial_number++;
        } else {
            qrzl_log("send_device_status failed");
            qrzl_log("发送失败，断开连接并重置 socket");
            close_and_reset_zc_socket();
            ret = -1;
        }
    }
    pthread_mutex_unlock(&send_message_lock);
    // 释放数据包内存
    zc_free_packet(&send_datapacket);
    return ret;
}
/*
发送空包函数，reuse_cmd_id传0表示不复用上一个命令ID，传别的表示复用上一个命令ID
*/
static int send_empty_packet(uint32_t reuse_cmd_id)
{
    zc_data_packet_t packet = {};
     const char *body = "{}";
     packet.cmd_id = (reuse_cmd_id == 0) ? generate_cmd_id() : reuse_cmd_id;
     packet.body_length = strlen(body) + sizeof(uint32_t);
     packet.body = (char *)malloc(strlen(body));
     if (!packet.body) {
        qrzl_log("send_empty_packet: malloc failed");
        return -1;
    }
    memcpy(packet.body, body, strlen(body));
    pthread_mutex_lock(&send_message_lock);
    int ret = -1;
    if (zc_sockfd != -1) {
        ret = send_data(zc_sockfd, &packet);
    }
    pthread_mutex_unlock(&send_message_lock);
    
    zc_free_packet(&packet);
 
    return ret;
}
/*
发送错误响应函数，reuse_cmd_id传0表示不复用上一个命令ID，传别的表示复用上一个命令ID
*/
int send_error_response(uint32_t reuse_cmd_id,int err_code)
{
    zc_data_packet_t packet = {};
    char body[64] = {0};

    // 构造 JSON 字符串：
    snprintf(body, sizeof(body), "{\"err_code\": \"%d\"}", err_code);

    packet.cmd_id = (reuse_cmd_id == 0) ? generate_cmd_id() : reuse_cmd_id;
    packet.body_length = strlen(body) + sizeof(uint32_t);
    packet.body = (char *)malloc(strlen(body));
    if (!packet.body) {
        qrzl_log("send_error_response: malloc failed");
        return -1;
    }
    memcpy(packet.body, body, strlen(body));

    pthread_mutex_lock(&send_message_lock);
    int ret = -1;
    if (zc_sockfd != -1) {
        qrzl_log("发送错误包 cmd_id=%u, err_code=%d", packet.cmd_id, err_code);
        ret = send_data(zc_sockfd, &packet);
    }
    pthread_mutex_unlock(&send_message_lock);

    zc_free_packet(&packet);
    return ret;
}
static void init_zc_socket()
{
    qrzl_log("init_zc_socket");
    int socketfd;
    int retry_delay = 2;  // 初始延时 2 秒
    const int max_delay = 60;  // 最大延时 60 秒
    while (1)
    {
        while (1)
        {
            socketfd = create_socket(tcp_request_path, tcp_request_port);
            qrzl_log("socketfd: %d\n", socketfd);
            if (socketfd < 0) {
                qrzl_log("连接失败，%d 秒后重试...", retry_delay);
                sleep(retry_delay);
                //更新下次延时时间（2 → 10 → ... → 60）
                retry_delay = (retry_delay * 5< max_delay) ? retry_delay * 5 : max_delay;
            } else {
                zc_sockfd = socketfd;
                // 连接成功，重置延时时间
                retry_delay = 2;
                break;
            }
        }
        size_t i;
        for (i = 0; i < 2; i++)
        {
            if (zc_is_login == 1) {
                //登录成功马上发送一次
                send_all_data(0);
                return;
            } 
            else {
                qrzl_log("未登录(连接),关闭并重置socket");
                close_and_reset_zc_socket();
                break;
            }
        }
        qrzl_log("未知错误,关闭并重置socket");
        close_and_reset_zc_socket();
        
    }   

}

/**
 * 心跳检测，自动重连（目前当作socket初始化）
 */
static void* heartbeat_check_auto_reconn_thread()
{   int retry_delay = 2;  // 初始延时 2 秒
    const int max_delay = 60;  // 最大延时 60 秒
    while (1)
    {   
        if (zc_sockfd == -1) {
            qrzl_log("检测到socket断开,尝试重新连接...");
            init_zc_socket();
        } 
        sleep(1); // 添加适当的休眠，避免CPU占用过高
        /*先不进行心跳
        else {
            if (zc_is_login == 0) {
                sleep(5);
                continue;
            }
            if (send_heartbeat(zc_sockfd) < 1) {
                close_and_reset_zc_socket();
                continue;
            } else {
                send_device_traffic_statistics(zc_sockfd);
                heartbeat_pack_loss_num++;
            }
            if (heartbeat_pack_loss_num >= 2) {
                close_and_reset_zc_socket();
            }
            sleep(300);
        }
        
        */
    }
    return NULL;
}
static int recv_all(int socket, void *buf, size_t length) {
    size_t total = 0;
    ssize_t n;
    uint8_t *p = (uint8_t *)buf;

    while (total < length) {

        n = recv(socket, p + total, length - total, 0);

        if (n > 0) {
            total += n;
        } else if (n == 0) {
            qrzl_log("recv_all: recv returned 0, peer closed the connection.");
            return -2;  // 对端关闭连接
        } else {
            // n < 0, 出错
            if (errno == EINTR) {
                qrzl_log("recv_all: recv was interrupted by signal, retrying...");
                continue;  // 中断可以重试
            } else if (errno == EAGAIN || errno == EWOULDBLOCK) {
                qrzl_log("recv_all: no data available temporarily (EAGAIN/EWOULDBLOCK)");
                return -1;
            } else {
                qrzl_log("recv_all: recv error: %s", strerror(errno));
                return -1;  // 其他错误
            }
        }
    }
    qrzl_log("recv_all: finished receiving %zu bytes", total);
    return 0;
}
static int receive_data(int socket, zc_data_packet_t *packet) {
    uint8_t head_buf[2];

    // 读取 body_length（2字节，大端）
    if (recv_all(socket, head_buf, 2) != 0) {
        return -2;
    }

    packet->body_length = (uint16_t)(head_buf[0] << 8) | head_buf[1];
    if (packet->body_length < 4 || packet->body_length > ZC_MSG_MAX_LENGTH) {
        qrzl_log("Invalid body_length: %u", packet->body_length);
        return -1;
    }

    // 分配 buffer：cmd_id（4字节） + body
    uint8_t *body_buf = (uint8_t *)malloc(packet->body_length);
    if (!body_buf) {
        perror("malloc failed");
        return -1;
    }

    if (recv_all(socket, body_buf, packet->body_length) != 0) {
        free(body_buf);
        return -2;
    }

    // 解析 cmd_id（前 4 字节，大端）
    packet->cmd_id = (uint32_t)(body_buf[0] << 24) | (body_buf[1] << 16) | (body_buf[2] << 8) | body_buf[3];

    // 分配并拷贝 body，不添加 '\0'
    int json_len = packet->body_length - 4;
    if (json_len > 0) {
        packet->body = (char *)malloc(json_len);
        if (!packet->body) {
            free(body_buf);
            perror("malloc failed");
            return -1;
        }
        memcpy(packet->body, body_buf + 4, json_len);
    } else {
        packet->body = NULL;
    }
    free(body_buf);
    qrzl_log("Received: body_length=%u, cmd_id=%u, body=%.*s", 
         packet->body_length, packet->cmd_id, json_len, packet->body);
    return 0;
}
// 处理接收到的数据包
static int data_handler(zc_data_packet_t *packet)
{
    // 通过cmd_id判断是否是我发送的请求收到的是服务端回复我的响应
    if (is_cmd_id_sent_by_me(packet->cmd_id)) {
        qrzl_log("接收到缓存中的cmd_id=%u,跳过处理", packet->cmd_id);
        return 0;
    }else
        qrzl_log("不是缓存中的cmd_id,开始进行数据处理");
    cJSON *value = cJSON_Parse(packet->body);
    if (value == NULL)
    {
        qrzl_err("json value is NULL");
        return -1;
    }
    // 确保顶层是一个对象
    if (!cJSON_IsObject(value))
    {
        qrzl_err("JSON is not an object.\n");
        cJSON_Delete(value);
        return -1;
    }


    int update_wifi_flag = 0;
    int save_nv_flag = 0;
    struct wifi_config_t wifi_config = {};
    init_wifi_config_value(&wifi_config);

    struct mac_filter_config_t mac_filter_config = {};
    init_mac_filter_config_value(&mac_filter_config);

    int restart_flag = 0;
    int reset_flag = 0;
    int update_mac_filter_flag = 0;
    int report_flag = 0;
     // 遍历对象的键值对
    int i;
    cJSON *item = NULL;
    cJSON_ArrayForEach(item, value)
    {
        const char *key = item->string;
        cJSON *val = item;

        if (!cJSON_IsString(val)) 
        {
            // 文档说所有的value都是字符串类型，如果不是字符串类型，那说明有异常，不管这个
            continue;
        }

        /* 限制速度，0 表 示不限速，限制 4G，任意速度， 单位为 Kbps，整数。 例如：256 */
        if (strcmp(key, "limitSpeed") == 0)
        {
            if (strcmp(val->valuestring, "") == 0) {
                continue;
            }
            int limit_speed;
            limit_speed = atoi(val->valuestring);
            if (limit_speed >= 0)
            {   
                save_nv_flag |= 1;
                limit_net_speed(limit_speed, limit_speed);
            }
        } 
        /* 设备下次上报时间间隔，单位 秒 */
        else if (strcmp(key, "nextRptTime") == 0)
        {
            int next_rpt_time;
            next_rpt_time = atoi(val->valuestring);
            if (next_rpt_time != request_interval_time)
            {
                request_interval_time = next_rpt_time;
            }
            
        }
        /* 设备清算时间，datetime 格式，返回时间小于该设置时间时，设备清理缓存数据 */
        else if (strcmp(key, "clrStaticsTime") == 0)
        {
            
        }
        /* 服务器当前时间，datetime格式，用于设备时间校正 */
        else if (strcmp(key, "srvCurrTime") == 0)
        {
            
        }
        /* 设备下次上报流量间隔，单位 kb，暂时这个参 数没有使用，但 是需要预留 */
        else if (strcmp(key, "trafficRptThreshold") == 0)
        {
            
        }
        /* wifi 名称 */
        else if (strcmp(key, "ssidName") == 0)
        {
            update_wifi_flag |= 1;
            snprintf(wifi_config.ssid, sizeof(wifi_config.ssid), "%s", val->valuestring);
        }
        /* wifi 密码 */
        else if (strcmp(key, "ssidPass") == 0)
        {
            if (strlen(val->valuestring) >= 8 && strlen(val->valuestring) < sizeof(wifi_config.key) - 1)
            {
                update_wifi_flag |= 1;
                snprintf(wifi_config.key, sizeof(wifi_config.key), "%s", val->valuestring);
            }
        }
        /* 网络类型 4G/3G/2G/AUTO */
        else if (strcmp(key, "wan_type") == 0)
        {
            
        }
        /* 强制重置设备 1：强制重置 0：不重置 */
        else if (strcmp(key, "force_reset") == 0)
        {
            if (strcmp(val->valuestring, "1") == 0)
            {
                reset_flag |= 1;
            }
        }
        /* WIFI 状态 0 表示关闭，1 表示 打开，2 表示隐藏  SSID */
        else if (strcmp(key, "wifistatus") == 0)
        {
            if (strcmp(val->valuestring, "0") == 0)
            {
                wifi_config.enable = 0;
            }
            else if (strcmp(val->valuestring, "1") == 0)
            {
                wifi_config.enable = 1;
                wifi_config.hide = 0;
            }
            else if (strcmp(val->valuestring, "2") == 0)
            {
                wifi_config.enable = 1;
                wifi_config.hide = 1;
            }
            
            update_wifi_flag |= 1;
        }
        /* WIFI 状态 0 表示关闭，1 表示 打开，2 表示隐藏  SSID */
        else if (strcmp(key, "hiddenSsid") == 0)
        {
            if (strcmp(val->valuestring, "0") == 0)
            {
                wifi_config.hide = 0;
            }
            else if (strcmp(val->valuestring, "1") == 0)
            {
                wifi_config.hide = 1;
            }
            update_wifi_flag |= 1;
        }
        /* 强制重启设备 1 重启，0 不重启 */
        else if (strcmp(key, "Force_restart") == 0 || strcmp(key, "force_restart") == 0)
        {
            if (strcmp(val->valuestring, "1") == 0)
            {
                restart_flag |= 1;
            }
        }
        /* 设备检查新版本升级, 0 不检查，1 检查 */
        else if (strcmp(key, "DeviceUpgrade") == 0)
        {
            
        }
        /* 修改设备管理 端密码 */
        else if (strcmp(key, "webPassword") == 0)
        {
            save_nv_flag |= 1;
            update_web_password(val->valuestring);
        }
        /* 黑白名单模式 设置开启黑名单还 是白名单或都不开 启
0：正常模式
1： 白名单模式
2：黑名单模式
黑白名单模式与对  应列表需组合设置， 否则不生效。 */
        else if (strcmp(key, "Wifi_filter_type") == 0)
        {
            int wifi_filter_type;
            wifi_filter_type = atoi(val->valuestring);
            if (wifi_filter_type >= 0 && wifi_filter_type < 3)
            {   
                update_mac_filter_flag++;
                mac_filter_config.wifi_filter_type = wifi_filter_type;
            }
        }
        /* 设置黑名单 设置接入设备黑名 单
内容为 mac 地址列 表， 以分号相隔
22:06:B0:CA:33:CC ;22:06:B0:CA:33:CD */
        else if (strcmp(key, "Blacklist") == 0)
        {
            update_mac_filter_flag++;
            strlcpy(mac_filter_config.mac_black_list, val->valuestring, sizeof(mac_filter_config.mac_black_list));
        }
        /* 设置白名单
        设置接入设备白名 单
内容为 mac 地址列 表， 以分号相隔
22:06:B0:CA:33:CC ;22:06:B0:CA:33:C D
         */
        else if (strcmp(key, "Whitelist") == 0)
        {
            update_mac_filter_flag++;
            strlcpy(mac_filter_config.mac_white_list, val->valuestring, sizeof(mac_filter_config.mac_white_list));
        }
        /* 设置最大连接数, 最小1, 最大10 */
        else if (strcmp(key, "deviceCountSet") == 0)
        {
            update_wifi_flag |= 1;
            wifi_config.max_access_num = atoi(val->valuestring);
        }
        /* Wifi 加密方式 0：OPEN 1：WPA2(AES)-PSK  2：WPA-PSK/WPA2-PSK*/
        else if (strcmp(key, "apEncrypttype") == 0)
        {
            update_wifi_flag |= 1;
            if (strcmp(val->valuestring, "0") == 0)
            {
                strcpy(wifi_config.auth_mode, "OPEN");
            }
            else if (strcmp(val->valuestring, "1") == 0)
            {
                strcpy(wifi_config.auth_mode, "WPA2PSK");
            }
            else if (strcmp(val->valuestring, "2") == 0)
            {
                strcpy(wifi_config.auth_mode, "WPAPSKWPA2PSK");
            }
        }
        /* 切换卡槽 0 为卡槽 1,1 为卡槽 2（可用来区分实体 卡和贴片卡） */
        else if (strcmp(key, "switchSIM") == 0 || strcmp(key, "SwitchSim") == 0)
        {

            int switch_crad_count = 0;
            while (g_qrzl_device_dynamic_data.is_test_net == 1 && switch_crad_count < 30)
            {
                qrzl_log("正在测网，不能切卡");
                sleep(5);
                switch_crad_count++;
            }

            int current_sim_index = get_device_current_sim_index();
            int target_sim_index = -1;
//想要确保不进行切换到当前卡操作，一定要根据型号来确定切卡的规则，否则永远不会进入实际切卡操作
#ifdef  JCV_HW_MZ804_V1_4
#ifdef QRZL_ESIM2_ON_SIM_SLOT
            if (0 == strcmp(val->valuestring, "0")) {
                target_sim_index = 1;
            } else if (0 == strcmp(val->valuestring, "1")) {
                target_sim_index = 2;
            }
#elif QRZL_HAVE_3_ESIM_CARD
            if (0 == strcmp(val->valuestring, "0")) {
                target_sim_index = 1;
            } else if (0 == strcmp(val->valuestring, "1")) {
                target_sim_index = 2;
            } else if (0 == strcmp(val->valuestring, "2")) {
                target_sim_index = 0;
            }
#endif
#elif JCV_HW_UZ901_V1_6
#ifdef QRZL_ESIM2_ON_SIM_SLOT
            if (0 == strcmp(val->valuestring, "0")) {
                target_sim_index = 1;
            } else if (0 == strcmp(val->valuestring, "1")) {
                target_sim_index = 2;
            }
#endif
#endif
            qrzl_log("当前卡槽索引: %d, 目标卡槽索引: %d\n", current_sim_index, target_sim_index);
            if(target_sim_index != -1 && current_sim_index != target_sim_index)
            {
                //因为后面会断网，所以这里提前发送空包响应
                send_empty_packet(packet->cmd_id); // 发送空包，复用上一个命令ID
#ifdef QRZL_QICHENG_SWITCHCARD_ZERO_IS_OUTCARD
                if (0 == strcmp(val->valuestring, "0"))
                {
                    save_nv_flag |= 1;
                    switch_sim_card_not_restart(0);
                }
                else if (0 == strcmp(val->valuestring, "1"))
                {
                    save_nv_flag |= 1;
                    switch_sim_card_not_restart(1);
                }
                else if (0 == strcmp(val->valuestring, "2"))
                {
                    save_nv_flag |= 1;
                    switch_sim_card_not_restart(2);
                }
#else
                if (0 == strcmp(val->valuestring, "0"))
                {
                    save_nv_flag |= 1;
                    switch_sim_card_not_restart(1);
                }
                else if (0 == strcmp(val->valuestring, "1"))
                {
                    save_nv_flag |= 1;
                    switch_sim_card_not_restart(2);
                }
                else if (0 == strcmp(val->valuestring, "2"))
                {
                    save_nv_flag |= 1;
                    switch_sim_card_not_restart(0);
                }
#endif
                close_and_reset_zc_socket(); // 切换卡槽后需要重置socket
            }
            else
            {
                qrzl_log("当前卡槽与设置卡槽相同，不需要切换");
            }     
        }
        /* 切换频段 例如：band8 则下发字符串 8；不支持组合频段，只能下发单个频段的值，设备未校准频段下发无效 */
        else if (strcmp(key, "band") == 0)
        {
            // 暂时只支持设置为自动
            if (strcmp(val->valuestring, "0") == 0)
            {
                set_lte_net_band(0);
            }
        }
        else if(strcmp(key,"report_now")==0)
        {
            if (strcmp(val->valuestring, "1") == 0)
            {
                // 立即上报
                report_flag |= 1;
                qrzl_log("收到立即上报指令");
                send_all_data(packet->cmd_id);
            }
        }

        // switch (val->type) {
        // case json_string:
        //     qrzl_log("Value: %s (String)\n", val->u.string.ptr);
        //     break;
        // case json_integer:
        //     qrzl_log("Value: %lld (Integer)\n", val->u.integer);
        //     break;
        // case json_boolean:
        //     qrzl_log("Value: %s (Boolean)\n", val->u.boolean ? "true" : "false");
        //     break;
        // default:
        //     qrzl_log("Value: (Unsupported Type)\n");
        // }
    }

    if (update_wifi_flag != 0)
    {
        save_nv_flag |= 1;

        update_wifi_by_config(&wifi_config);
        
    }
    if (update_mac_filter_flag > 0)
    {
        save_nv_flag |= 1;
        update_mac_filter_by_config(&mac_filter_config);
    }
    if (save_nv_flag != 0)
    {
        cfg_save();
    }
    if (restart_flag != 0)
    {
        restart_device();
    }
    if (reset_flag != 0)
    {
        reset_device();
    }
    if(update_wifi_flag|update_mac_filter_flag| restart_flag|reset_flag|save_nv_flag)
    {
        // 发送全部数据
        send_empty_packet(packet->cmd_id); // 发送空包，复用上一个命令ID
        qrzl_log("发送响应 cmd_id=%u", packet->cmd_id);
    }else if(report_flag==0)
    {
        qrzl_log("没有需要更新的配置,发送错误包");
        send_error_response(packet->cmd_id,1);
    }
    
    // Clean up the JSON object
    cJSON_Delete(value);
    return 0;
}



static void* send_heart_socket_data_thread()
{

    qrzl_log("启动心跳");
    while (1)
    {
        if(zc_sockfd == -1) {
            qrzl_log("send_thread_zc_socket=-1,等待建立连接");
            sleep(1); // 等待连接建立
            continue;
        }

       
        qrzl_log("发送心跳包...");
        int ret = send_empty_packet(0);  // 0 表示本地生成 cmd_id

        if (ret > 0) {
            qrzl_log("心跳包发送成功，长度: %d 字节", ret);
            zc_send_serial_number++;
        } else {
            qrzl_log("心跳包发送失败，断开连接并重置 socket");
            close_and_reset_zc_socket();
        }

        sleep(request_interval_time);
    }
    return NULL;
}
static void* receive_socket_data_handler_thread()
{
    zc_data_packet_t receive_datapacket = {};
    int ret;
    last_receive_time = time(NULL); // 初始化最后接收时间
    while (1)
    {   qrzl_log("rec_zc_sockfd: %d", zc_sockfd);
        if (zc_sockfd != -1) {
            qrzl_log("start receive_data");
            memset(&receive_datapacket, 0, sizeof(receive_datapacket));
            ret = receive_data(zc_sockfd, &receive_datapacket);
            if (ret == 0) {
                last_receive_time = time(NULL); // 更新最后接收时间
                data_handler(&receive_datapacket);
            } else if (ret == -2)
            {
                qrzl_log("receive_data error, ret = -2, Server closed the connection/read timeout");
                qrzl_log("接收失败,断开并重置socket");
                close_and_reset_zc_socket();
            }
            qrzl_log("start free_packet");
            zc_free_packet(&receive_datapacket);
            
        }
        // 检查是否超时
        time_t current_time = time(NULL);
        if (current_time - last_receive_time > SERVER_TIMEOUT) {
            qrzl_log("3分钟未收到服务端消息,主动断开连接");
            close_and_reset_zc_socket();
            last_receive_time = current_time; // 重置计时
        }
        sleep(1);
    }
    return NULL;
}
/**
 * 设备状态监听线程，如果设备有些状态发生变化了，就上报服务端
 */
static void* device_status_listeners_thread()
{
    // wifi连接
    char old_station_mac[512] = {0};
    cfg_get_item("station_mac", old_station_mac, sizeof(old_station_mac));
    char new_station_mac[512] = {0};

    char old_wifi_filter_type[2] = {0};
    char old_mac_black_list[180] = {0};
    char old_mac_white_list[180] = {0};

    // mac黑白名单
    cfg_get_item("ACL_mode", old_wifi_filter_type, sizeof(old_wifi_filter_type));
    cfg_get_item("wifi_mac_black_list", old_mac_black_list, sizeof(old_mac_black_list));
    cfg_get_item("wifi_mac_white_list", old_mac_white_list, sizeof(old_mac_white_list));


    char new_wifi_filter_type[2] = {0};
    char new_mac_black_list[180] = {0};
    char new_mac_white_list[180] = {0};
    
    // wifi配置以及基站
    char old_cell_id[16] = {0};
    char old_tac[17] = {0};
    
    cfg_get_item("cell_id", old_cell_id, sizeof(old_cell_id));
    cfg_get_item("tac_code", old_tac, sizeof(old_tac));

    char new_cell_id[16] = {0};
    char new_tac[17] = {0};
    //wifi名与密码
    char old_wifi_ssid[65] = {0};
    char old_wifi_key[65] = {0};


    cfg_get_item("SSID1", old_wifi_ssid, sizeof(old_wifi_ssid));
    cfg_get_item("WPAPSK1", old_wifi_key, sizeof(old_wifi_key));

    char new_wifi_ssid[65] = {0};
    char new_wifi_key[65] = {0};

    while (1)
    {
        cfg_get_item("station_mac", new_station_mac, sizeof(new_station_mac));
        if (strncmp(old_station_mac, new_station_mac, sizeof(old_station_mac)) != 0 && zc_is_login== 1) {
            qrzl_log("station_mac changed");
            //update_device_dynamic_data();
            send_all_data(0);
            cfg_get_item("station_mac", old_station_mac, sizeof(old_station_mac));
        }

        cfg_get_item("ACL_mode", new_wifi_filter_type, sizeof(new_wifi_filter_type));
        cfg_get_item("wifi_mac_black_list", new_mac_black_list, sizeof(new_mac_black_list));
        cfg_get_item("wifi_mac_white_list", new_mac_white_list, sizeof(new_mac_white_list));

        if (strncmp(old_wifi_filter_type, new_wifi_filter_type, sizeof(old_wifi_filter_type)) != 0 ||
            strncmp(old_mac_black_list, new_mac_black_list, sizeof(old_mac_black_list)) != 0 || 
            strncmp(old_mac_white_list, new_mac_white_list, sizeof(old_mac_white_list)) != 0) {
            if (zc_is_login== 1) {
                qrzl_log("wifi filter changed");
                //update_device_dynamic_data();
                send_all_data(0);
                cfg_get_item("ACL_mode", old_wifi_filter_type, sizeof(old_wifi_filter_type));
                cfg_get_item("wifi_mac_black_list", old_mac_black_list, sizeof(old_mac_black_list));
                cfg_get_item("wifi_mac_white_list", old_mac_white_list, sizeof(old_mac_white_list));
            }
        }

        cfg_get_item("SSID1", new_wifi_ssid, sizeof(new_wifi_ssid));
        cfg_get_item("WPAPSK1", new_wifi_key, sizeof(new_wifi_key));

        if( strncmp(old_wifi_ssid, new_wifi_ssid, sizeof(old_wifi_ssid)) != 0 ||
            strncmp(old_wifi_key, new_wifi_key, sizeof(old_wifi_key)) != 0) {
            if (zc_is_login== 1) {
                qrzl_log("wifi ssid or key changed");
                //update_device_dynamic_data();
                send_all_data(0);
                cfg_get_item("SSID1", old_wifi_ssid, sizeof(old_wifi_ssid));
                cfg_get_item("WPAPSK1", old_wifi_key, sizeof(old_wifi_key));
            }
        }
        /*
        cfg_get_item("cell_id", new_cell_id, sizeof(new_cell_id));
        cfg_get_item("tac_code", new_tac, sizeof(new_tac));
        if (strncmp(old_cell_id, new_cell_id, sizeof(old_cell_id)) != 0 ||
        strncmp(old_tac, new_tac, sizeof(new_tac)) != 0) {
            if (zc_is_login== 1) {
                qrzl_log("cell_id or tac_code changed");
                //update_device_dynamic_data();
                send_all_data(0);
                cfg_get_item("cell_id", old_cell_id, sizeof(old_cell_id));
                cfg_get_item("tac_code", old_tac, sizeof(old_tac));
            }
        
        }
        */
        sleep(10);
    }
    
    return NULL;

}
void * qc_tcp_control_start()
{
    int err;
    init_config_data();
    if (pthread_mutex_init(&send_message_lock, NULL) != 0) {
        qrzl_log("send_message_lock init failed\n");
    }
    qrzl_log("开始奇成-TCP接口处理\n");
    update_device_static_data();
    update_device_dynamic_data();
    
    pthread_t heartbeat_check_auto_reconn_tid;
    err = pthread_create(&heartbeat_check_auto_reconn_tid, NULL, heartbeat_check_auto_reconn_thread, NULL);
    if (err != 0) {
        qrzl_log("创建 heartbeat_check_auto_reconn 线程失败, error code: %d", err);
    }

    pthread_t receive_socket_data_handler_thread_tid;
    err = pthread_create(&receive_socket_data_handler_thread_tid, NULL, receive_socket_data_handler_thread, NULL);
    if (err != 0) {
        qrzl_log("创建 receive_socket_data_handler_thread 线程失败, error code: %d", err);
    }
    
    pthread_t send_socket_data_thread_tid;
    err = pthread_create(&send_socket_data_thread_tid, NULL, send_heart_socket_data_thread, NULL);//心跳数据上报
    if (err != 0) {
        qrzl_log("创建 send_socket_data_thread 线程失败, error code: %d", err);
    }
    pthread_t device_status_listeners_tid;
    err = pthread_create(&device_status_listeners_tid, NULL, device_status_listeners_thread, NULL);
    if (err != 0) {
        qrzl_log("创建 device_status_listeners 线程失败, error code: %d", err);
    }
    pthread_join(heartbeat_check_auto_reconn_tid, NULL);
    pthread_join(receive_socket_data_handler_thread_tid, NULL);
    return NULL;
}
