#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <signal.h>
#include <errno.h>


#include "jijia_http_control.h"
#include "../common_utils/cjson.h"
#include "../common_utils/md5.h"
#include "../qrzl_utils.h"
#include "curl/curl.h"
#include "softap_api.h"
#include "nv_api.h"

#define QRZL_HTTP_RESPONSE_MAX 5120
#define QRZL_HTTP_REQUEST_BODY_MAX 2048
#define HASH_SIZE 16

extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

/* 请求间隔时间 */
static int request_interval_time = 60;

// client ID
static char *client_id = "1101";

// Skey  用于生成签名
static char *skey = "6113c78aaf33865387f1c70bdbba97ac";

// Sign
static unsigned char global_sign_hash[33] = {0};
static unsigned char open_sign_hash[33] = {0};
static unsigned char update_sign_hash[33] = {0};
static unsigned char chsim_sign_hash[33] = {0};

// 当前上网模式
static u_int8_t mode_type = -1;

// http请求地址
static char http_request_path[256] = {0};

// 字符串拼接函数
void str_append(char *str, size_t max_len, const char *format, ...)
{
    va_list args;
    va_start(args, format);

    size_t len = strlen(str);
    vsnprintf(str + len, max_len - len, format, args);

    va_end(args);
}

// HTTP回调函数，用于处理HTTP响应
static size_t http_write_callback(void *contents, size_t size, size_t nmemb, void *userp)
{
    size_t totalSize = size * nmemb;
    char *response = (char *)userp;

    size_t currentLen = strlen(response);
    size_t remaining = QRZL_HTTP_RESPONSE_MAX - currentLen - 1; // 预留 '\0'

    if (remaining <= 0)
    {
        qrzl_log("http返回值已满，不能再写入\n");
        return 0;
    }

    size_t copyLen = totalSize < remaining ? totalSize : remaining;
    strncat(response, (char *)contents, copyLen);

    return totalSize;
}

// 发送GET请求
static int http_send_get_request(const char *url, char *response, char *m)
{
    CURL *curl;
    CURLcode res;
    qrzl_log("JIJIA -> http request url: %s\n", url);

    curl = curl_easy_init();
    if (curl)
    {
        struct curl_slist *headers = NULL;
        // 添加请求头
        if (m != NULL)
        {
            char header_buf[QRZL_HTTP_REQUEST_BODY_MAX + 10];  // 足够大，留出 "m: " 前缀
            snprintf(header_buf, sizeof(header_buf), "m: %s", m);

            headers = curl_slist_append(headers, header_buf);
            qrzl_log("JIJIA -> hearders: %s", header_buf);
            curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        }
        // curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
        curl_easy_setopt(curl, CURLOPT_URL, url);
        // curl_easy_setopt(curl, CURLOPT_POST, 1L);
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 30L);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
        res = curl_easy_perform(curl);
        if (res != CURLE_OK)
        {
            qrzl_log("curl_easy_perform() failed: %s\n", curl_easy_strerror(res));
        }
        else
        {
            qrzl_log("JIJIA -> http Response: %s\n", response);
        }
        if (headers) {
            curl_slist_free_all(headers);
        }
        curl_easy_cleanup(curl);
        if (res != CURLE_OK)
        {
            response[0] = '\0';  // 清空
            return -1;
        }
        return 0;
    }
    return -1;
}

// skey+c+devid
void init_global_interface_sign()
{
    int i;
    unsigned char temp_hash[HASH_SIZE] = {0};
    lpa_MD5_CTX md5ctx = {0};

    lpa_MD5_Init(&md5ctx);
    lpa_MD5_Update(&md5ctx, skey, strlen(skey));
    lpa_MD5_Update(&md5ctx, client_id, strlen(client_id));
    lpa_MD5_Update(&md5ctx, g_qrzl_device_static_data.imei, strlen(g_qrzl_device_static_data.imei));
    lpa_MD5_Final(temp_hash, &md5ctx);

    for (i = 0; i < HASH_SIZE; i++)
    {
        sprintf(global_sign_hash + i * 2, "%02x", temp_hash[i]);
    }
    qrzl_log("global_sign_hash: %s \n", global_sign_hash);
}

// skey+devid
void init_open_sign()
{
    int i;
    unsigned char temp_hash[HASH_SIZE] = {0};
    lpa_MD5_CTX md5ctx = {0};

    lpa_MD5_Init(&md5ctx);
    lpa_MD5_Update(&md5ctx, skey, strlen(skey));
    lpa_MD5_Update(&md5ctx, g_qrzl_device_static_data.imei, strlen(g_qrzl_device_static_data.imei));
    lpa_MD5_Final(temp_hash, &md5ctx);
    for (i = 0; i < HASH_SIZE; i++)
    {
        sprintf(open_sign_hash + i * 2, "%02x", temp_hash[i]);
    }
    qrzl_log("open_sign_hash: %s \n", open_sign_hash);
}

// skey+ devid + mode
void init_chsim_sign()
{
    int i;
    unsigned char temp_hash[HASH_SIZE] = {0};
    lpa_MD5_CTX md5ctx = {0};

    lpa_MD5_Init(&md5ctx);
    lpa_MD5_Update(&md5ctx, skey, strlen(skey));
    lpa_MD5_Update(&md5ctx, g_qrzl_device_static_data.imei, strlen(g_qrzl_device_static_data.imei));
    char mode_str[4] = {0};
    snprintf(mode_str, sizeof(mode_str), "%d", mode_type);
    lpa_MD5_Update(&md5ctx, mode_str, strlen(mode_str));
    lpa_MD5_Final(temp_hash, &md5ctx);
    for (i = 0; i < HASH_SIZE; i++)
    {
        sprintf(chsim_sign_hash + i * 2, "%02x", temp_hash[i]);
    }
    qrzl_log("chsim_sign_hash: %s \n", chsim_sign_hash);
}

// skey+devid+wificount
void init_update_sign()
{
    int i;
    unsigned char temp_hash[HASH_SIZE] = {0};
    lpa_MD5_CTX md5ctx = {0};

    lpa_MD5_Init(&md5ctx);
    lpa_MD5_Update(&md5ctx, skey, strlen(skey));
    lpa_MD5_Update(&md5ctx, g_qrzl_device_static_data.imei, strlen(g_qrzl_device_static_data.imei));
    char conn_num[10] = {0};
    snprintf(conn_num, sizeof(conn_num), "%d", g_qrzl_device_dynamic_data.conn_num);
    lpa_MD5_Update(&md5ctx, conn_num, strlen(conn_num));
    lpa_MD5_Final(temp_hash, &md5ctx);
    for (i = 0; i < HASH_SIZE; i++)
    {
        sprintf(update_sign_hash + i * 2, "%02x", temp_hash[i]);
    }
    qrzl_log("update_sign_hash: %s \n", update_sign_hash);
}

// 禁用网络并关机
static void disable_net_and_shutdown()
{
    //禁用网络(一次性，重启恢复，此处不用set_network_br0_disconnect的原因是，只检测是否禁用，默认都是开放的，避免出现开放处理错误导致用户使用体验不佳的情况)
    // set_network_br0_disconnect(1);
    qrzl_log("JIJIA -> disable network.  Restart Recovery");
    // 添加规则：禁止 br0 -> wan1 转发
    system("iptables-save | grep -q -- '-A FORWARD -i br0 -o wan1 -j DROP' || iptables -I FORWARD -i br0 -o wan1 -j DROP");
    // 添加规则：禁止 wan1 -> br0 转发
    system("iptables-save | grep -q -- '-A FORWARD -i wan1 -o br0 -j DROP' || iptables -I FORWARD -i wan1 -o br0 -j DROP");
    // IPv6 添加规则：禁止 br0 -> wan1 转发
    system("ip6tables-save | grep -q -- '-A FORWARD -i br0 -o wan1 -j DROP' || ip6tables -I FORWARD -i br0 -o wan1 -j DROP");
    // IPv6 添加规则：禁止 wan1 -> br0 转发
    system("ip6tables-save | grep -q -- '-A FORWARD -i wan1 -o br0 -j DROP' || ip6tables -I FORWARD -i wan1 -o br0 -j DROP");
#ifndef QRZL_NET_DISABLE_NO_CHANGE_LED
#ifdef QRZL_NET_READCARDREDON_CONNECTEDGREENON_DISABLENETGREENBLINK
    system("echo timer > /sys/class/leds/modem_g_led/trigger"); // 打开网络绿灯定时器
    system("echo 1 > /sys/class/leds/modem_g_led/brightness"); // 打开网络绿灯
#else
    system("echo none > /sys/class/leds/modem_g_led/trigger"); // 关闭网络绿灯定时器
    system("echo 0 > /sys/class/leds/modem_g_led/brightness"); // 关闭网络绿灯
    system("echo none > /sys/class/leds/modem_r_led/trigger"); // 关闭网络红灯定时器
    system("echo 1 > /sys/class/leds/modem_r_led/brightness"); // 打开网络红灯
#endif
#endif

#ifndef JCV_HW_UZ901_V1_4
    // 带电源（UFI之外的设备） 关机
    //shutdown_device();
#endif
}

int handler_global_msg(char *request_body)
{
    cJSON *res_body_json = cJSON_Parse(request_body);
    if (res_body_json == NULL || !cJSON_IsObject(res_body_json)) {
        qrzl_log("全局调度接口返回值不是一个JSON数据或者不存在");
        if (res_body_json) cJSON_Delete(res_body_json);
        return -1;
    }

    cJSON *code_json = cJSON_GetObjectItem(res_body_json, "code");

    if (code_json == NULL || !cJSON_IsNumber(code_json)) {
        qrzl_log("全局调度接口返回值中 code 不是一个整数或者不存在");
        cJSON_Delete(res_body_json);
        return -1;
    }

    if (code_json->valueint == 1) {
        // 不允许设备上网
        set_network_br0_disconnect(1);
    } else if (code_json->valueint == 0) {

        cJSON *data_json = cJSON_GetObjectItem(res_body_json, "data");
        if (data_json == NULL || !cJSON_IsObject(data_json)) {
            qrzl_log("全局调度接口返回值中 data 不是一个JSON数据或者不存在");
            cJSON_Delete(res_body_json);
            return -1;
        }

        cJSON *address_json = cJSON_GetObjectItem(data_json, "address");
        cJSON *port_json = cJSON_GetObjectItem(data_json, "port");
        if (!cJSON_IsString(address_json) || strlen(address_json->valuestring) <= 0) {
            qrzl_log("全局调度接口返回值中 address 不是字符串，或者为空");
            cJSON_Delete(res_body_json);
            return -1;
        }
        if (!cJSON_IsString(port_json) || strlen(port_json->valuestring) <= 0) {
            qrzl_log("全局调度接口返回值中 port 不是字符串，或者为空");
            cJSON_Delete(res_body_json);
            return -1;
        }

        // 拼接完整请求地址
        str_append(http_request_path, sizeof(http_request_path), "%s:%s", address_json->valuestring, port_json->valuestring);
        qrzl_log("远程请求地址: %s", http_request_path);
    }

    cJSON_Delete(res_body_json);
    return 0;
}

int handler_open_msg(char *request_body)
{
    cJSON *res_body_json = cJSON_Parse(request_body);
    if (res_body_json == NULL || !cJSON_IsObject(res_body_json)) {
        qrzl_log("/MiFi/Open 响应的不是一个JSON数据或者不存在");
        if (res_body_json) cJSON_Delete(res_body_json);
        return -1;
    }

    cJSON *code_json = cJSON_GetObjectItem(res_body_json, "code");
    if (code_json == NULL || !cJSON_IsNumber(code_json)) {
        qrzl_log("/MiFi/Open 返回值中 code 不是一个整数或者不存在");
        cJSON_Delete(res_body_json);
        return -1;
    }

    int code = code_json->valueint;
    if (code == 502) {
        disable_net_and_shutdown();
    } else if(code == 0) {
        cJSON *json_str = cJSON_GetObjectItem(res_body_json, "json");
        if (json_str == NULL || !cJSON_IsObject(json_str)) {
            qrzl_log("/MiFi/Open 返回值中 json 不是一个对象或者不存在");
            cJSON_Delete(res_body_json);
            return -1;
        }
        // 获取mode
        cJSON *mode_json = cJSON_GetObjectItem(json_str, "mode");
        if (mode_json == NULL || !cJSON_IsString(mode_json)) {
            qrzl_log("/MiFi/Open 返回值中 mode 不是一个字符串或者不存在");
            cJSON_Delete(res_body_json);
            return -1;
        }

        // 赋值全局变量
        mode_type = atoi(mode_json->valuestring);

        if (mode_type == 2) {
            // 内置卡1
            switch_sim_card_not_restart(1);
        } else if (mode_type == 4) {
            // 内置卡2
            switch_sim_card_not_restart(2);
        } else if (mode_type == 3) {
            // 外插卡
            switch_sim_card_not_restart(0);
        } else if (mode_type == 9) {
            // 轮询上网模式
            // 默认是打开的
        }
    }

    cJSON_Delete(res_body_json);
    return 0;
}

// 处理响应
void jijia_update_resp_handler(cJSON *resopne)
{
    cJSON *code_json = cJSON_GetObjectItem(resopne, "code");
    if (!code_json || !cJSON_IsNumber(code_json)) {
        qrzl_log("/MiFi/Update3 返回值中 code 不是一个整数或者不存在");
        return;
    }

    cJSON *json_obj = cJSON_GetObjectItem(resopne, "json");
    if (!json_obj || !cJSON_IsObject(json_obj)) {
        qrzl_log("/MiFi/Update3 返回值中 json 不是一个对象或者不存在");
        return;
    }

    int code = code_json->valueint;
    cJSON *json_info = cJSON_GetObjectItem(json_obj, "info");

    switch (code) {
        case 0:
            qrzl_log("/MiFi/Update3 信息上报成功!");
            break;
        case 502:
            disable_net_and_shutdown();
            break;

        case 506: { // 限速
            if (!json_info || !cJSON_IsString(json_info)) {
                qrzl_log("/MiFi/Update3 返回值中 限速 info 字段异常");
                return;
            }
            uint64_t limit_value = strtoull(json_info->valuestring, NULL, 10);
            limit_net_speed(limit_value, limit_value);
            break;
        }

        case 507:  // 解除限速
            limit_net_speed(0, 0);
            break;

        case 509:
            restart_device();
            break;

        case 511:  // 修改 WiFi 密码
        case 514: { // 修改 WiFi 名称
            if (!json_info || !cJSON_IsString(json_info)) {
                qrzl_log("/MiFi/Update3 返回值中 WiFi配置 info 字段异常");
                return;
            }
            struct wifi_config_t wifi_config;
            memset(&wifi_config, 0, sizeof(wifi_config));
            init_wifi_config_value(&wifi_config);
            if (code == 511) {
                snprintf(wifi_config.key, sizeof(wifi_config.key), "%s", json_info->valuestring);
            } else {
                snprintf(wifi_config.ssid, sizeof(wifi_config.ssid), "%s", json_info->valuestring);
            }
            update_wifi_by_config(&wifi_config);
            break;
        }

        case 515:
            reset_device();
            break;

        default:
            qrzl_log("非法 code: %d，无法处理。", code);
            break;
    }
}


static void jijia_build_header(cJSON *m)
{
    qrzl_log("start jijia_build_header");
    cJSON_AddStringToObject(m, "c", client_id);

    // 本次开机使用的流量 KB
    char real_time_flow[100] = {0};
    snprintf(real_time_flow, sizeof(real_time_flow), "%llu", g_qrzl_device_dynamic_data.realtime_total_bytes / 1024);
    cJSON_AddStringToObject(m, "flow", real_time_flow);

    // 获取当前的时间
    time_t t = time(NULL);
    struct tm *tm_info = localtime(&t);
    char datetime[20]; 
    // 格式化成：YYYYMMDDHHmmss
    strftime(datetime, sizeof(datetime), "%Y%m%d%H%M%S", tm_info);
    cJSON_AddStringToObject(m, "reptm", datetime);

    cJSON_AddStringToObject(m, "devid", g_qrzl_device_static_data.imei);
    cJSON_AddStringToObject(m, "iccid", g_qrzl_device_dynamic_data.iccid);

    char mode_str[4] = {0};  // 最多3位数 + 终止符
    snprintf(mode_str, sizeof(mode_str), "%d", mode_type);
    cJSON_AddStringToObject(m, "mode", mode_str);

    cJSON_AddStringToObject(m, "db", g_qrzl_device_dynamic_data.lte_rsrp);
    char wificount[10] = {0};
    snprintf(wificount, sizeof(wificount), "%d", g_qrzl_device_dynamic_data.conn_num);
    cJSON_AddStringToObject(m, "wificount", wificount);
    cJSON_AddStringToObject(m, "wifipsw", g_qrzl_device_dynamic_data.wifi_key);
    cJSON_AddStringToObject(m, "ssid", g_qrzl_device_dynamic_data.wifi_ssid);
    cJSON_AddStringToObject(m, "imei", g_qrzl_device_static_data.imei);

    char battery_vol_percent[10] = {0};
    cfg_get_item("battery_vol_percent", battery_vol_percent, sizeof(battery_vol_percent));
    cJSON_AddStringToObject(m, "battery", battery_vol_percent);

    cJSON_AddStringToObject(m, "ssid_5g", "");
    cJSON_AddStringToObject(m, "wifipsw_5g", "");

    // 获取限速值
    uint64_t vspeed = get_up_limit_net_speed();
    char limit_speed[1024] = {0};
    snprintf(limit_speed, sizeof(limit_speed), "%llu", vspeed);
    cJSON_AddStringToObject(m, "vspeed", limit_speed);

    cJSON_AddStringToObject(m, "sign", update_sign_hash);
}

int get_global_info()
{
    
    // 构建JSON请求体
    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    // 初始化全局调度接口  "http://get.mifi.ink/sn/getSN.do?c=1001&devid=10011712260002&sign=5846821e3939e9871cea54f01ea5ab13&btn=7854534524&sim=2799765543&mmc=&mnc=&lac="
    char global_interface_http_url[512] = "http://get.mifi.ink/sn/getSN.do?";
    // 拼接动态数据
    str_append(global_interface_http_url, sizeof(global_interface_http_url), "c=%s&devid=%s&sign=%s&bth=%s&sim=%s&mmc=%s&mnc=%s&lac=%s", 
        client_id, g_qrzl_device_static_data.imei, global_sign_hash, "", g_qrzl_device_dynamic_data.iccid, "", g_qrzl_device_dynamic_data.mnc, g_qrzl_device_dynamic_data.lac);
    // 发送GET请求
    http_send_get_request(global_interface_http_url, request_body, NULL);

    int res = handler_global_msg(request_body);
    return res;
}

/**
 *  说明： mode 上网模式， 2 是内置卡 1， 3 是外插卡模式,4 是内置卡 2， 9 是设备端轮询上网模式
    业务流程：
    1)、模式 2、 4 需要上报流量，模式 3 不需要上报流量。
    2)、如果设备插了外置卡，开机的时候用外置卡上网访问 open 接口。由
    open 接口的返回值决定设备的运行模式。
    3)、如果插了外置卡不能上网，设备端不做处理。
    4)、无论那种模式上网，设备每次开机的时候必须访问 open 接口。
    5)、默认是模式 2 内置电信卡上网模式
    6)、模式 9 是由设备端轮询上网模式
 */
int get_internet_mode()
{
    // 用于接收完整地址
    char open_http_url[256] = {0};
    // 请求接口路径
    char *open_url_suff = "/MiFi/Open";
    // 拼接完整地址
    str_append(open_http_url, sizeof(open_http_url), "%s%s", http_request_path, open_url_suff);
    // 构建请求头m，拼接信息 headers 
    cJSON *m = cJSON_CreateObject();
    cJSON_AddStringToObject(m, "c", client_id);
    cJSON_AddStringToObject(m, "mcc", "");
    cJSON_AddStringToObject(m, "mnc", g_qrzl_device_dynamic_data.mnc);
    cJSON_AddStringToObject(m, "lac", g_qrzl_device_dynamic_data.lac);
    cJSON_AddStringToObject(m, "ci", g_qrzl_device_dynamic_data.cid);
    cJSON_AddStringToObject(m, "devid", g_qrzl_device_static_data.imei);
    cJSON_AddStringToObject(m, "iccid", g_qrzl_device_dynamic_data.iccid);
    cJSON_AddStringToObject(m, "hardvers", g_qrzl_device_static_data.soft_version);
    cJSON_AddStringToObject(m, "db", g_qrzl_device_dynamic_data.lte_rsrp);
    cJSON_AddStringToObject(m, "sign", open_sign_hash);
    // 手动请求的序列化字符串，由 malloc() 分配，需要手动释放。
    char *headers_str_json = cJSON_PrintUnformatted(m);  // 转成紧凑字符串
    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};

    if (headers_str_json) {
        // 发送GET请求
        http_send_get_request(open_http_url, request_body, headers_str_json);
        free(headers_str_json); // 手动释放内部申请的内存
    }
    cJSON_Delete(m); // 释放 JSON 对象

    // 处理返回值
    int hanlder_res = handler_open_msg(request_body);

    return hanlder_res;
}

/**
 * /MiFi/ChSim 接口
 *  目前只用于单纯上报
 */
void push_chsim_info()
{
    // 初始化签名
    init_chsim_sign();
    // 用于接收完整地址
    char chsim_http_url[256] = {0};
    // 请求接口路径
    char *chsim_url_suff = "/MiFi/ChSim";
    // 拼接完整地址
    str_append(chsim_http_url, sizeof(chsim_http_url), "%s%s", http_request_path, chsim_url_suff);
    // 构建请求头m，拼接信息 headers 
    cJSON *m = cJSON_CreateObject();
    cJSON_AddStringToObject(m, "c", client_id);
    cJSON_AddStringToObject(m, "devid", g_qrzl_device_static_data.imei);
    char mode_str[4] = {0};  // 最多3位数 + 终止符
    snprintf(mode_str, sizeof(mode_str), "%d", mode_type);
    cJSON_AddStringToObject(m, "mode", mode_str);
    cJSON_AddStringToObject(m, "sign", chsim_sign_hash);
    // 手动请求的序列化字符串，由 malloc() 分配，需要手动释放。
    char *headers_str_json = cJSON_PrintUnformatted(m);  // 转成紧凑字符串
    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};

    if (headers_str_json) {
        // 发送GET请求
        http_send_get_request(chsim_http_url, request_body, headers_str_json);
        free(headers_str_json); // 手动释放内部申请的内存
    }
    cJSON_Delete(m); // 释放 JSON 对象

    // 暂时没有处理request_body的要求，只是上报
}

/**
 * 初始化一些配置信息 
 * */
static int init_config_data() 
{
    int cfg_ret;

    char cloud_request_interval_time[10] = {0};
    cfg_get_item(NV_QRZL_CLOUD_REQUEST_INTERVAL_TIME, cloud_request_interval_time, 10);
    request_interval_time = atoi(cloud_request_interval_time);
    if (request_interval_time == 0) {
        request_interval_time = 300;
    }

    return 0;
}

void jijia_start_process()
{
    int ret;
    char http_response[QRZL_HTTP_RESPONSE_MAX] = {0}; // 存储响应体

    update_device_dynamic_data();

    //push_chsim_info();

    // 构建请求头
    cJSON *m = cJSON_CreateObject();
    jijia_build_header(m);

    char *headers_str_json = cJSON_PrintUnformatted(m);  // 序列化
    cJSON_Delete(m); // 提前释放 JSON 对象

    // 构造请求 URL
    char update_http_url[256] = {0};
    str_append(update_http_url, sizeof(update_http_url), "%s%s", http_request_path, "/MiFi/Update3");

    if (headers_str_json) {
        ret = http_send_get_request(update_http_url, http_response, headers_str_json);
        free(headers_str_json);
    } else {
        qrzl_err("headers_str_json is NULL");
        return;
    }

    if (ret != 0 || strlen(http_response) == 0 || http_response[0] != '{') {
        qrzl_err("HTTP 请求失败或响应不是 JSON: %s", http_response);
        return;
    }

    cJSON *value = cJSON_Parse(http_response);
    if (!value || !cJSON_IsObject(value)) {
        qrzl_err("响应不是合法 JSON 对象: %s", http_response);
        if (value) cJSON_Delete(value);
        return;
    }

    jijia_update_resp_handler(value);
    cJSON_Delete(value);
}



/**
 *  积嘉 通信协议 主线程
 */
void* jijia_http_control_start()
{
    int ret;
    ret = init_config_data();
    if (ret != 0)
    {
        qrzl_err("初始化数据失败，请检查配置是否正确");
        return;
    }

    // 防止开机时一开始没网
    int i;
    for (i = 0; i < 3; i++) {
        if (check_network() == 0) {
            break;
        }
    }
    // 更新动/静态信息
    update_device_static_data();
    update_device_dynamic_data();

    // 调用全局接口获取信息
    int global_res;
    // 初始化全局调度接口签名
    init_global_interface_sign();
    while ((global_res = get_global_info()) < 0)
    {
        qrzl_log("全局调度接口异常, 5s后重试...");
        sleep(5);
    }

    // 获取 mode 上网模式
    int mode_res;
    // 初始化open接口的签名
    init_open_sign();
    while ((mode_res = get_internet_mode()) < 0)
    {
        qrzl_log("获取mode上网模式超时或异常，5分钟后重试...");
        sleep(300); // 五分钟重试
    }

    while (1) 
    {
        init_update_sign(); // 初始化 update 签名
        jijia_start_process();
        if (request_interval_time <= 0) {
            qrzl_log("request_interval_time is 0, cshttp_control end");
            break;
        }
        qrzl_log("request_interval_time: %d s", request_interval_time);
        sleep(request_interval_time);
    }
    return NULL;
}