#define _GNU_SOURCE
#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <signal.h>
#include <errno.h>
#include <pthread.h>
#include <curl/curl.h>
#include <fcntl.h>      // 非阻塞IO设置 

#include "../common_utils/cjson.h"
#include "one_link_http_control.h"
#ifdef QRZL_AUTH_WUXING
#include "../auth_control/wuxing_auth_control.h"
#endif
#ifdef QRZL_AUTH_JIUYAO
#include "../auth_control/jiuyao_auth_control.h"
#endif
#ifdef QRZL_AUTH_XUNYOU
#include "../auth_control/xunyou_auth_control.h"
#endif
#ifdef QRZL_AUTH_JNZY
#include "../auth_control/jnzy_auth_control.h"
#endif
#ifdef QRZL_AUTH_CHUANGSAN
#include "../auth_control/chuangsan_auth_control.h"
#endif
#ifdef QRZL_AUTH_BEIWEI
#include "../auth_control/beiwei_auth_control.h"
#endif
#ifdef QRZL_AUTH_MY
#include "../auth_control/my_auth_control.h"
#endif
#include "../qrzl_utils.h"
#include "softap_api.h"
#include "nv_api.h"


extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

#define QRZL_HTTP_RESPONSE_MAX 5120
#define QRZL_HTTP_REQUEST_BODY_MAX 2048

#define LOCAL_AUTH_MAX_SIZE 5120  // 本地存储的字符串最长长度

#define KEY_ONE_LINK_GET_TOKEN_URL       "ONE_LINK_customers_get_token_url"
#define KEY_ONE_LINK_URL                 "ONE_LINK_auth_http_url"
#define KEY_ONE_LINK_PORT                "ONE_LINK_auth_http_port"
#define KEY_ONE_LINK_CUSTOMER_INFO       "ONE_LINK_customer_info"
#define KEY_ONE_LINK_AUTH_APPID          "ONE_LINK_auth_appid"
#define KEY_ONE_LINK_AUTH_PASSWORD          "ONE_LINK_auth_password"

#define ONE_LINK_LOCAL_AUTH_INFO    "one_link_authed_mac"
#define CMP_LOCAL_AUTH_INFO         "cmp_authed_mac"
#define UNINET_LOCAL_AUTH_INFO      "uninet_authed_mac"

// 上一次的 MAC 列表（分号分隔）
static char last_station_mac[2048] = {0};  
static pthread_mutex_t last_station_mac_mutex = PTHREAD_MUTEX_INITIALIZER;

static char one_link_auth_http_url[256] = {0};
static char one_link_auth_http_port[10] = {0};
static char one_link_customers_get_token_url[256] = {0};
static char one_link_customer_info[50] = {0};

static char appid[125];
static char auth_password[125];

// 序列号
static unsigned int sequence = 1;
// 事务编码存储
static char transid[1024];
// TOKEN
static char token[1024];

static int update_local_auth_info_for_onelink_api();

// 拼接str工具函数
static void safe_str_append(char *str, size_t max_len, const char *format, ...) {
    va_list args;
    va_start(args, format);

    size_t len = strlen(str);
    vsnprintf(str + len, max_len - len, format, args);

    va_end(args);
}

/**
 * 初始化一些配置信息 
 * */
static int init_config_data() 
{
    cfg_get_item(KEY_ONE_LINK_CUSTOMER_INFO, one_link_customer_info, sizeof(one_link_customer_info));
    if (strlen(one_link_customer_info) == 0) {
        qrzl_log("Configuration item %s is missing !", KEY_ONE_LINK_CUSTOMER_INFO);
    }

    cfg_get_item(KEY_ONE_LINK_URL, one_link_auth_http_url, sizeof(one_link_auth_http_url));
    if (strlen(one_link_auth_http_url) == 0) {
        qrzl_log("Configuration item %s is missing !", KEY_ONE_LINK_URL);
    }

    cfg_get_item(KEY_ONE_LINK_PORT, one_link_auth_http_port, sizeof(one_link_auth_http_port));
    if (strlen(one_link_auth_http_port) == 0) {
        qrzl_log("Configuration item %s is missing !", KEY_ONE_LINK_PORT);
    }

    cfg_get_item(KEY_ONE_LINK_GET_TOKEN_URL, one_link_customers_get_token_url, sizeof(one_link_customers_get_token_url));
    if (strlen(one_link_customers_get_token_url) == 0) {
        qrzl_log("Configuration item %s is missing !", KEY_ONE_LINK_GET_TOKEN_URL);
    }

    return 0;
}

// 生成事务编码
void generate_transid(const char *appid, unsigned int sequence, char *transid_out, size_t out_size) {
    if (strlen(appid) + 14 + 8 + 1 > out_size) {
        fprintf(stderr, "ONELINK generate_transid -> 输出缓冲区太小");
        return;
    }

    time_t t = time(NULL);
    struct tm *tm_info = localtime(&t);
    char datetime[15]; // YYYYMMDDHHMISS + '\0'
    strftime(datetime, sizeof(datetime), "%Y%m%d%H%M%S", tm_info);

    snprintf(transid_out, out_size, "%s%s%08u", appid, datetime, sequence);
}

// HTTP回调函数，用于处理HTTP响应
static size_t http_write_callback(void *contents, size_t size, size_t nmemb, void *userp) {
    size_t totalSize = size * nmemb;
    char *response = (char *)userp;

    size_t currentLen = strlen(response);
    size_t remaining = QRZL_HTTP_RESPONSE_MAX - currentLen - 1; // 预留 '\0'

    if (remaining <= 0) {
        qrzl_log("http返回值已满，不能再写入");
        return 0;
    }

    size_t copyLen = totalSize < remaining ? totalSize : remaining;
    strncat(response, (char *)contents, copyLen);

    return totalSize;
}

// 发送GET请求
static int http_send_get_request(const char *url, char *response) {
    CURL *curl;
    CURLcode res;
    qrzl_log("ONE LINK -> http request url: %s", url);

    curl = curl_easy_init();
    if (curl) {
        // curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
        curl_easy_setopt(curl, CURLOPT_URL, url);
        // curl_easy_setopt(curl, CURLOPT_POST, 1L);
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L); 
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
        res = curl_easy_perform(curl);
        if (res != CURLE_OK) {
            qrzl_log("curl_easy_perform() failed: %s", curl_easy_strerror(res));
        } else {
            qrzl_log("http Response: %s", response);
        }
        curl_easy_cleanup(curl);
        if (res != CURLE_OK) {
            return -1;
        }
        return 0;
    }
    return -1;
}

// 通用的POST请求函数
int https_send_post_request(const char *url, const char* body, char *response)
{
    CURL *curl;
    CURLcode res;

    qrzl_log("ONELINK https_send_post_request -> 当前请求地址：%s", url);
    qrzl_log("ONELINK https_send_post_request -> 当前请求体：%s", body);

    // 初始化curl
    curl = curl_easy_init();

    if(curl == NULL) {
        qrzl_log("ONELINK -> init CURL failed!!");
        return -1;
    }
    // 设置SSL
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);
    // 设置请求地址
    curl_easy_setopt(curl, CURLOPT_URL, url);
    // 使用post方式请求
    curl_easy_setopt(curl, CURLOPT_POST, 1L);
    // 设置响应超时时间
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
    // 设置连接超时时间
    curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L);
    // 回调函数
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
    // 设置POST请求的内容
    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, body);
    // 设置HTTP头
    struct curl_slist *headers = NULL;
    headers = curl_slist_append(headers, "Content-Type: application/json"); // （告诉服务器这是JSON数据）
    // 设置公共参数
    curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
    // 设置 接收请求响应的内容
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
    // 正式发起请求
    res = curl_easy_perform(curl);

    // 处理返回值
    if(res != CURLE_OK) {
        qrzl_log("ONELINK ->  curl_easy_perform request failed!");
    }

    qrzl_log("ONELINK -> response: %s", response);

    // 清理内存
    curl_slist_free_all(headers);
    curl_easy_cleanup(curl);

    if (res != CURLE_OK)
    {
        return -1;
    }

    return 0;
}

/**
 * 盛世联运的获取token的接口
 */
void shengshi_get_token_code()
{
    // 接收请求后返回的JSON数据
    char http_response_content[QRZL_HTTP_RESPONSE_MAX] = {0};
    // api方法请求路径，此方法是被客户封装了的，与上报服务器的地址不一致, 后续客户可能没有此接口给我们使用，到时候需要自己调用原生接口，然后进行封装
    char http_get_token_request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    // 拼接完整请求体
    safe_str_append(http_get_token_request_url, sizeof(http_get_token_request_url), "%s?appid=%s", one_link_customers_get_token_url, appid);

    qrzl_log("获取Token 请求地址:%s", http_get_token_request_url);

    // 发送Get请求
    int res_spr;
    res_spr = http_send_get_request(http_get_token_request_url, http_response_content);
    if(res_spr != 0) {
        qrzl_log("请求异常，请重试!");
        return ;
    }

    qrzl_log("get_token_code 请求成功!");
    qrzl_log("get_token_code 响应内容：%s",http_response_content);

    // 通过cJSON解析响应数据
    cJSON *root = cJSON_Parse(http_response_content);
    if (root == NULL || root->type != cJSON_Object){
        qrzl_log("get_token_code 解析失败");
        return ;
    }
    
    cJSON *result = cJSON_GetObjectItem(root, "result");
    if (result == NULL || result->type != cJSON_Object) {
        qrzl_log("get_token_code 找不到 result 字段");
        cJSON_Delete(root);
        return ;
    }

    cJSON *token_item = cJSON_GetObjectItem(result, "token");
    if (token_item != NULL && token_item->type == cJSON_String){
        qrzl_log("get_token_code -> Token: %s", token_item->valuestring);
        snprintf(token, sizeof(token), "%s", token_item->valuestring);
    } else {
        qrzl_log("找不到 token 或 token 不是字符串");
    }

    cJSON_Delete(root); // 释放 JSON 对象， 只需要释放最外层，内部会自动释放内层的对象，例如result、token_item
}

/*
*星光获取token的接口
*/
void xingguang_get_token_code(){
        // 接收请求后返回的JSON数据
    char http_response_content[QRZL_HTTP_RESPONSE_MAX] = {0};
    // api方法请求路径，此方法是被客户封装了的，与上报服务器的地址不一致, 后续客户可能没有此接口给我们使用，到时候需要自己调用原生接口，然后进行封装
    char http_get_token_request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    // 拼接完整请求体
    safe_str_append(http_get_token_request_url, sizeof(http_get_token_request_url), "%s?iccid=%s", one_link_customers_get_token_url, g_qrzl_device_dynamic_data.iccid);

    qrzl_log("获取Token 请求地址:%s", http_get_token_request_url);

    // 发送Get请求
    int res_spr;
    res_spr = http_send_get_request(http_get_token_request_url, http_response_content);
    if(res_spr != 0) {
        qrzl_log("请求异常，请重试!");
        return ;
    }

    qrzl_log("get_token_code 请求成功!");
    qrzl_log("get_token_code 响应内容：%s",http_response_content);

    // 通过cJSON解析响应数据
    cJSON *root = cJSON_Parse(http_response_content);
    if (root == NULL || root->type != cJSON_Object){
        qrzl_log("get_token_code 解析失败");
        return ;
    }
    
    cJSON *result = cJSON_GetObjectItem(root, "result");
    if (result == NULL || result->type != cJSON_Object) {
        qrzl_log("get_token_code 找不到 result 字段");
        cJSON_Delete(root);
        return ;
    }

    cJSON *auth_verify_item = cJSON_GetObjectItem(result, "auth_verify");
    if (auth_verify_item != NULL && (auth_verify_item->type == cJSON_True || auth_verify_item->type == cJSON_False)){
        qrzl_log("get_token_code -> auth_verify: %s", auth_verify_item->type==cJSON_True?"true":"false");
        if(auth_verify_item->type==cJSON_True){
            cfg_set("qrzl_cloud_authentic_switch", "1");
        }else if(auth_verify_item->type==cJSON_False){
            cfg_set("qrzl_cloud_authentic_switch", "0");
        }
    } else {
        qrzl_log("找不到 auth_verify 或 auth_verify 不是布尔值");
    }

    cJSON *advertise_mode_item = cJSON_GetObjectItem(result, "advertise_mode");
    if (advertise_mode_item != NULL && (advertise_mode_item->type == cJSON_True || advertise_mode_item->type == cJSON_False)){
        qrzl_log("get_token_code -> advertise_mode: %s", advertise_mode_item->type==cJSON_True?"true":"false");
        if(advertise_mode_item->type==cJSON_True){
            cfg_set("qrzl_advertise_mode", "1");
        }else if(advertise_mode_item->type==cJSON_False){
            cfg_set("qrzl_advertise_mode", "0");
        }
    } else {
        qrzl_log("找不到 advertise_mode 或 advertise_mode 不是布尔值");
    }

    cJSON *password_item = cJSON_GetObjectItem(result, "password");
    if (password_item != NULL && password_item->type == cJSON_String){
        qrzl_log("get_token_code -> password: %s", password_item->valuestring);
    } else {
        qrzl_log("找不到 password 或 password 不是字符串");
    }

    cJSON *token_item = cJSON_GetObjectItem(result, "token");
    if (token_item != NULL && token_item->type == cJSON_String){
        qrzl_log("get_token_code -> Token: %s", token_item->valuestring);
        snprintf(token, sizeof(token), "%s", token_item->valuestring);
    } else {
        qrzl_log("找不到 token 或 token 不是字符串");
    }

    cJSON *appid_item = cJSON_GetObjectItem(result, "appid");
    if (appid_item != NULL && appid_item->type == cJSON_String){
        qrzl_log("get_token_code -> Appid: %s", appid_item->valuestring);
        snprintf(appid, sizeof(appid), "%s", appid_item->valuestring);
    } else {
        qrzl_log("找不到 addid 或 appid 不是字符串");
    }


    cJSON_Delete(root); // 释放 JSON 对象， 只需要释放最外层，内部会自动释放内层的对象，例如result、token_item
}

/*
*威宇智通获取token的接口
*/
void weiyuzhitong_get_token_code(){
        // 接收请求后返回的JSON数据
    char http_response_content[QRZL_HTTP_RESPONSE_MAX] = {0};
    // api方法请求路径，此方法是被客户封装了的，与上报服务器的地址不一致, 后续客户可能没有此接口给我们使用，到时候需要自己调用原生接口，然后进行封装
    char http_get_token_request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    // 拼接完整请求体
    safe_str_append(http_get_token_request_url, sizeof(http_get_token_request_url), "%s?iccid=%s", one_link_customers_get_token_url, g_qrzl_device_dynamic_data.iccid);

    qrzl_log("获取Token 请求地址:%s", http_get_token_request_url);

    // 发送Get请求
    int res_spr;
    res_spr = http_send_get_request(http_get_token_request_url, http_response_content);
    if(res_spr != 0) {
        qrzl_log("请求异常，请重试!");
        return ;
    }

    qrzl_log("get_token_code 请求成功!");
    qrzl_log("get_token_code 响应内容：%s",http_response_content);

    // 通过cJSON解析响应数据
    cJSON *root = cJSON_Parse(http_response_content);
    if (root == NULL || root->type != cJSON_Object){
        qrzl_log("get_token_code 解析失败");
        return ;
    }
    cJSON *data = cJSON_GetObjectItem(root, "data");
    if (data == NULL || data->type != cJSON_Object) {
        qrzl_log("get_token_code 找不到 data 字段");
        cJSON_Delete(root);
        return ;
    }
    cJSON *token_item = cJSON_GetObjectItem(data, "token");
    if (token_item != NULL && token_item->type == cJSON_String){
        qrzl_log("get_token_code -> Token: %s", token_item->valuestring);
        snprintf(token, sizeof(token), "%s", token_item->valuestring);
    } else {
        qrzl_log("找不到 token 或 token 不是字符串");
    }

    cJSON *appid_item = cJSON_GetObjectItem(data, "appid");
    if (appid_item != NULL && appid_item->type == cJSON_String){
        qrzl_log("get_token_code -> Appid: %s", appid_item->valuestring);
        snprintf(appid, sizeof(appid), "%s", appid_item->valuestring);
    } else {
        qrzl_log("找不到 addpi 或 appid 不是字符串");
    }


    cJSON_Delete(root); // 释放 JSON 对象， 只需要释放最外层，内部会自动释放内层的对象，例如result、token_item
}


/**
 * 中移API 获取token
 */
int one_link_get_token_code()
{
    // 生成事务编码
    generate_transid(appid, sequence++, transid, sizeof(transid));
    qrzl_log("当前事务编码：%s", transid);
    // 接收请求后返回的JSON数据
    char http_response_content[QRZL_HTTP_RESPONSE_MAX] = {0};
    // api方法请求路径
    char method_suff[] = "/wireless/get/token";
    char http_tmp_url[256] = {0};
    safe_str_append(http_tmp_url, sizeof(http_tmp_url), "%s", one_link_auth_http_url);
    // 拼接完整请求体
    safe_str_append(http_tmp_url, sizeof(http_tmp_url), "%s", method_suff);

    // 构建JSON请求体
    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};

    cJSON *root = cJSON_CreateObject();

    cJSON_AddStringToObject(root, "appid", appid);
    cJSON_AddStringToObject(root, "password", auth_password);
    cJSON_AddStringToObject(root, "transid", transid);
    cJSON_AddStringToObject(root, "refresh", "0");

    // 手动请求的序列化字符串，由 malloc() 分配，需要手动释放。
    char *json_str = cJSON_PrintUnformatted(root);  // 转成紧凑字符串
    if (json_str) {
        strncpy(request_body, json_str, sizeof(request_body) - 1);
        request_body[sizeof(request_body) - 1] = '\0'; // 防止越界
        free(json_str); // 手动释放内部申请的内存
    }

    cJSON_Delete(root); // 释放 JSON 对象

    // 发送POST请求
    int res_spr;
    res_spr = https_send_post_request(http_tmp_url, request_body, http_response_content);

    // 解析结果
    cJSON *response_result = cJSON_Parse(http_response_content);
    if (response_result == NULL || response_result->type != cJSON_Object) {
        qrzl_log("get_token response 解析失败\n");
        cJSON_Delete(response_result);
        return -1;
    }

    cJSON *code = cJSON_GetObjectItem(response_result, "code");
    if (code == NULL || code->type != cJSON_String) {
        qrzl_log("找不到 code 字段 或 不是字符串\n");
        cJSON_Delete(response_result);
        return -1;
    } else {
        if (strcmp("0", code->valuestring) != 0) {
            qrzl_log("code码非正确返回\n");
            cJSON_Delete(response_result);
            return -1;
        }
    }

    cJSON *result = cJSON_GetObjectItem(response_result, "result");
    if (result == NULL || result->type != cJSON_Object) {
        qrzl_log("找不到 result 字段 或 不是对象\n");
        cJSON_Delete(response_result);
        return -1;
    }

    cJSON *j_token = cJSON_GetObjectItem(result, "token");
    if (j_token == NULL || j_token->type != cJSON_String) {
        qrzl_log("找不到 j_token 字段 或 不是字符串\n");
        cJSON_Delete(response_result);
        return -1;
    }

    // token 赋值
    snprintf(token, sizeof(token), "%s", j_token->valuestring);
    qrzl_log("获取token成功：%s", token);
    cJSON_Delete(response_result);
    return 0;
}



// 获取Token
static void get_token_code() {
#ifdef QRZL_AUTH_XUNYOU
    xunyou_get_token_code(appid, sizeof(appid), token, sizeof(token));
#elif defined(QRZL_AUTH_JNZY)
    jnzy_get_token(appid, sizeof(appid), token, sizeof(token));
#elif defined(QRZL_AUTH_SHAYIN)
    shayin_get_token(appid, sizeof(appid), token, sizeof(token));
#elif defined(QRZL_AUTH_CHUANGSAN)
    // TODO chuangsan - no token needed for CHUANGSAN implementation
    qrzl_log("CHUANGSAN customer - no token code needed");
#else
    // Default ORIGIN implementation
    cfg_get_item(KEY_ONE_LINK_AUTH_APPID, appid, sizeof(appid));
    cfg_get_item(KEY_ONE_LINK_AUTH_PASSWORD, auth_password, sizeof(auth_password));
    one_link_get_token_code();
#endif
}

/**
 * 中移API原生发送短信请求
 */
static int send_sms_origin(const char *terminalMac, const char *phoneNum)
{
    // 获取token
    get_token_code();
    // 生成事务编码
    generate_transid(appid, sequence++, transid, sizeof(transid));
    qrzl_log("Token: %s", token);
    qrzl_log("当前事务编码：%s", transid);
    // 接收请求后返回的JSON数据
    char http_response_content[QRZL_HTTP_RESPONSE_MAX] = {0};
    // api方法请求路径
    char method_suff[] = "/wireless/sendSms";
    char http_tmp_url[256] = {0};
    safe_str_append(http_tmp_url, sizeof(http_tmp_url), "%s", one_link_auth_http_url);
    // 拼接完整请求体
    safe_str_append(http_tmp_url, sizeof(http_tmp_url), "%s", method_suff);

    // 构建JSON请求体
    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};

    cJSON *root = cJSON_CreateObject();

    char convert_mac[32] = {0};
    convert_mac_colon_to_dash(g_qrzl_device_static_data.mac, convert_mac, 32);
    cJSON_AddStringToObject(root, "mac", convert_mac); // 设备mac
    char convert_terminalMac[32] = {0};
    convert_mac_colon_to_dash(terminalMac, convert_terminalMac, 32);
    cJSON_AddStringToObject(root, "terminalMac", convert_terminalMac); // 手机端mac
    cJSON_AddStringToObject(root, "phoneNum", phoneNum); // 手机号
    cJSON_AddStringToObject(root, "iccid", g_qrzl_device_dynamic_data.iccid);
    cJSON_AddStringToObject(root, "transid", transid);
    cJSON_AddStringToObject(root, "token", token);

    // 手动请求的序列化字符串，由 malloc() 分配，需要手动释放。
    char *json_str = cJSON_PrintUnformatted(root);  // 转成紧凑字符串
    if (json_str) {
        strncpy(request_body, json_str, sizeof(request_body) - 1);
        request_body[sizeof(request_body) - 1] = '\0'; // 防止越界
        free(json_str); // 手动释放内部申请的内存
    }

    cJSON_Delete(root); // 释放 JSON 对象

    // 发送POST请求
    int res_spr;
    res_spr = https_send_post_request(http_tmp_url, request_body, http_response_content);

    // 解析结果
    cJSON *response_result = cJSON_Parse(http_response_content);
    if (response_result == NULL || response_result->type != cJSON_Object) {
        qrzl_log("get_sms_code response 解析失败\n");
        cJSON_Delete(response_result);
        return -1;
    }
    
    cJSON *code = cJSON_GetObjectItem(response_result, "code");
    if (code == NULL || code->type != cJSON_String) {
        qrzl_log("one_link_terminal_auth 找不到 code 字段 或 不是字符串\n");
        cJSON_Delete(response_result);
        return -1;
    }

    if(res_spr != 0) {
        qrzl_log("请求异常，请重试!");
        cJSON_Delete(response_result);
        return -1;
    } else if(strcmp("0", code->valuestring) != 0) {
        qrzl_log("请求参数异常!");
        cJSON_Delete(response_result);
        return -2;
    }

    qrzl_log("响应内容：%s\n",http_response_content);
    cJSON_Delete(response_result);

    return 0;
}

// 获取短信验证码
int get_sms_code(char *terminalMac, char *phoneNum)
{
    // 通过nv读取卡，不使用动态数据更新的函数，这个函数会频繁更新不必要的数据
    cfg_get_item("ziccid", g_qrzl_device_dynamic_data.iccid, 21);

#ifdef QRZL_AUTH_CHUANGSAN
    cfg_get_item("ziccid", g_qrzl_device_dynamic_data.iccid, 21);
    return cs_one_link_send_sms(g_qrzl_device_dynamic_data.iccid, g_qrzl_device_static_data.mac, terminalMac, phoneNum);
#elif defined(QRZL_AUTH_BEIWEI)
    return bw_send_sms(terminalMac, phoneNum);
#else
    return send_sms_origin(terminalMac, phoneNum);
#endif
}

/**
 * 中移API 认证
 * @param terminalMac 终端MAC
 * @param phoneNum  手机号
 * @param code 验证码
 * @return 认证结果 0 成功， -1 失败
 */
int orginal_terminal_auth(char *terminalMac, char *phoneNum, char *code)
{
    cfg_get_item("ziccid", g_qrzl_device_dynamic_data.iccid, 21);

    // 获取token
    get_token_code();
    // 生成事务编码
    generate_transid(appid, sequence++, transid, sizeof(transid));
    qrzl_log("Token: %s\n", token);
    qrzl_log("当前事务编码：%s\n", transid);
    // 接收请求后返回的JSON数据
    char http_response_content[QRZL_HTTP_RESPONSE_MAX] = {0};
    // api方法请求路径
    char method_suff[254] = {0};
    safe_str_append(method_suff, sizeof(method_suff), "%s", "/wireless/terminalAuth");
    char http_tmp_url[256] = {0};
    safe_str_append(http_tmp_url, sizeof(http_tmp_url), "%s", one_link_auth_http_url);
    // 拼接完整请求体
    safe_str_append(http_tmp_url, sizeof(http_tmp_url), "%s", method_suff);

    // 构建JSON请求体
    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};

    cJSON *root = cJSON_CreateObject();

    cJSON_AddStringToObject(root, "verifyCode", code);
    char convert_mac[32] = {0};
    convert_mac_colon_to_dash(g_qrzl_device_static_data.mac, convert_mac, 32);
    cJSON_AddStringToObject(root, "mac", convert_mac); // 设备mac
    char convert_terminalMac[32] = {0};
    convert_mac_colon_to_dash(terminalMac, convert_terminalMac, 32);
    cJSON_AddStringToObject(root, "terminalMac", convert_terminalMac); // 手机端mac
    cJSON_AddStringToObject(root, "phoneNum", phoneNum); // 手机号
    cJSON_AddStringToObject(root, "iccid", g_qrzl_device_dynamic_data.iccid);
    cJSON_AddStringToObject(root, "transid", transid);
    cJSON_AddStringToObject(root, "token", token);

    // 手动请求的序列化字符串，由 malloc() 分配，需要手动释放。
    char *json_str = cJSON_PrintUnformatted(root);  // 转成紧凑字符串
    if (json_str) {
        strncpy(request_body, json_str, sizeof(request_body) - 1);
        request_body[sizeof(request_body) - 1] = '\0'; // 防止越界
        free(json_str); // 手动释放内部申请的内存
    }

    cJSON_Delete(root); // 释放 JSON 对象

    // 发送POST请求
    int res_spr;
    res_spr = https_send_post_request(http_tmp_url, request_body, http_response_content);
    if(res_spr != 0) {
        qrzl_log("请求异常，请重试!\n");
        return -1;
    }

    qrzl_log("响应内容：%s\n\n\n",http_response_content);

    // 获取 codeToken
    // 通过cJSON解析响应数据
    cJSON *root_response = cJSON_Parse(http_response_content);
    if (root_response == NULL || root_response->type != cJSON_Object) {
        qrzl_log("one_link_terminal_auth response 解析失败\n");
        cJSON_Delete(root_response);
        return -1;
    }
    
    cJSON *result = cJSON_GetObjectItem(root_response, "result");
    if (result == NULL || result->type != cJSON_Object) {
        qrzl_log("one_link_terminal_auth 找不到 result 字段\n");
        cJSON_Delete(root_response);
        return -1;
    }

    cJSON *codeToken = cJSON_GetObjectItem(result, "codeToken");
    if (codeToken != NULL && codeToken->type == cJSON_String) {
        qrzl_log("one_link_terminal_auth -> codeToken: %s\n", codeToken->valuestring);
        // TODO codeToken 后续回调接口拿到，用于判断该token是否可用
    } else {
        qrzl_log("找不到 codeToken 或 codeToken 不是字符串\n");
        return -1;
    }

    cJSON_Delete(root_response); // 释放 JSON 对象

    // 校验完成之后重新更新本地认证信息
    // update_local_auth_info_for_onelink_api();

    return 0;
}

/**
 * 终端登录验证
 */
int one_link_terminal_auth(char *terminalMac, char *phoneNum, char *code)
{
#ifdef QRZL_AUTH_CHUANGSAN
    cfg_get_item("ziccid", g_qrzl_device_dynamic_data.iccid, 21);
    return cs_one_link_verify(g_qrzl_device_dynamic_data.iccid, g_qrzl_device_static_data.mac, terminalMac, phoneNum, code);
#elif defined(QRZL_AUTH_BEIWEI)
    return bw_terminal_auth(terminalMac, code, phoneNum);
#else
    return orginal_terminal_auth(terminalMac, phoneNum, code);
#endif
}


/**
 * 中移API
 * 终端上下线上报分支处理
 */
void one_link_device_line_report(int push_type, char *terminalMac)
{
    // 获取token
    get_token_code();
    // 生成事务编码
    generate_transid(appid, sequence++, transid, sizeof(transid));
    qrzl_log("Token: %s\n", token);
    qrzl_log("当前事务编码：%s\n", transid);
    // 接收请求后返回的JSON数据
    char http_response_content[QRZL_HTTP_RESPONSE_MAX] = {0};
    // api方法请求路径
    char method_suff[254] = {0};
    if (push_type == 1) {
        safe_str_append(method_suff, sizeof(method_suff), "%s", "/wireless/terminalOnline");
    } else {
        safe_str_append(method_suff, sizeof(method_suff), "%s", "/wireless/terminalOffline");
    }
    char http_tmp_url[256] = {0};
    safe_str_append(http_tmp_url, sizeof(http_tmp_url), "%s", one_link_auth_http_url);
    // 拼接完整请求体
    safe_str_append(http_tmp_url, sizeof(http_tmp_url), "%s", method_suff);

    // 构建JSON请求体
    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};

    cJSON *root = cJSON_CreateObject();

    cJSON_AddStringToObject(root, "transid", transid);
    cJSON_AddStringToObject(root, "token", token);
    char convert_mac[32] = {0};
    convert_mac_colon_to_dash(g_qrzl_device_static_data.mac, convert_mac, 32);
    cJSON_AddStringToObject(root, "mac", convert_mac); // 设备mac
    char convert_terminalMac[32] = {0};
    convert_mac_format(terminalMac, convert_terminalMac, sizeof(convert_terminalMac), '-');
    cJSON_AddStringToObject(root, "terminalMac", convert_terminalMac); // 手机端mac
    // cJSON_AddStringToObject(root, "longitude", "116.4074");
    // cJSON_AddStringToObject(root, "latitude", "39.9052");
    cJSON_AddStringToObject(root, "iccid", g_qrzl_device_dynamic_data.iccid);
    cJSON_AddStringToObject(root, "imei", g_qrzl_device_static_data.imei);
    time_t t = time(NULL);
    struct tm *tm_info = localtime(&t);
    char datetime[25]; // YYYY-MM-DD HH:MM:SS + '\0'
    strftime(datetime, sizeof(datetime), "%Y-%m-%d %H:%M:%S", tm_info);
    cJSON_AddStringToObject(root, "busiTime", datetime);

    // 手动请求的序列化字符串，由 malloc() 分配，需要手动释放。
    char *json_str = cJSON_PrintUnformatted(root);  // 转成紧凑字符串
    if (json_str) {
        strncpy(request_body, json_str, sizeof(request_body) - 1);
        request_body[sizeof(request_body) - 1] = '\0'; // 防止越界
        free(json_str); // 手动释放内部申请的内存
    }

    cJSON_Delete(root); // 释放 JSON 对象


    // 发送POST请求
    int res_spr;
    res_spr = https_send_post_request(http_tmp_url, request_body, http_response_content);
    if(res_spr != 0) {
        qrzl_log("请求异常，请重试!\n");
        qrzl_log("ONE LINK 上报失败！\n\n\n");
        return ;
    }

    // 解析 JSON 响应
    cJSON *json_response = cJSON_Parse(http_response_content);
    if (!json_response || json_response->type != cJSON_Object) {
        qrzl_log("响应格式错误或为空.");
        cJSON_Delete(json_response);
        return;
    }
    // 取code判断上报是否成功，没成功也不用管错误message
    cJSON *json_Code = cJSON_GetObjectItem(json_response, "code");
    if (json_Code && json_Code->type == cJSON_String && strcmp(json_Code->valuestring, "0") == 0) {
        qrzl_log("ONE LINK 上报 %s 成功！\n\n\n", push_type == 0 ? "下线":"上线");
    } else {
        qrzl_log("ONE LINK 上报 %s 失败\n\n\n", push_type == 0 ? "下线":"上线");
    }
    //qrzl_log("ONE LINK 上报 %s 成功！\n\n\n", push_type == 0 ? "下线":"上线");
    return ;
}

 
/**
 * 终端信息上报
 * @param push_type  1 上报上线， 0 上报下线
 * @param terminalMac  终端MAC
 */
void one_link_push_device_info(int push_type, char *terminalMac, char *terminalIp)
{
    // 通过nv读取卡，不使用动态数据更新的函数，这个函数会频繁更新不必要的数据
    cfg_get_item("ziccid", g_qrzl_device_dynamic_data.iccid, 21);

#ifdef QRZL_AUTH_WUXING
    // wuxing接口上报
    wuxing_device_line_type_push(push_type == 1 ? "1" : "2", g_qrzl_device_static_data.mac, terminalMac);
#elif defined(QRZL_AUTH_JIUYAO)
    // jiuyao接口上报
    jiuyao_device_line_type_push(push_type == 1 ? "1" : "2", g_qrzl_device_static_data.mac, terminalMac);
#elif defined(QRZL_AUTH_CHUANGSAN)
    // chuangsan接口上报
    cfg_get_item("ziccid", g_qrzl_device_dynamic_data.iccid, 21);
    cs_one_link_on_offline(push_type, g_qrzl_device_dynamic_data.iccid, g_qrzl_device_static_data.mac, terminalMac, terminalIp);
#elif defined(QRZL_AUTH_BEIWEI)
    bw_report_device_line_state(push_type, terminalMac);
#elif defined(QRZL_AUTH_MY)
    my_report_device_line_state(push_type, terminalMac);
#elif defined(QRZL_AUTH_ONE_LINK_ORIGINAL)
    // 中移原生接口上报
    one_link_device_line_report(push_type, terminalMac);
#else
    
#endif
    
}

/**
 * 获取已认证的列表，原生字符串
 */
void one_link_get_auth_list_origin(char *rev_response)
{   
    // 获取token
    get_token_code();
    // 生成事务编码
    generate_transid(appid, sequence++, transid, sizeof(transid));
    qrzl_log("one_link_get_auth_list -> Token: %s\n", token);
    qrzl_log("one_link_get_auth_list -> 当前事务编码：%s\n", transid);
    // api方法请求路径
    char method_suff[254] = {0};
    safe_str_append(method_suff, sizeof(method_suff), "%s", "/wireless/getTerminalList");
    char http_tmp_url[256] = {0};
    safe_str_append(http_tmp_url, sizeof(http_tmp_url), "%s", one_link_auth_http_url);
    // 拼接完整请求体
    safe_str_append(http_tmp_url, sizeof(http_tmp_url), "%s", method_suff);

    // 构建JSON请求体
    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};

    cJSON *root = cJSON_CreateObject();

    cJSON_AddStringToObject(root, "transid", transid);
    cJSON_AddStringToObject(root, "token", token);
    char convert_mac[32] = {0};
    convert_mac_colon_to_dash(g_qrzl_device_static_data.mac, convert_mac, 32);
    cJSON_AddStringToObject(root, "mac", convert_mac);
    cJSON_AddStringToObject(root, "iccid", g_qrzl_device_dynamic_data.iccid); // 读取当前使用的卡

    // 手动请求的序列化字符串，由 malloc() 分配，需要手动释放。
    char *json_str = cJSON_PrintUnformatted(root);  // 转成紧凑字符串
    if (json_str) {
        strncpy(request_body, json_str, sizeof(request_body) - 1);
        request_body[sizeof(request_body) - 1] = '\0'; // 防止越界
        free(json_str); // 手动释放内部申请的内存
    }

    cJSON_Delete(root); // 释放 JSON 对象


    // 发送POST请求
    int res_spr;
    res_spr = https_send_post_request(http_tmp_url, request_body, rev_response);
    if(res_spr != 0) {
        qrzl_log("one_link_get_auth_list -> request excption!!!\n");
        return ;
    }

    qrzl_log("one_link_get_auth_list-> response context : %s\n",rev_response);
}

// 获取该设备已认证的列表, 处理成字符串
char *one_link_get_auth_list()
{
    cfg_get_item("ziccid", g_qrzl_device_dynamic_data.iccid, 21);

    // 动态字符串构造（初始 4KB，可扩展）
    size_t buffer_size = 5048;
    char *authed_mac_list = malloc(buffer_size);
    if (!authed_mac_list) {
        return NULL;
    }
    authed_mac_list[0] = '\0';

    char http_response_content[QRZL_HTTP_RESPONSE_MAX] = {0};
    one_link_get_auth_list_origin(http_response_content);

    cJSON *response_result = cJSON_Parse(http_response_content);
    if (!response_result || response_result->type != cJSON_Object) {
        qrzl_log("one_link_get_auth_list response 解析失败\n");
        free(authed_mac_list);
        return NULL;
    }

    cJSON *code = cJSON_GetObjectItem(response_result, "code");
    cJSON *message = cJSON_GetObjectItem(response_result, "message");
    if (!code || code->type != cJSON_String || strcmp(code->valuestring, "0") != 0) {
        qrzl_log("one_link_terminal_auth 错误 -> %s\n",
                 message && message->type == cJSON_String ? message->valuestring : "未知错误");
        cJSON_Delete(response_result);
        free(authed_mac_list);
        return NULL;
    }

    cJSON *result = cJSON_GetObjectItem(response_result, "result");
    cJSON *terminal_list = cJSON_GetObjectItem(result, "terminalList");
    if (!terminal_list || cJSON_GetArraySize(terminal_list) == 0) {
        qrzl_log("one_link_get_auth_list  terminalList is empty !");
        cJSON_Delete(response_result);
        return authed_mac_list; // 返回空字符串
    }

    int arr_size = cJSON_GetArraySize(terminal_list);

    // 当前时间戳
    time_t now = time(NULL);
    int i;
    for (i = 0; i < arr_size; i++) {
        cJSON *item = cJSON_GetArrayItem(terminal_list, i);
        if (!item || item->type != cJSON_Object) continue;

        cJSON *item_mac = cJSON_GetObjectItem(item, "terminalMac");
        cJSON *expireTime = cJSON_GetObjectItem(item, "expireTime");
        if (!item_mac || item_mac->type != cJSON_String ||
            !expireTime || expireTime->type != cJSON_String) {
            continue;
        }

        // 转换时间字符串为时间戳
        struct tm tm_exp = {0};
        strptime(expireTime->valuestring, "%Y-%m-%d %H:%M:%S", &tm_exp);
        time_t expire_ts = mktime(&tm_exp);

        if (expire_ts < now) {
            qrzl_log("mac: %s 认证已过期，需重新认证.", item_mac->valuestring);
            continue;
        }

        // 转换mac格式
        char terminal_mac[32] = {0};
        convert_mac_dash_to_colon(item_mac->valuestring, terminal_mac, sizeof(terminal_mac));

        // 拼接到结果字符串
        size_t need_len = strlen(authed_mac_list) + strlen(terminal_mac) + 2;
        if (need_len > buffer_size) {
            buffer_size *= 2;
            char *new_buf = realloc(authed_mac_list, buffer_size);
            if (!new_buf) {
                free(authed_mac_list);
                cJSON_Delete(response_result);
                return NULL;
            }
            authed_mac_list = new_buf;
        }
        strcat(authed_mac_list, terminal_mac);
        strcat(authed_mac_list, ";");
    }

    qrzl_log("one_link_get_auth_list -> authed_mac_list: %s", authed_mac_list);

    cJSON_Delete(response_result);
    return authed_mac_list; // 调用者必须 free()
}

/**
 * 判断mac是否已认证 (未启用该方案)
 * @param mac 终端mac
 * @return 0 未认证； 1 已认证
 */
static int one_link_is_mac_authed(const char *mac)
{
    int ret = 0;

    if (!mac || strlen(mac) == 0) {
        return ret;
    }

    char *authed_list = one_link_get_auth_list();
    if (!authed_list) {
        qrzl_err("无法获取已认证 MAC 列表");
        return ret;
    }

    // 格式化传入的 mac
    char formatted_mac[32] = {0};
    convert_mac_format(mac, formatted_mac, sizeof(formatted_mac), ':');

    // 构造 ";xx:xx:xx:xx:xx:xx;" 格式，避免部分匹配问题
    char search_key[40] = {0};
    snprintf(search_key, sizeof(search_key), ";%s;", formatted_mac);

    qrzl_log("search_key: %s", search_key);

    // 为了统一处理，首尾补上 ';'
    size_t list_len = strlen(authed_list);
    char *wrapped_list = malloc(list_len + 3);
    if (!wrapped_list) {
        free(authed_list);
        return ret;
    }
    snprintf(wrapped_list, list_len + 3, ";%s", authed_list);

    qrzl_log("wrapped_list: %s", wrapped_list);

    if (strstr(wrapped_list, search_key) != NULL) {
        ret = 1;
    }

    free(authed_list);
    free(wrapped_list);
    return ret;
}

/**
 * 判断 MAC 是否在 本地认证 列表中（分号分隔）
 * @param list 本地认证信息
 * @param mac 终端mac
 * @return 0 不存在， 1 存在
 */
static int mac_in_local_auth_list(const char *list, const char *mac) {
    if (!list || !mac) return 0;

    const char *p = list;
    size_t mac_len = strlen(mac);
    while (p && *p) {
        const char *sep = strchr(p, ';');
        size_t len = sep ? (size_t)(sep - p) : strlen(p);
        if (len == mac_len && strncasecmp(p, mac, mac_len) == 0) {
            return 1;
        }
        p = sep ? sep + 1 : NULL;
    }
    return 0;
}

/**
 * 通过接口返回已认证信息，更新到本地
 * @return 0 成功， 1 失败
 */
int update_local_auth_info_for_onelink_api()
{   
    int isp = get_current_isp();

    if(isp != 1) {
        return -1;
    }

    char *new_auth_str = NULL;

#ifdef QRZL_AUTH_WUXING
    new_auth_str = wuxing_check_device_authed();
#elif defined(QRZL_AUTH_JIUYAO)
    new_auth_str = jiuyao_check_device_authed();
#elif defined(QRZL_AUTH_ONE_LINK_ORIGINAL)
    init_cmp_config_data();
    new_auth_str = one_link_get_auth_list();
#elif defined(QRZL_AUTH_XUNYOU)
    init_xunyou_config_data();
    new_auth_str = one_link_get_auth_list();
#elif defined(QRZL_AUTH_JNZY) || defined(QRZL_AUTH_SHAYIN)
    new_auth_str = one_link_get_auth_list();
#elif defined(QRZL_AUTH_CHUANGSAN)
    new_auth_str = cs_one_link_get_authed();
#elif defined(QRZL_AUTH_BEIWEI)
    new_auth_str = bw_get_authed_list_str();
#elif defined(QRZL_AUTH_MY)
    new_auth_str = my_get_authed_list_str();
#else
    // Default case
    new_auth_str = one_link_get_auth_list();
#endif

    if (new_auth_str == NULL) { // 如果函数返回NULL说明是执行或解析失败，不要去更新原来的数据
        qrzl_log("接口获取认证信息失败，new_auth_str is NULL");
        free(new_auth_str);
        return -1;
    }

    if (strlen(new_auth_str) >= LOCAL_AUTH_MAX_SIZE) {
        qrzl_log("接口获取认证信息MAC 列表过长，无法保存\n");
        free(new_auth_str);
        return -1;
    }

    qrzl_log("yang - new_auth_str: %s, ONE_LINK_LOCAL_AUTH_INFO: %s", new_auth_str, ONE_LINK_LOCAL_AUTH_INFO);

    // 更新接口新认证信息到本地
    // cfg_set(ONE_LINK_LOCAL_AUTH_INFO, new_auth_str);
    update_authed_mac_list(ONE_LINK_LOCAL_AUTH_INFO, new_auth_str);
    
    free(new_auth_str);
    return 0;
}

/**
 * 验证终端是否已认证
 * @param mac 设备MAC
 * @param terminalMac 终端MAC
 * @param servAddr 回调地址
 * @return 1: 已认证    2：未认证
 */
int one_link_terminal_is_authed(const char *mac, const char *terminalMac, const char *servAddr)
{
    // if (strncmp(one_link_customer_info, "ORIGIN", sizeof(one_link_customer_info)) == 0) {
    //     // 中移原生认证检测
    //     return one_link_is_mac_authed(terminalMac);
    // } else if (strncmp(one_link_customer_info, "XUNYOU", sizeof(one_link_customer_info)) == 0) {
    //     // 中移原生认证检测
    //     return one_link_is_mac_authed(terminalMac);
    // } else if (strncmp(one_link_customer_info, "WUXING", sizeof(one_link_customer_info)) == 0) {
    //     wuxing_get_sign_info(mac, terminalMac, servAddr);
    //     // wuxing 认证检测
    //     return wuxing_is_mac_authed(terminalMac);
    // } else if (strncmp(one_link_customer_info, "CHUANGSAN", sizeof(one_link_customer_info)) == 0) {
    //     // TODO 创三认证接口
    // } else {
    //     // 获取已认证的mac列表
    //     char one_link_authed_mac_list[1024] = {0};
    //     cfg_get_item("one_link_authed_mac", one_link_authed_mac_list, sizeof(one_link_authed_mac_list));
    //     // 本地存储认证检测
    //     return is_mac_authenticated_with_timeout(terminalMac, one_link_authed_mac_list, 60*60*24*30);
    // }

    // * 读取本地存储的认证信息，避免接口频繁调用，或网络原因导致认证加载延迟
    int isp = get_current_isp();
    // 本地数据过期时间 单位 s
    int timeout_auth = 60*60*24*30;
    // 获取本地认证信息
    char *authed_mac_list = get_loacl_auth_info();

    char terminal_mac_format[33] = {0};
    // 将mac转成:分割格式
    convert_mac_format(terminalMac, terminal_mac_format, sizeof(terminal_mac_format), ':');

    qrzl_log("terminal_mac: %s", terminal_mac_format);
    qrzl_log("authed_mac_list: %s", authed_mac_list);

    return mac_in_local_auth_list(authed_mac_list, terminal_mac_format);
}

// 填充mac数组
int split_mac_list(const char *src, char macs[][32], int max_macs) {
    int count = 0;
    char buf[2048];
    // 将mac复制到缓冲区
    strncpy(buf, src, sizeof(buf));
    // 分割mac
    char *token = strtok(buf, ";");
    while (token && count < max_macs) {
        // 将mac填充到二维数组中
        strncpy(macs[count++], token, 31);
        token = strtok(NULL, ";");
    }
    return count;
}

// 检测设备离线
void check_device_offline(const char *prev_list, const char *current_list) {
    // 定义二维数组，数组个数最大200，因为设备最多只能连接200个
    char prev[200][32], curr[200][32];
    // 填充上次mac数组和当前mac数组
    int prev_cnt = split_mac_list(prev_list, prev, 200);
    int curr_cnt = split_mac_list(current_list, curr, 200);
    int i, j;
    // 循环上次的mac数组
    for (i = 0; i < prev_cnt; ++i) {
        int found = 0;
        // 内循环当前的mac数组，用于每次比较
        for (j = 0; j < curr_cnt; ++j) {
            // 如果上次的mac数组的元素，存在于当前数组，则表示设备在线，设置标识位并跳出内循环
            if (strcmp(prev[i], curr[j]) == 0) {
                found = 1;
                break;
            }
        }
        // 如果上次的mac数组的元素不在当前数组，说明设备下线了
        if (!found) {
            // 离线了
            qrzl_log("离线设备MAC：%s", prev[i]);
            char authentic_switch[10] = {0};
            cfg_get_item("qrzl_cloud_authentic_switch", authentic_switch, sizeof(authentic_switch));
            if(strcmp(authentic_switch,"1") == 0 || strlen(authentic_switch) == 0 ) {
                // 上报设备下线
                one_link_push_device_info(0, prev[i], "");
            }
        }
    }
}

int one_link_check_mac_in_last_station(char *station_mac) {
    pthread_mutex_lock(&last_station_mac_mutex);
    int ret = 0;
    if (strlen(last_station_mac) > 0) {
        if (strstr(last_station_mac, station_mac) != NULL) {
            // 如果上次记录的mac列表中已经存在当前mac，则返回1
            ret = 1;
        }
    }
    pthread_mutex_unlock(&last_station_mac_mutex);
    return ret;
}

void one_link_change_last_station_mac(char *station_mac) {
    update_device_dynamic_data();
    pthread_mutex_lock(&last_station_mac_mutex);
    //strncpy(last_station_mac, station_mac, sizeof(last_station_mac) - 1);
    if(strlen(last_station_mac)>0){
        if(strstr(last_station_mac, station_mac) != NULL){
            // 如果上次记录的mac列表中已经存在当前mac，则不进行更新
            pthread_mutex_unlock(&last_station_mac_mutex);
            return;
        }
        strcat(last_station_mac, ";");
        strcat(last_station_mac, station_mac);
    }
    else{
        strncpy(last_station_mac, station_mac, sizeof(last_station_mac) - 1);
    }
    pthread_mutex_unlock(&last_station_mac_mutex);
}

void* one_link_device_offline_thread()
{
    while (1)
    {
        int last_station_mac_len = 0;
        // 获取当前卡的IMSI
        char sim_imsi[32] = {0};
        cfg_get_item("sim_imsi", sim_imsi, 32);
        // 通过imsi查询运营商
        int isp = get_isp_by_imsi(sim_imsi);
        // 运营商不是移动则不进行下面的动作
        if (isp != 1) {
            continue;
        }

        pthread_mutex_lock(&last_station_mac_mutex);

        update_device_dynamic_data();
        char station_mac[2048] = {0};
        int ret = cfg_get_item("station_mac", station_mac, sizeof(station_mac));
        if (ret != 0) {
            qrzl_log("one_link_device_offline_thread -> station_mac 读取失败或未初始化!");
        }
        qrzl_log("one_link_device_offline_thread -> station_mac : %s ", station_mac);
        last_station_mac_len = strlen(last_station_mac);

        // 比较当前mac列表与上次记录mac列表
        if (last_station_mac_len > 0) {
            check_device_offline(last_station_mac, station_mac);
        }
        
        // 更新上次记录mac列表
        strncpy(last_station_mac, station_mac, sizeof(last_station_mac) - 1);

        pthread_mutex_unlock(&last_station_mac_mutex);
        // 每30秒检测一次设备下线
        sleep(30);
    }
    return NULL;
}

// void* one_link_device_offline_thread()
// {
//     while (1)
//     {

//         char station_mac[2048] = {0};
//         int ret = cfg_get_item("station_mac", station_mac, sizeof(station_mac));
//         if (ret != 0) {
//             qrzl_log("one_link_device_offline_thread -> station_mac 读取失败或未初始化!");
//         }
//         qrzl_log("one_link_device_offline_thread -> station_mac : %s ", station_mac);
//         if((strlen(station_mac) == 0) && (strlen(last_station_mac) == 0) || strcmp(station_mac,last_station_mac) == 0) {
//             qrzl_log("one_link_device_offline_thread -> station_mac is empty or null, skip this cycle.");
//             sleep(1);
//             continue;
//         }

//         int last_station_mac_len = 0;
//         // 获取当前卡的IMSI
//         char sim_imsi[32] = {0};
//         cfg_get_item("sim_imsi", sim_imsi, 32);
//         // 通过imsi查询运营商
//         int isp = get_isp_by_imsi(sim_imsi);
//         // 运营商不是移动则不进行下面的动作
//         if (isp != 1) {
//             continue;
//         }

//         update_device_dynamic_data();
//         last_station_mac_len = strlen(last_station_mac);

//         // 比较当前mac列表与上次记录mac列表
//         if (last_station_mac_len > 0) {
//             check_device_offline(last_station_mac, station_mac);
//         }
        
//         // 更新上次记录mac列表
//         strncpy(last_station_mac, station_mac, sizeof(last_station_mac) - 1);

//         // 每30秒检测一次设备下线
//         sleep(30);
//     }
//     return NULL;
// }


void one_link_start_process() 
{
    // 更新设备数据（一般通过写号写入的信息，无法被用户更改）， 会更新 g_qrzl_device_static_data
    update_device_static_data();

    int one_link_device_offline_thread_err;
    pthread_t one_link_device_offline_thread_tid;

    // 开启检测设备下线线程
    one_link_device_offline_thread_err = pthread_create(&one_link_device_offline_thread_tid, NULL, one_link_device_offline_thread, NULL);
    if (one_link_device_offline_thread_err != 0) {
        qrzl_err("创建one_link_device_offline_thread线程失败, error code: %d", one_link_device_offline_thread_err);
    }
    
}

void init_onelink_authed_info()
{

#ifndef QRZL_AUTH_ONE_LINK_HTTP
    // 如果未定义 QRZL_AUTH_ONE_LINK_HTTP 宏则直接返回
    qrzl_log("未开启移动认证，不进行本地认证信息更新.");
    return;
#endif

    // 等待网络上网完成 
    int i;
    for (i = 0; i < 3; i++) {
        if (check_network() == 0) {
            break;
        }
    }

    qrzl_log("开始初始化移动本地认证信息....");
    if (get_current_isp() != 1 && get_current_isp() != 0) {
        qrzl_log("非移动卡，结束重新获取");
        return;
    }
    // 初始化时更新一次本地认证信息
    int try_update_count = 1;
    int wait_time = 3;
    while (update_local_auth_info_for_onelink_api() != 0 && try_update_count <= 5) {
        qrzl_log("第 %d 次尝试更新本地认证信息失败，%d秒后尝试重新更新...", try_update_count, wait_time);
        try_update_count++;
        sleep(wait_time);
    }
}

// one_link 线程
void* one_link_http_control_start()
{   
    // 初始化配置
    init_config_data();

    init_onelink_authed_info();

    one_link_start_process();
}

// 获取第三方认证页面地址
int one_link_get_customers_page_url(char *customers_page_url, int url_len, char *mac, char *terminalMac, char *client_ip, char *servAddr)
{   
    qrzl_log("开始构建认证页面");
    // 清理customers_page_url，避免残留数据
    if (customers_page_url && url_len > 0) {
        memset(customers_page_url, 0, url_len);
        qrzl_log("清理customers_page_url残留数据..");
    }

#if defined(QRZL_ONE_LINK_CUSTOMER_WUXING) || defined(QRZL_ONE_LINK_CUSTOMER_JIUYAO)
    // 尝试获取客户自定义的认证页面
    int try_count = 3;
    while (try_count > 0 && cfg_get_item("cmp_customers_auth_page_url", customers_page_url, url_len) != 0)
    {   
        try_count --;
        sleep(1);
    }
#elif defined(QRZL_ONE_LINK_CUSTOMER_MY)
    // MAC
    char convered_mac[33] = {0};
    convert_mac_format(mac, convered_mac, sizeof(convered_mac), '\0');
    // terminalMac
    char convered_terminalMac[33] = {0};
    convert_mac_format(terminalMac, convered_terminalMac, sizeof(convered_terminalMac), '\0');
    // iccid
    char current_iccid[22] = {0};
    cfg_get_item("ziccid", current_iccid, sizeof(current_iccid));
    // sn
    char sn[20] = {0};
    snprintf(sn, sizeof(sn), "%s", g_qrzl_device_static_data.sn);
    // sequence
    int sim_index = get_device_current_sim_index();
    char sequence[2] = {0};
    switch (sim_index)
    {
        case 0:
            snprintf(sequence, sizeof(sequence), "%s", "3");
            break;
        case 1:
            snprintf(sequence, sizeof(sequence), "%s", "1");
            break;
        case 2:
            snprintf(sequence, sizeof(sequence), "%s", "2");
            break;
    }

    char lan_ip[30] = {0};
    cfg_get_item("lan_ipaddr", lan_ip, sizeof(lan_ip));

    snprintf(customers_page_url, url_len, "%s/boss-web/wx-login.html?mac=%s&terminalMac=%s&phoneNum=%s&iccid=%s&sn=%s&sequence=%s&deviceIp=http://%s/Api/codeToken", 
                one_link_customers_get_token_url, convered_mac, convered_terminalMac, "", current_iccid, sn, sequence , lan_ip);

#elif defined(QRZL_ONE_LINK_CUSTOMER_BEIWEI)
    bw_build_customer_url(customers_page_url, url_len, terminalMac, 3);
#else
    // 移动内置页面
   
#endif

    if (strlen(customers_page_url) > 0) {
        qrzl_log("移动 跳转地址为：%s", customers_page_url);
    } else {
        qrzl_log("移动 第三方跳转地址为NULL，使用内置页面");
    }

    return 0;
}


/**
 * 内置自定义认证页面
 */
const char *get_auth_html()
{

return

"<!DOCTYPE html>\n"
"<html lang='zh-CN'>\n"
"<head>\n"
  "<meta charset='UTF-8' />\n"
  "<meta name='viewport' content='width=device-width, initial-scale=1.0' />\n"
  "<title>ONELINK 认证</title>\n"
  "<style>\n"
    "body {\n"
      "font-family: Arial, sans-serif;\n"
      "margin: 0;\n"
      "padding: 0;\n"
      "background: linear-gradient(135deg, #e0f7fa, #e0f2f1);\n"
      "display: flex;\n"
      "justify-content: center;\n"
      "align-items: center;\n"
      "height: 100vh;\n"
      "animation: fadeIn 1s ease-in-out;\n"
    "}\n"
    "@keyframes fadeIn {\n"
      "from { opacity: 0; transform: scale(0.95); }\n"
      "to { opacity: 1; transform: scale(1); }\n"
    "}\n"
    ".container {\n"
      "background: #fff;\n"
      "padding: 25px 20px;\n"
      "border-radius: 12px;\n"
      "box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);\n"
      "width: 80%;\n"
      "max-width: 400px;\n"
      "animation: slideUp 0.6s ease-out;\n"
    "}\n"
    "@keyframes slideUp {\n"
      "from { transform: translateY(20px); opacity: 0; }\n"
      "to { transform: translateY(0); opacity: 1; }\n"
    "}\n"
    "h1 {\n"
      "font-size: 24px;\n"
      "margin-bottom: 20px;\n"
      "text-align: center;\n"
      "color: #333;\n"
    "}\n"
    "input[type='text'], button, input[type='submit'] {\n"
      "width: 100%;\n"
      "padding: 12px;\n"
      "margin: 10px 0;\n"
      "box-sizing: border-box;\n"
      "border: 1px solid #ccc;\n"
      "border-radius: 6px;\n"
      "font-size: 16px;\n"
      "transition: all 0.2s ease;\n"
    "}\n"
    "input[type='text']:focus {\n"
      "border-color: #4CAF50;\n"
      "outline: none;\n"
      "box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);\n"
    "}\n"
    "button, input[type='submit'] {\n"
      "background-color: #4CAF50;\n"
      "color: white;\n"
      "border: none;\n"
      "cursor: pointer;\n"
    "}\n"

    "button:hover:enabled, input[type='submit']:hover:enabled {\n"
      "background-color: #45a049;\n"
    "}\n"

    "button:disabled {\n"
      "background-color: #ccc !important;\n"
      "color: #666;\n"
      "cursor: not-allowed;\n"
    "}\n"

    ".message {\n"
      "font-size: 14px;\n"
      "color: #d32f2f;\n"
      "text-align: center;\n"
      "display: none;\n"
    "}\n"
  "</style>\n"

  "<script>\n"
    "function isValidPhone(phone) {\n"
      "return /^1[3-9]\\d{9}$/.test(phone);\n"
    "}\n"

    "function getCode() {\n"
      "const phone = document.getElementById('phoneNumber').value.trim();\n"
      "const msg = document.getElementById('msg');\n"
      "const btn = document.getElementById('getCodeBtn');\n"

      "msg.style.display = 'none';\n"

      "if (!phone) {\n"
        "msg.innerText = '请输入手机号';\n"
        "msg.style.display = 'block';\n"
        "return;\n"
      "}\n"

      "if (!isValidPhone(phone)) {\n"
        "msg.innerText = '手机号格式不正确';\n"
        "msg.style.display = 'block';\n"
        "return;\n"
      "}\n"

      "btn.disabled = true;\n"
      "btn.innerText = '发送中...';\n"

      "fetch('/get_code?phoneNumber=' + encodeURIComponent(phone))\n"
        ".then(res => res.json())\n"
        ".then(data => {\n"
          "if (data.code === '0') {\n"
            "alert('验证码已发送');\n"
            "startCountdown(btn);\n"
          "} else {\n"
            "btn.disabled = false;\n"
            "btn.innerText = '获取验证码';\n"
            "msg.innerText = data.message || '验证码发送失败或达到上限';\n"
            "msg.style.display = 'block';\n"
          "}\n"
        "})\n"
        ".catch(error => {\n"
          "btn.disabled = false;\n"
          "btn.innerText = '获取验证码';\n"
          "msg.innerText = '请求失败，请稍后重试';\n"
          "msg.style.display = 'block';\n"
        "});\n"
    "}\n"

    "function startCountdown(button) {\n"
      "let seconds = 30;\n"
      "button.disabled = true;\n"
      "button.innerText = `重新获取(${seconds}s)`;\n"

      "const interval = setInterval(() => {\n"
        "seconds--;\n"
        "if (seconds > 0) {\n"
          "button.innerText = `重新获取(${seconds}s)`;\n"
        "} else {\n"
          "clearInterval(interval);\n"
          "button.disabled = false;\n"
          "button.innerText = '获取验证码';\n"
        "}\n"
      "}, 1000);\n"
    "}\n"
    "function sendVerify() {\n"
        "const phone = document.getElementById('phoneNumber').value.trim();\n"
        "const code = document.getElementById('codeInput').value.trim();\n"
        "const msg = document.getElementById('msg');\n"
    
        "msg.style.display = 'none';\n"
    
        "if (!phone || !isValidPhone(phone)) {\n"
            "msg.innerText = '请输入正确的手机号';\n"
            "msg.style.display = 'block';\n"
            "return;\n"
        "}\n"
    
        "if (!code || code.length < 4) {\n"
            "msg.innerText = '请输入验证码';\n"
            "msg.style.display = 'block';\n"
            "return;\n"
        "}\n"
        "msg.style.color = '#333';\n"
        "msg.innerText = '认证中，请稍候...';\n"
        "msg.style.display = 'block';\n"
        "fetch('/send_verify?phoneNumber=' + encodeURIComponent(phone) + '&code=' + encodeURIComponent(code))\n"
        ".then(response => response.text())\n"
        ".then(html => {\n"
            "const redirectMatch = html.match(/<meta\\s+http-equiv=\"refresh\"\\s+content=\"[^;]*;url=([^\"]+)\"\\s*\\/?>/i);\n"
            "if (redirectMatch) {\n"
            "const redirectUrl = redirectMatch[1];\n"
            "window.location.href = redirectUrl;\n"
            "} else {\n"
                "console.log('No redirect found in response');\n"
            "}\n"
        "})\n"
        ".catch(error => {\n"
            "console.error('Error:', error);\n"
        "});\n"
    "}\n"

    "function validateForm(event) {\n"
      "const phone = document.getElementById('phoneNumber').value.trim();\n"
      "const code = document.getElementById('codeInput').value.trim();\n"
      "const msg = document.getElementById('msg');\n"
      "msg.style.display = 'none';\n"

      "if (!phone || !isValidPhone(phone)) {\n"
        "msg.innerText = '请输入正确的手机号';\n"
        "msg.style.display = 'block';\n"
        "event.preventDefault();\n"
        "return false;\n"
      "}\n"

      "if (!code || code.length < 4) {\n"
        "msg.innerText = '请输入验证码';\n"
        "msg.style.display = 'block';\n"
        "event.preventDefault();\n"
        "return false;\n"
      "}\n"

        "msg.style.color = '#333';\n"
        "msg.innerText = '认证中，请稍候...';\n"
        "msg.style.display = 'block';\n"

      "return true;\n"
    "}\n"
  "</script>\n"
"</head>\n"

"<body>\n"
"  <div class='container'>\n"
"    <h1>ONELINK 上网认证</h1>\n"
"    <form >\n"
"      <input type='text' id='phoneNumber' maxlength='11' name='phoneNumber' placeholder='请输入手机号' />\n"
"      <button type='button' id='getCodeBtn' maxlength='6' onclick='getCode()'>获取验证码</button>\n"
"      <input type='text' id='codeInput' name='code' placeholder='请输入验证码' />\n"
"      <button type='button' id='sendVerifyBtn' maxlength='6' onclick='sendVerify()'/>提交认证</button>\n"
"    </form>\n"
"    <div id='msg' class='message'></div>\n"
"  </div>\n"
"</body>\n"
"</html>\n";
}