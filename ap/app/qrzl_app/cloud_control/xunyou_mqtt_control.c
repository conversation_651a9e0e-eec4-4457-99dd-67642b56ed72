#include "stdio.h"
#include "stdlib.h"
#include "string.h"
#include <sys/time.h>
#include <unistd.h>
#include <unistd.h>
#include <curl/curl.h>

#include "xunyou_mqtt_control.h"
#include "../qrzl_utils.h"
#include "MQTTClient.h"
#include "../common_utils/cjson.h"

#define QRZL_HTTP_RESPONSE_MAX 5120
#define QRZL_HTTP_REQUEST_BODY_MAX 2048

extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

static char *get_remote_info_url = "http://iotcloud.xywlhlh.com/iot/device/report/config";

static char mqtt_server[128] = {0};
static char mqtt_port[8] = {0};
static char mqtt_username[128] = {0};
static char mqtt_password[128] = {0};
static char mqtt_client_id[128] = {0};
static int  mqtt_keepalive = 15;
static char mqtt_in_topic[128] = {0};  // 设备订阅主题
static char mqtt_out_topic[128] = {0}; // 设备发布主题

static pthread_mutex_t xunyou_msg_handler_lock;  // 定义T1类型mqtt消息处理互斥锁，防止多线程同时操作

static pthread_mutex_t mqtt_lock = PTHREAD_MUTEX_INITIALIZER;
static volatile int mqtt_connected = 0;

static uint32_t xunyou_publish_interval = 60;
static uint32_t retry_get_remote_config = 5;

// 变更上报信息结构体
typedef struct {
    char ssidName[64];
    char ssidPass[256];
    char wifiStatus[4];
    char wifiEncrypt[4];
    char connectCount[10];
    char webPassword[257];
    char currentIp[16];
    char remainPower[10];
    char isCharge[4];
    char rssi[16];
    char mainSim[4];
} changeInfoReport;

// 记录上次的信息
changeInfoReport last_changeInfoReport = {0};
// 记录本次的信息
changeInfoReport now_changeInfoReport = {0};

// 新的变更信息从参数 newInfo 中传入
static void update_change_info_report(const changeInfoReport *newInfo) {
    // 把当前 now -> last
    memcpy(&last_changeInfoReport, &now_changeInfoReport, sizeof(changeInfoReport));
    
    // 把新一轮的数据写入 now
    memcpy(&now_changeInfoReport, newInfo, sizeof(changeInfoReport));
}

// 比较两个 changeInfoReport，只构造变化字段的 JSON
char *build_change_info_delta_json(const changeInfoReport *old, const changeInfoReport *new) {
    cJSON *root = cJSON_CreateObject();
    if (!root) return NULL;

    uint64_t amountFlow = g_qrzl_device_dynamic_data.flux_month_total_bytes;
    if (amountFlow > 0) {
        amountFlow /= 1024;
    } else {
        amountFlow = 0;
    }
    char amountFlow_str[30] = {0};
    snprintf(amountFlow_str, sizeof(amountFlow_str), "%llu", amountFlow);

    // 获取当前限速值
    char limit_speed_str[30] = {0};
    snprintf(limit_speed_str, sizeof(limit_speed_str), "%llu", get_down_limit_net_speed());

    // 获取上报间隙
    char report_time_str[10] = {0};
    snprintf(report_time_str, sizeof(report_time_str), "%d", xunyou_publish_interval);

    cJSON_AddStringToObject(root, "imei", g_qrzl_device_static_data.imei);
    cJSON_AddStringToObject(root, "iccid", g_qrzl_device_dynamic_data.iccid);
    cJSON_AddStringToObject(root, "amountFlow", amountFlow_str);
    cJSON_AddStringToObject(root, "limitSpeed", limit_speed_str);
    cJSON_AddStringToObject(root, "mqttNextReportTime", report_time_str);

    if (strcmp(old->ssidName, new->ssidName) != 0) {
        cJSON_AddStringToObject(root, "ssidName", new->ssidName);
    }

    if (strcmp(old->ssidPass, new->ssidPass) != 0) {
        cJSON_AddStringToObject(root, "ssidPass", new->ssidPass);
    }

    if (strcmp(old->wifiStatus, new->wifiStatus) != 0) {
        cJSON_AddStringToObject(root, "wifiStatus", new->wifiStatus);
    }

    // if (strcmp(old->wifiEncrypt, new->wifiEncrypt) != 0) {
    //     cJSON_AddStringToObject(root, "wifiEncrypt", new->wifiEncrypt);
    // }

    if (strcmp(old->connectCount, new->connectCount) != 0) {
        cJSON_AddStringToObject(root, "connectCount", new->connectCount);
    }

    // if (strcmp(old->webPassword, new->webPassword) != 0) {
    //     cJSON_AddStringToObject(root, "webPassword", new->webPassword);
    // }

    // if (strcmp(old->currentIp, new->currentIp) != 0) {
    //     cJSON_AddStringToObject(root, "currentIp", new->currentIp);
    // }

    if (strcmp(old->remainPower, new->remainPower) != 0) {
        cJSON_AddStringToObject(root, "remainPower", new->remainPower);
    }

    if (strcmp(old->isCharge, new->isCharge) != 0) {
        cJSON_AddStringToObject(root, "isCharge", new->isCharge);
    }

    if (strcmp(old->rssi, new->rssi) != 0) {
        cJSON_AddStringToObject(root, "rssi", new->rssi);
    }

    if (strcmp(old->mainSim, new->mainSim) != 0) {
        cJSON_AddStringToObject(root, "mainSim", new->mainSim);
    }

    char *json_str = cJSON_PrintUnformatted(root);
    cJSON_Delete(root);
    return json_str;  // 需要调用者 free(json_str)
}


// MAC 转小写并统一成冒号分隔形式，便于匹配
static void normalize_mac(const char *src, char *dst) {
    int j = 0;
    int i = 0;
    for (i = 0; src[i] != '\0'; i++) {
        if (src[i] == ':' || src[i] == '-') {
            dst[j++] = ':';
        } else {
            dst[j++] = tolower((unsigned char)src[i]);
        }
    }
    dst[j] = '\0';
}

static size_t http_write_callback(void *contents, size_t size, size_t nmemb, void *userp) {
    size_t totalSize = size * nmemb;
    char *response = (char *)userp;

    size_t currentLen = strlen(response);
    size_t remaining = QRZL_HTTP_RESPONSE_MAX - currentLen - 1; // 预留 '\0'

    if (remaining <= 0) {
        qrzl_log("http返回值已满，不能再写入");
        return 0;
    }

    size_t copyLen = totalSize < remaining ? totalSize : remaining;
    strncat(response, (char *)contents, copyLen);

    return totalSize;
}

/**
 * HTTP POST 请求
 * @param url 请求地址
 * @param body 请求体
 * @param response 响应体
 * @param ssl_flag 是否带SSL 1: 是  0：否
 * @return 0 请求成功， -1 请求失败
 */
int http_send_post_request(const char *url, const char* body, char *response, int ssl_flag)
{
    CURL *curl;
    CURLcode res;

    qrzl_log("http_send_post_request -> URL: %s", url);
    qrzl_log("http_send_post_request -> request body: %s", body);

    // 初始化curl
    curl = curl_easy_init();

    if(curl == NULL) {
        qrzl_log("init CURL failed!!");
        return -1;
    }
    // 设置SSL
    if (ssl_flag == 1) {
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);
    }
    // 设置请求地址
    curl_easy_setopt(curl, CURLOPT_URL, url);
    // 使用post方式请求
    curl_easy_setopt(curl, CURLOPT_POST, 1L);
    // 设置响应超时时间
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
    // 设置连接超时时间
    curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L);
    // 回调函数
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
    // 设置POST请求的内容
    // curl_easy_setopt(curl, CURLOPT_POSTFIELDS, body);  // 这里使用了表单提交就不要提交body了
    // 设置HTTP头
    struct curl_slist *headers = NULL;

    // 构造 mime/form 表单体
    curl_mime *form = curl_mime_init(curl);
    curl_mimepart *field = NULL;
    field = curl_mime_addpart(form);
    curl_mime_name(field, "imei");
    curl_mime_data(field, g_qrzl_device_static_data.imei, CURL_ZERO_TERMINATED);

    // 设置表单
    curl_easy_setopt(curl, CURLOPT_MIMEPOST, form);

    // 设置公共参数
    curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
    // 设置 接收请求响应的内容
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
    // 正式发起请求
    res = curl_easy_perform(curl);

    // 处理返回值
    if(res != CURLE_OK) {
        qrzl_log("curl_easy_perform request failed!");
    }

    qrzl_log("http_send_post_request -> response body: %s", response);

    // 清理内存
     curl_mime_free(form);
    curl_slist_free_all(headers);
    curl_easy_cleanup(curl);

    if (res != CURLE_OK)
    {
        return -1;
    }
    return 0;
}

static int https_send_get_request(const char *url, char *response) {
    CURL *curl;
    CURLcode res;
    qrzl_log("http request url: %s", url);

    curl = curl_easy_init();
    if (curl) {
        // curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
        curl_easy_setopt(curl, CURLOPT_URL, url);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);
        // curl_easy_setopt(curl, CURLOPT_POST, 1L);
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L); 
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
        res = curl_easy_perform(curl);
        if (res != CURLE_OK) {
            qrzl_err("curl_easy_perform() failed: %s", curl_easy_strerror(res));
        } else {
            qrzl_log("http Response: %s\n", response);
        }
        curl_easy_cleanup(curl);
        if (res != CURLE_OK) {
            return -1;
        }
        return 0;
    }
    return -1;
}

// 获取当前连接WIFI用户的设备信息
static cJSON *get_connet_device_info()
{   
    // 请求地址
    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    // 接收响应
    char rev_response[QRZL_HTTP_RESPONSE_MAX] = {0};

    // 获取网关
    char lan_addr[20] = {0};
    cfg_get_item("lan_ipaddr", lan_addr, sizeof(lan_addr));

    // 登陆 http://*************/reqproc/proc_post?goformId=ALK_LOGIN
    snprintf(request_url, sizeof(request_url), "http://%s/reqproc/proc_post?goformId=ALK_LOGIN", lan_addr);
    int login_res = https_send_get_request(request_url, rev_response);
    if(login_res != 0) {
        qrzl_log("ALK_LOGIN 登陆失败");
        return NULL;
    } else {
        qrzl_log("ALK_LOGIN 登陆成功");
    }

    // 获取当前连接的设备信息 http://*************/reqproc/proc_get?cmd=station_list
    // 拼接请求地址
    snprintf(request_url, sizeof(request_url), "http://%s/reqproc/proc_get?cmd=station_list", lan_addr);
    // 清空rev_response
    memset(rev_response, 0, sizeof(rev_response));
    int sta_list_res = https_send_get_request(request_url, rev_response);
    if(sta_list_res != 0) {
        qrzl_log("当前连接的设备信息 失败");
        return NULL;
    }

    // 解析 JSON
    cJSON *root = cJSON_Parse(rev_response);
    if (!root) {
        qrzl_log("root JSON 解析失败");
        return NULL;
    }

    cJSON *station_list = cJSON_GetObjectItem(root, "station_list");
    if (!station_list) {
        qrzl_log("station_list 解析失败");
        return NULL;
    }

    return station_list; // 由调用方负责 cJSON_Delete
}

// 获取已认证的设备信息
static cJSON *get_auth_device_info()
{   
    // 请求地址
    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    // 接收响应
    char rev_response[QRZL_HTTP_RESPONSE_MAX] = {0};

    // 调用接口
    xunyou_check_device_authed_origin(rev_response);

    // 解析 JSON
    cJSON *root = cJSON_Parse(rev_response);

    if (!root) {
        qrzl_log("root 解析失败");
        return NULL;
    }

    cJSON *result = cJSON_GetObjectItem(root, "result");
    if (result == NULL || result->type != cJSON_Object) {
        qrzl_log("result 解析失败");
        return NULL;
    }

    cJSON *terminalList = cJSON_GetObjectItem(result, "terminalList");
    if (terminalList == NULL || terminalList->type != cJSON_Array) {
        qrzl_log("terminalList 解析失败");
        return NULL;
    }

    return terminalList; // 由调用方负责 cJSON_Delete
}

static char *build_terminal_list_report(cJSON *terminal_list, cJSON *station_list) {
    if (!terminal_list || terminal_list->type != cJSON_Array ||
        !station_list || station_list->type != cJSON_Array) {
        return NULL;
    }

    cJSON *result_array = cJSON_CreateArray();

    int term_size = cJSON_GetArraySize(terminal_list);
    int stat_size = cJSON_GetArraySize(station_list);

    int i = 0;
    for (i = 0; i < term_size; i++) {
        cJSON *term_item = cJSON_GetArrayItem(terminal_list, i);
        if (!term_item) continue;

        char mac1[32] = {0}, mac2[32] = {0};
        cJSON *term_mac = cJSON_GetObjectItem(term_item, "terminalMac");
        if (!term_mac || term_mac->type != cJSON_String) continue;
        normalize_mac(term_mac->valuestring, mac1);

        cJSON *expireTime = cJSON_GetObjectItem(term_item, "expireTime");

        int found = 0;
        int j = 0;
        for (j = 0; j < stat_size; j++) {
            cJSON *stat_item = cJSON_GetArrayItem(station_list, j);
            if (!stat_item) continue;

            cJSON *stat_mac = cJSON_GetObjectItem(stat_item, "mac_addr");
            if (!stat_mac || stat_mac->type != cJSON_String) continue;
            normalize_mac(stat_mac->valuestring, mac2);

            if (strcmp(mac1, mac2) == 0) {
                // 匹配成功
                cJSON *obj = cJSON_CreateObject();
                cJSON_AddStringToObject(obj, "terminalMac", mac1);
                cJSON_AddStringToObject(obj, "on_off", "1");

                cJSON *hostname = cJSON_GetObjectItem(stat_item, "hostname");
                cJSON_AddStringToObject(obj, "terminalName",
                    (hostname && hostname->type == cJSON_String) ? hostname->valuestring : "");

                cJSON *conn_time = cJSON_GetObjectItem(stat_item, "connect_time");
                char time_str[20] = {0};

                time_t now = time(NULL);
                struct tm *tm_info = localtime(&now);
                strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", tm_info);
                cJSON_AddStringToObject(obj, "busiTime", time_str);

                cJSON_AddStringToObject(obj, "expireTime",
                    (expireTime && expireTime->type == cJSON_String) ? expireTime->valuestring : "");

                cJSON_AddItemToArray(result_array, obj);
                found = 1;
                break;
            }
        }

        // if (!found) {
        //     // 没有匹配，标记离线
        //     cJSON *obj = cJSON_CreateObject();
        //     cJSON_AddStringToObject(obj, "terminalMac", mac1);
        //     cJSON_AddStringToObject(obj, "on_off", "0");
        //     cJSON_AddStringToObject(obj, "terminalName", "");
        //     cJSON_AddStringToObject(obj, "busiTime", "");
        //     cJSON_AddStringToObject(obj, "expireTime",
        //         (expireTime && expireTime->type == cJSON_String) ? expireTime->valuestring : "");
        //     cJSON_AddItemToArray(result_array, obj);
        // }
    }

    char *out_str = cJSON_PrintUnformatted(result_array);
    
    cJSON_Delete(result_array);
    return out_str; // 需要 free
}


int xunyou_mqtt_connect(MQTTClient* client_p)
{
    int rc;

    MQTTClient_connectOptions conn_opts = MQTTClient_connectOptions_initializer;
    conn_opts.username = mqtt_username;
    conn_opts.password = mqtt_password;
    conn_opts.keepAliveInterval = mqtt_keepalive;
    conn_opts.cleansession = 1;

    while ((rc = MQTTClient_connect(*client_p, &conn_opts)) != MQTTCLIENT_SUCCESS) {
        qrzl_log("MQTT connect failed with code %d. Retrying in 10 seconds...", rc);
        sleep(10);
        mqtt_connected = 0;
    }

    qrzl_log("mqtt connected successfully!");
    if ((rc = MQTTClient_subscribe(*client_p, mqtt_in_topic, 1)) != MQTTCLIENT_SUCCESS)
    {
        qrzl_log("Failed to subscribe to topic, return code %d", rc);
    }

    qrzl_log("Subscribed to topic: %s", mqtt_in_topic);

    mqtt_connected = 1;
    return rc;
}

void xunyou_on_message_delivered(void* context, MQTTClient_deliveryToken dt)
{
    qrzl_log("Message with token %d delivered", dt);
}



/**
 * 数据变更信息上报
 */
int xunyou_report_update_device_info(MQTTClient* client_p)
{
    if (!MQTTClient_isConnected(*client_p)) 
    {
        qrzl_log("MQTT not connected, don't publish device info");
        return -1;
    }

    // 更新信息
    update_device_dynamic_data();

    // 获取WIFI加密方式
    char wifi_encrypt_str[4] = {0};
    snprintf(wifi_encrypt_str, sizeof(wifi_encrypt_str), "%s",  "0");
    if (strncmp("WPA2PSK", g_qrzl_device_dynamic_data.wifi_auth_mode, sizeof(g_qrzl_device_dynamic_data.wifi_auth_mode)) == 0)
    {
        snprintf(wifi_encrypt_str, sizeof(wifi_encrypt_str), "%s", "0");
    }
    else if (strncmp("WPAPSKWPA2PSK", g_qrzl_device_dynamic_data.wifi_auth_mode, sizeof(g_qrzl_device_dynamic_data.wifi_auth_mode)) == 0)
    {
        snprintf(wifi_encrypt_str, sizeof(wifi_encrypt_str), "%s", "1");
    }

    // 获取充电状态
    char isCharge_str[4] = {0};
    snprintf(isCharge_str, sizeof(isCharge_str), "%d", get_device_charge_status());

    // 获取连接人数
    char conn_num_str[10] = {0};
    snprintf(conn_num_str, sizeof(conn_num_str), "%d", g_qrzl_device_dynamic_data.conn_num);

    // 处理WEB密码
    char web_password_base64[64*4+1] = {0};
    qrzl_base64_encode_safe(g_qrzl_device_dynamic_data.web_password, strlen(g_qrzl_device_dynamic_data.web_password),
                                web_password_base64, sizeof(web_password_base64));

    // 获取当前使用卡槽
    int main_sim = -1;
    char main_sim_str[4] = {0};
#ifdef QRZL_ESIM2_ON_SIM_SLOT
    if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM1_only") == 0)
    {
        main_sim = 0;
    } 
    else if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM2_only") == 0)
    {
        main_sim = 1;
    }
#elif QRZL_HAVE_3_ESIM_CARD
    if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM1_only") == 0)
    {
        main_sim = 1;
    } 
    else if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM2_only") == 0)
    {
        main_sim = 2;
    }
    else if (strcmp(g_qrzl_device_dynamic_data.current_sim, "RSIM_only") == 0)
    {
        main_sim = 0;
    }
#endif
    snprintf(main_sim_str, sizeof(main_sim_str), "%d", main_sim);

    // 更新记录
    changeInfoReport new_info = {0};
    strcpy(new_info.ssidName, g_qrzl_device_dynamic_data.wifi_ssid);
    strcpy(new_info.ssidPass, g_qrzl_device_dynamic_data.wifi_key_base64);
    strcpy(new_info.wifiStatus, g_qrzl_device_dynamic_data.wifi_enable == 1 ? "1":"0");
    strcpy(new_info.wifiEncrypt, wifi_encrypt_str);
    strcpy(new_info.connectCount, conn_num_str);
    strcpy(new_info.webPassword, web_password_base64);
    strcpy(new_info.currentIp, g_qrzl_device_dynamic_data.lan_ipaddr);
    strcpy(new_info.remainPower, g_qrzl_device_dynamic_data.remain_power);
    strcpy(new_info.isCharge, isCharge_str);
    strcpy(new_info.rssi, g_qrzl_device_dynamic_data.rssi);
    strcpy(new_info.mainSim, main_sim_str);

    update_change_info_report(&new_info);

    // 比较上次记录的信息
    char *json_str = build_change_info_delta_json(&last_changeInfoReport, &now_changeInfoReport);


    if (strcmp(json_str, "{}") == 0) {
        qrzl_log("数据为NULL，不进行上报。");
        free(json_str);
        return 0;
    }

    // 构建数据上报
    qrzl_log("数据有变化: %s", json_str);
    qrzl_log("开始上报变化数据...");

    MQTTClient_message pubmsg = MQTTClient_message_initializer;
    pubmsg.payload = json_str;
    pubmsg.payloadlen = (int)strlen(json_str);
    pubmsg.qos = 0;
    pubmsg.retained = 0;

    MQTTClient_deliveryToken token;
    int rc = MQTTClient_publishMessage(*client_p, mqtt_out_topic, &pubmsg, &token);
    if (rc != MQTTCLIENT_SUCCESS) {
        qrzl_err("Failed to publish message, return code %d", rc);
    } else {
        qrzl_log("Message published!");
    }

    free(json_str);

    return 0;
}

int xunyou_publish_device_info(MQTTClient* client_p)
{
    if (!MQTTClient_isConnected(*client_p)) 
    {
        qrzl_log("MQTT not connected, don't publish device info");
        return -1;
    }
    update_device_dynamic_data();
    qrzl_log("开始推送信息至mqtt broker");

    // 构造请求JSON Body
    cJSON *root = cJSON_CreateObject();

    // 月流量处理
    uint64_t amountFlow = g_qrzl_device_dynamic_data.flux_month_total_bytes;
    if (amountFlow > 0) {
        amountFlow /= 1024;
    } else {
        amountFlow = 0;
    }
    char amountFlow_str[30] = {0};
    snprintf(amountFlow_str, sizeof(amountFlow_str), "%llu", amountFlow);

    // 获取当前时间； 长度需要能存储 "yyyymmddhhmmss" 和一个 '\0'
    char time_string[15] = {0}; 
    get_local_time("%Y%m%d%H%M%S", time_string, sizeof(time_string));

    // 获取当前限速值
    char limit_speed_str[30] = {0};
    snprintf(limit_speed_str, sizeof(limit_speed_str), "%llu", get_down_limit_net_speed());

    // 获取WIFI加密方式
    char wifi_encrypt_str[4] = {0};
    snprintf(wifi_encrypt_str, sizeof(wifi_encrypt_str), "%s",  "0");
    if (strncmp("WPA2PSK", g_qrzl_device_dynamic_data.wifi_auth_mode, sizeof(g_qrzl_device_dynamic_data.wifi_auth_mode)) == 0)
    {
        snprintf(wifi_encrypt_str, sizeof(wifi_encrypt_str), "%s", "0");
    }
    else if (strncmp("WPAPSKWPA2PSK", g_qrzl_device_dynamic_data.wifi_auth_mode, sizeof(g_qrzl_device_dynamic_data.wifi_auth_mode)) == 0)
    {
        snprintf(wifi_encrypt_str, sizeof(wifi_encrypt_str), "%s", "1");
    }

    // 处理WEB密码
    char web_password_base64[64*4+1] = {0};
    qrzl_base64_encode_safe(g_qrzl_device_dynamic_data.web_password, strlen(g_qrzl_device_dynamic_data.web_password),
                                web_password_base64, sizeof(web_password_base64));

    // 获取当前使用卡槽
    int main_sim = -1;
    char main_sim_str[4] = {0};
#ifdef QRZL_ESIM2_ON_SIM_SLOT
    if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM1_only") == 0)
    {
        main_sim = 0;
    } 
    else if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM2_only") == 0)
    {
        main_sim = 1;
    }
#elif QRZL_HAVE_3_ESIM_CARD
    if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM1_only") == 0)
    {
        main_sim = 1;
    } 
    else if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM2_only") == 0)
    {
        main_sim = 2;
    }
    else if (strcmp(g_qrzl_device_dynamic_data.current_sim, "RSIM_only") == 0)
    {
        main_sim = 0;
    }
#endif
    snprintf(main_sim_str, sizeof(main_sim_str), "%d", main_sim);

    // 获取DNS
    char wan_pridns[16] = "0.0.0.0";
    char wan_secdns[16] = "0.0.0.0";
    if (cfg_get_item("wan1_pridns", wan_pridns, sizeof(wan_pridns)) != 0) {
        strcpy(wan_pridns, "0.0.0.0");
    }
    if (cfg_get_item("wan1_secdns", wan_secdns, sizeof(wan_secdns)) != 0) {
        strcpy(wan_secdns, "0.0.0.0");
    }
    char dns_str[35] = {0};
    snprintf(dns_str, sizeof(dns_str), "%s,%s", wan_pridns, wan_secdns);

    // 获取充电状态
    char isCharge_str[4] = {0};
    snprintf(isCharge_str, sizeof(isCharge_str), "%d", get_device_charge_status());

    // 获取上网公网IP
    char networkIp_str[16] = "0.0.0.0";
    cfg_get_item("wan_ipaddr", networkIp_str, sizeof(networkIp_str));

    // 获取主板温度
    char temperature_str[8] = "0";
    cfg_get_item("board_temperature", temperature_str, sizeof(temperature_str));

    // 获取连接人数
    char conn_num_str[10] = {0};
    snprintf(conn_num_str, sizeof(conn_num_str), "%d", g_qrzl_device_dynamic_data.conn_num);

    // 获取上报间隙
    char report_time_str[10] = {0};
    snprintf(report_time_str, sizeof(report_time_str), "%d", xunyou_publish_interval);

    char is_open_auth_str[2] = {0};
    cfg_get_item("qrzl_cloud_authentic_switch", is_open_auth_str, sizeof(is_open_auth_str));


    cJSON_AddStringToObject(root, "imei", g_qrzl_device_static_data.imei); 
    cJSON_AddStringToObject(root, "iccid", g_qrzl_device_dynamic_data.iccid);
    cJSON_AddStringToObject(root, "amountFlow", amountFlow_str);
    cJSON_AddStringToObject(root, "currentTime", time_string);
    cJSON_AddStringToObject(root, "limitSpeed", limit_speed_str);
    cJSON_AddStringToObject(root, "ssidName", g_qrzl_device_dynamic_data.wifi_ssid);
    cJSON_AddStringToObject(root, "ssidPass", g_qrzl_device_dynamic_data.wifi_key_base64);
    cJSON_AddStringToObject(root, "wifiStatus", g_qrzl_device_dynamic_data.wifi_enable == 1 ? "1":"0");
    cJSON_AddStringToObject(root, "wifiEncrypt", wifi_encrypt_str);
    // cJSON_AddStringToObject(root, "wifiStatus5G", "0");
    // cJSON_AddStringToObject(root, "wifiEncrypt5G", "0");
    // cJSON_AddStringToObject(root, "ssidName5G", "");
    // cJSON_AddStringToObject(root, "ssidPass5G", "");
    cJSON_AddStringToObject(root, "connectCount", conn_num_str);
    cJSON_AddStringToObject(root, "softVersion", g_qrzl_device_static_data.soft_version);
    cJSON_AddStringToObject(root, "webPassword", web_password_base64);
    cJSON_AddStringToObject(root, "mainSim", main_sim_str);
    cJSON_AddStringToObject(root, "rssi", g_qrzl_device_dynamic_data.rssi);
    cJSON_AddStringToObject(root, "dualSim", "1");
    cJSON_AddStringToObject(root, "currentIp", g_qrzl_device_dynamic_data.lan_ipaddr);
    cJSON_AddStringToObject(root, "remainPower", g_qrzl_device_dynamic_data.remain_power);
    cJSON_AddStringToObject(root, "mcc", g_qrzl_device_dynamic_data.mcc);
    cJSON_AddStringToObject(root, "mnc", g_qrzl_device_dynamic_data.mnc);
    cJSON_AddStringToObject(root, "lac", g_qrzl_device_dynamic_data.lac);
    cJSON_AddStringToObject(root, "mac", g_qrzl_device_static_data.mac);
    cJSON_AddStringToObject(root, "cellid", g_qrzl_device_dynamic_data.cid);
    cJSON_AddStringToObject(root, "dnsServers", dns_str);
    cJSON_AddStringToObject(root, "isCharge", isCharge_str);
    cJSON_AddStringToObject(root, "networkIp", networkIp_str);
    cJSON_AddStringToObject(root, "temperature", temperature_str);
    cJSON_AddStringToObject(root, "mqttNextReportTime", report_time_str);
    cJSON_AddStringToObject(root, "isOpenAuth", is_open_auth_str);

    cJSON *terminal_list = get_auth_device_info();
    cJSON *station_list = get_connet_device_info();

    char *merged = build_terminal_list_report(terminal_list, station_list);

    if (merged) {
        qrzl_log("合并结果: %s\n", merged);

        cJSON *merged_json = cJSON_Parse(merged); // 保持 JSON 类型
        if (merged_json) {
            cJSON_AddItemToObject(root, "terminalList", merged_json);
        }
    }

    free(merged);

    // 更新记录
    changeInfoReport new_info = {0};
    strcpy(new_info.ssidName, g_qrzl_device_dynamic_data.wifi_ssid);
    strcpy(new_info.ssidPass, g_qrzl_device_dynamic_data.wifi_key_base64);
    strcpy(new_info.wifiStatus, g_qrzl_device_dynamic_data.wifi_enable == 1 ? "1":"0");
    strcpy(new_info.wifiEncrypt, wifi_encrypt_str);
    strcpy(new_info.connectCount, conn_num_str);
    strcpy(new_info.webPassword, web_password_base64);
    strcpy(new_info.currentIp, g_qrzl_device_dynamic_data.lan_ipaddr);
    strcpy(new_info.remainPower, g_qrzl_device_dynamic_data.remain_power);
    strcpy(new_info.isCharge, isCharge_str);

    update_change_info_report(&new_info);

    // 手动请求的序列化字符串，由 malloc() 分配，需要手动释放。
    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char *json_str = cJSON_PrintUnformatted(root);  // 转成紧凑字符串
    if (json_str) {
        strncpy(request_body, json_str, sizeof(request_body) - 1);
        request_body[sizeof(request_body) - 1] = '\0'; // 防止越界
        free(json_str); // 手动释放内部申请的内存
    }
    cJSON_Delete(root); // 释放 JSON 对象

    qrzl_log("mqtt payload: %s", request_body);

    MQTTClient_message pubmsg = MQTTClient_message_initializer;
    pubmsg.payload = request_body;
    pubmsg.payloadlen = (int)strlen(request_body);
    pubmsg.qos = 0;
    pubmsg.retained = 0;

    MQTTClient_deliveryToken token;
    int rc = MQTTClient_publishMessage(*client_p, mqtt_out_topic, &pubmsg, &token);
    if (rc != MQTTCLIENT_SUCCESS) {
        qrzl_err("Failed to publish message, return code %d", rc);
    } else {
        qrzl_log("Message published!");
    }
    return 0;
}

int xunyou_order_msg_handler(cJSON* value, MQTTClient* client_p)
{
    int ret = 0;
    cJSON* j_limit_speed_num = cJSON_GetObjectItem(value, "limitSpeed"); 
    if (j_limit_speed_num != NULL && cJSON_IsString(j_limit_speed_num))
    {
        uint64_t limit_speed_num = strtoull(j_limit_speed_num->valuestring, NULL, 10);
        limit_net_speed(limit_speed_num, limit_speed_num);
    }

    cJSON* j_next_report_time = cJSON_GetObjectItem(value, "mqttNextReportTime");
    if (j_next_report_time != NULL && cJSON_IsString(j_next_report_time))
    {
        qrzl_log("下发的心跳间隙：%s", j_next_report_time->valuestring);
        int tmp;
        tmp = atoi(j_next_report_time->valuestring);
        if (tmp > 10)
        {
            xunyou_publish_interval = tmp;
            qrzl_log("转换后的心跳间隙：%d", xunyou_publish_interval);
        } else {
            qrzl_log("心跳间隙不能低于10秒");
        }
    }

    struct wifi_config_t wifi_config = {};
    init_wifi_config_value(&wifi_config);

    cJSON* j_ssid_name = cJSON_GetObjectItem(value, "ssidName");
    if (j_ssid_name != NULL && cJSON_IsString(j_ssid_name) && strlen(j_ssid_name->valuestring) > 0)
    {
        snprintf(wifi_config.ssid, sizeof(wifi_config.ssid), "%s", j_ssid_name->valuestring);
    }
    cJSON* j_ssid_pass = cJSON_GetObjectItem(value, "ssidPass");
    if (j_ssid_pass != NULL && cJSON_IsString(j_ssid_pass) && strlen(j_ssid_pass->valuestring) > 0)
    {
        snprintf(wifi_config.key, sizeof(wifi_config.key), "%s", j_ssid_pass->valuestring);
    }
    cJSON* j_wifi_status = cJSON_GetObjectItem(value, "wifiStatus");
    if (j_wifi_status != NULL && cJSON_IsString(j_wifi_status))
    {
        if (strncmp("1", j_wifi_status->valuestring, 1) == 0)
        {
            wifi_config.enable = 1;
        }
        else
        {
            wifi_config.enable = 0;
        }
    }
    cJSON* j_wifi_encrypt = cJSON_GetObjectItem(value, "wifiEncrypt");
    if (j_wifi_encrypt != NULL && cJSON_IsString(j_wifi_encrypt))
    {
        if (strncmp("0", j_wifi_encrypt->valuestring, 1) == 0)
        {
            snprintf(wifi_config.auth_mode, sizeof(wifi_config.auth_mode), "WPA2PSK");
        }
        else if (strncmp("1", j_wifi_encrypt->valuestring, 1) == 0)
        {
            snprintf(wifi_config.auth_mode, sizeof(wifi_config.auth_mode), "WPAPSKWPA2PSK");
        }
    }

    cJSON* j_connect_count = cJSON_GetObjectItem(value, "connectCount");
    if (j_connect_count != NULL && cJSON_IsString(j_connect_count))
    {
        int max_connect = atoi(j_connect_count->valuestring);
        wifi_config.max_access_num = max_connect;
    }

    cJSON* j_wifi_usb_switch = cJSON_GetObjectItem(value, "wifiUsbSwitch");
    if (j_wifi_usb_switch != NULL && cJSON_IsString(j_wifi_usb_switch))
    {
        if (strncmp("1", j_wifi_usb_switch->valuestring, 1) == 0)
        {
            set_network_br0_disconnect(0);
            wifi_config.enable = 1;
        }
        else
        {
            set_network_br0_disconnect(1);
            wifi_config.enable = 0;
        }
    }

    update_wifi_by_config(&wifi_config);

    cJSON* j_force_reset = cJSON_GetObjectItem(value, "forceReset");
    if (j_force_reset != NULL && cJSON_IsString(j_force_reset))
    {
        if (strncmp("1", j_force_reset->valuestring, 1) == 0)
        {
            reset_device();
        }
    }

    cJSON* j_force_restart = cJSON_GetObjectItem(value, "forceRestart");
    if (j_force_restart != NULL && cJSON_IsString(j_force_restart))
    {
        if (strncmp("1", j_force_restart->valuestring, 1) == 0)
        {
            restart_device();
        }
    }

    cJSON* j_web_password = cJSON_GetObjectItem(value, "webPassword");
    if (j_web_password != NULL && cJSON_IsString(j_web_password) && strlen(j_web_password->valuestring) > 3)
    {
        update_web_password(j_web_password->valuestring);
    }
    
    cJSON* j_main_sim = cJSON_GetObjectItem(value, "mainSim");
    if (j_main_sim != NULL && cJSON_IsString(j_main_sim))
    {
        if (strncmp("0", j_main_sim->valuestring, 1) == 0)
        {
            switch_sim_card_not_restart(1);
        }
        else if (strncmp("1", j_main_sim->valuestring, 1) == 0)
        {
            switch_sim_card_not_restart(2);
        }
        else if (strncmp("2", j_main_sim->valuestring, 1) == 0)
        {
            switch_sim_card_not_restart(0);
        }
    }

    cJSON* j_dns_servers = cJSON_GetObjectItem(value, "dnsServers");
    if (j_dns_servers != NULL && cJSON_IsString(j_dns_servers))
    {
        char tmp_dns_list[32] = {0};
        strncpy(tmp_dns_list, j_dns_servers->valuestring, sizeof(tmp_dns_list));
        char dns1[16] = {0};
        char dns2[16] = {0};
        // 使用 strtok 函数分割字符串
        char *token = strtok(tmp_dns_list, ",");  // 第一个分割点
        if (token != NULL) {
            strncpy(dns1, token, sizeof(dns1));  // 存储第一个 IP 地址
        }

        token = strtok(NULL, ",");  // 获取下一个分割点
        if (token != NULL) {
            strncpy(dns2, token, sizeof(dns2));  // 存储第二个 IP 地址
        }

        if (valid_ipv4(dns1) && valid_ipv4(dns2))
        {
            qrzl_log("dns1: %s, dns2: %s; 合法dns，准备更新dns", dns1, dns2);
            // 打开文件（"w" 模式会清空文件内容）
            FILE *file = fopen("/etc_rw/dnsmasq.conf", "w");
            if (file == NULL) {
                qrzl_err("无法打开文件/etc_rw/dnsmasq.conf");
                return -1;
            }

            char dnsmasq_conf_content[64] = {0};
            snprintf(dnsmasq_conf_content, sizeof(dnsmasq_conf_content), "nameserver %s\nnameserver %s\n", dns1, dns2);

            // 写入内容到文件
            if (fputs(dnsmasq_conf_content, file) == EOF) {
                qrzl_err("写入文件/etc_rw/dnsmasq.conf失败");
                fclose(file);
                return -1;
            }
            // 关闭文件
            fclose(file);

            cfg_set("wan1_pridns", dns1);
            cfg_set("wan1_secdns", dns2);
            system("killall -9 dnsmasq");
            sleep(1);
            system("dnsmasq -i br0 -r /etc_rw/dnsmasq.conf &");
        }
    }

    cJSON* j_auth_switch = cJSON_GetObjectItem(value, "auth_switch");
    if (j_auth_switch != NULL && j_auth_switch->type == cJSON_String) {
        // qrzl_log("qrzl_cloud_authentic_switch set %s\n", j_auth_switch->u.string.ptr);
        // // 设置认证开关
        // ret = cfg_set("qrzl_cloud_authentic_switch", j_auth_switch->u.string.ptr);
		// if(ret < 0)
		// 	qrzl_log("qrzl_cloud_authentic_switch set vermode fail\n");
		// else
		// 	cfg_save();
        //     // 清空认证文件
        //     system("echo clear > /proc/cjportal/auth");
        //     qrzl_log("Certification information has been cleared.");
		// 	qrzl_log("qrzl_cloud_authentic_switch set success!\n");
        set_authentic_switch(j_auth_switch->valuestring);
    }

    cJSON* j_flowFlag = cJSON_GetObjectItem(value, "flowFlag");
    if (j_flowFlag != NULL && cJSON_IsString(j_flowFlag)) {
        if (strcmp("1", j_flowFlag) == 0) {
            qrzl_log("执行缓存清除...");
            // 清除月流量统计数据
            cfg_set("flux_month_total", "0");
            qrzl_log("已清除流量统计数据");
        }
    }

    cJSON* j_uploadNow = cJSON_GetObjectItem(value, "uploadNow");
    if (j_uploadNow != NULL && cJSON_IsString(j_uploadNow)) {
        if (strcmp(j_uploadNow->valuestring, "1") == 0 ) {
            xunyou_publish_device_info(client_p);
        }
    }

    return ret;
}

int xunyou_message_arrived(void* context, char* topicName, int topicLen, MQTTClient_message* message)
{
    qrzl_log("Message arrived on topic: %s", topicName);
    qrzl_log("Message: %.*s", message->payloadlen, (char*)message->payload);
    MQTTClient* client_p = (MQTTClient*)context;

    cJSON *j_value = cJSON_Parse((char*)message->payload);
    if (j_value != NULL)
    {
        if (cJSON_IsObject(j_value))
        {
            qrzl_err("json object normal");
            update_device_dynamic_data();
            xunyou_order_msg_handler(j_value, client_p);
        }
        // 释放 JSON 解析结果
        cJSON_Delete(j_value);
    }
    
    MQTTClient_freeMessage(&message);
    MQTTClient_free(topicName);

    return 1;
}

void* mqtt_reconnect_thread(void* arg) {
    MQTTClient* client_p = (MQTTClient*)arg;

    pthread_mutex_lock(&mqtt_lock);
    mqtt_connected = 0;
    qrzl_log("MQTT connection lost, start reconnect...");
    xunyou_mqtt_connect(client_p);
    xunyou_publish_device_info(client_p);
    pthread_mutex_unlock(&mqtt_lock);
    return NULL;
}

void xunyou_mqtt_connlost(void *context, char *cause)
{
    qrzl_log("MQTT lost, cause: %s", cause);
    pthread_t reconnect_tid;
    pthread_create(&reconnect_tid, NULL, mqtt_reconnect_thread, context);
    pthread_detach(reconnect_tid);
}

void* xunyou_publish_loop(void* client_ptr) {
    MQTTClient* client_p = (MQTTClient*)client_ptr;
    qrzl_log("publish_loop start");

    // 连接成功发送一次全部数据
    xunyou_publish_device_info(client_p);

    while (1) {

        sleep(xunyou_publish_interval);
        
        if (mqtt_connected) {
            pthread_mutex_lock(&mqtt_lock);
            // 数据变化上报
            xunyou_report_update_device_info(client_p);
            pthread_mutex_unlock(&mqtt_lock);
        } else {
            qrzl_log("MQTT not connected, skip publish");
        }
    }

    return NULL;
}

static void xunyou_cloud_client_start()
{
    update_device_static_data();
    
    if (pthread_mutex_init(&xunyou_msg_handler_lock, NULL) != 0) {
        qrzl_log("xunyou_msg_handler_lock init failed\n");
    }

    MQTTClient client;

    MQTTClient_create(&client, mqtt_server, mqtt_client_id, MQTTCLIENT_PERSISTENCE_NONE, NULL);

    MQTTClient_setCallbacks(client, &client, xunyou_mqtt_connlost, xunyou_message_arrived, xunyou_on_message_delivered);

    xunyou_mqtt_connect(&client);

    pthread_t pub_thread;
    if (pthread_create(&pub_thread, NULL, xunyou_publish_loop, &client) != 0) {
        qrzl_err("Failed to create publish thread");
    }

    pthread_detach(pub_thread);

    while (1)
    {
        sleep(1); // 主循环间隔
    }

    MQTTClient_disconnect(client, 10000);
    MQTTClient_destroy(&client);
    pthread_mutex_destroy(&xunyou_msg_handler_lock);
    return;
}

/**
 * 从服务器获取MQTT信息
 */
static int get_remote_mqtt_info()
{   
    int res;
    char respense_body[QRZL_HTTP_RESPONSE_MAX] = {0};

    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    snprintf(request_body, sizeof(request_body), "{\"imei\": \"%s\"}", g_qrzl_device_static_data.imei);

    // 构造请求
    res = http_send_post_request(get_remote_info_url, request_body, respense_body, 0);
    if (res != 0) {
        qrzl_err("获取MQTT信息失败，请求失败。");
        return -1;
    }

    // 解析响应体
    cJSON *root = cJSON_Parse(respense_body);
    if (root == NULL || root->type != cJSON_Object){
        qrzl_log("响应体解析失败!");
        cJSON_Delete(root);
        return -1;
    }

    cJSON *j_code = cJSON_GetObjectItem(root, "code");
    if (j_code == NULL || j_code->type != cJSON_String) {
        qrzl_log("code is NULL or type error!");
        cJSON_Delete(root);
        return -1;
    } else {
        if (strcmp("9999", j_code->valuestring) == 0) {
            qrzl_log("IMEI does not exist");
            retry_get_remote_config = 30;
            cJSON_Delete(root);
            return -1;
        }
    }

    cJSON *result = cJSON_GetObjectItem(root, "result");
    if (result == NULL || result->type != cJSON_True) {
        qrzl_log("Result 不是预期值!");
        cJSON_Delete(root);
        return -1;
    }

    cJSON *data = cJSON_GetObjectItem(root, "data");
    if (data == NULL || data->type != cJSON_Object) {
        qrzl_log("data is NULL or type error!");
        cJSON_Delete(root);
        return -1;
    }

    cJSON *host = cJSON_GetObjectItem(data, "host");
    if (host == NULL || host->type != cJSON_String) {
        qrzl_log("host is NULL or not string!");
        cJSON_Delete(root);
        return -1;
    }

    cJSON *port = cJSON_GetObjectItem(data, "port");
    if (port == NULL || port->type != cJSON_String) {
        qrzl_log("port is NULL or not string!");
        cJSON_Delete(root);
        return -1;
    }

    cJSON *username = cJSON_GetObjectItem(data, "username");
    if (username == NULL || username->type != cJSON_String) {
        qrzl_log("username is NULL or not string!");
        cJSON_Delete(root);
        return -1;
    }

    cJSON *password = cJSON_GetObjectItem(data, "password");
    if (password == NULL || password->type != cJSON_String) {
        qrzl_log("password is NULL or not string!");
        cJSON_Delete(root);
        return -1;
    }

    cJSON *clientId = cJSON_GetObjectItem(data, "clientId");
    if (clientId == NULL || clientId->type != cJSON_String) {
        qrzl_log("clientId is NULL or not string!");
        cJSON_Delete(root);
        return -1;
    }

    cJSON *keepalive = cJSON_GetObjectItem(data, "keepalive");
    if (keepalive == NULL || keepalive->type != cJSON_Number) {
        qrzl_log("keepalive is NULL or not int!");
        cJSON_Delete(root);
        return -1;
    }

    cJSON *deviceInTopic = cJSON_GetObjectItem(data, "deviceInTopic");
    if (deviceInTopic == NULL || deviceInTopic->type != cJSON_String) {
        qrzl_log("deviceInTopic is NULL or not string!");
        cJSON_Delete(root);
        return -1;
    }

    cJSON *deviceOutTopic = cJSON_GetObjectItem(data, "deviceOutTopic");
    if (deviceOutTopic == NULL || deviceOutTopic->type != cJSON_String) {
        qrzl_log("deviceOutTopic is NULL or not string!");
        cJSON_Delete(root);
        return -1;
    }

    // 开始填充MQTT信息
    snprintf(mqtt_server, sizeof(mqtt_server), "tcp://%s:%s", host->valuestring, port->valuestring);  // tcp://mqtt-zt.cz12.cn:8031
    snprintf(mqtt_username, sizeof(mqtt_username), "%s", username->valuestring);
    snprintf(mqtt_password, sizeof(mqtt_password), "%s", password->valuestring);
    snprintf(mqtt_client_id, sizeof(mqtt_client_id), "%s", clientId->valuestring);
    snprintf(mqtt_in_topic, sizeof(mqtt_in_topic), "%s", deviceInTopic->valuestring);
    snprintf(mqtt_out_topic, sizeof(mqtt_out_topic), "%s", deviceOutTopic->valuestring);
    mqtt_keepalive = keepalive->valueint;

    return 0;
}

static int is_mqtt_info_complete()
{
    qrzl_log("mqtt_server     : %s", mqtt_server);
    qrzl_log("mqtt_username   : %s", mqtt_username);
    qrzl_log("mqtt_password   : %s", mqtt_password);
    qrzl_log("mqtt_client_id  : %s", mqtt_client_id);
    qrzl_log("mqtt_in_topic   : %s", mqtt_in_topic);
    qrzl_log("mqtt_out_topic  : %s", mqtt_out_topic);
    qrzl_log("mqtt_keepalive  : %d", mqtt_keepalive);

    if (!mqtt_server || strlen(mqtt_server) == 0) return 0;
    if (!mqtt_username || strlen(mqtt_username) == 0) return 0;
    if (!mqtt_password || strlen(mqtt_password) == 0) return 0;
    if (!mqtt_client_id || strlen(mqtt_client_id) == 0) return 0;
    if (!mqtt_in_topic || strlen(mqtt_in_topic) == 0) return 0;
    if (!mqtt_out_topic || strlen(mqtt_out_topic) == 0) return 0;

    return 1;
}


void* start_xunyou_mqtt_control_client(){

    // 获取MQTT信息
    while (get_remote_mqtt_info() != 0 && is_mqtt_info_complete() == 0)
    {
        qrzl_log("%d秒后重新获取远程接口信息....", retry_get_remote_config);
        sleep(retry_get_remote_config);
    }
    
    xunyou_cloud_client_start();
}