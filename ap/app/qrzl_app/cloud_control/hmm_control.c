#include "stdio.h"
#include "stdlib.h"
#include "string.h"
#include <sys/time.h>
#include <unistd.h>
#include <unistd.h>

#include "hmm_control.h"
#include "../qrzl_utils.h"
#include "MQTTClient.h"
#include "../common_utils/cjson.h"

extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

static char mqtt_server[128] = {0};
static char mqtt_username[128] = {0};
static char mqtt_password[128] = {0};

/* ========================================= start mqtt type 1类型的处理 ==================================================================== */
static pthread_mutex_t t1_msg_handler_lock;  // 定义T1类型mqtt消息处理互斥锁，防止多线程同时操作

static uint32_t t1_publish_interval = 120;
static uint64_t t1_last_publish_flux_bytes = 0;

#ifdef QRZL_APP_CUSTOMIZATION_HMM
#define HMM_MQTT_SERVER2 "tcp://mqtt-zt.lte5.cn:8031"
static int is_enable_hmm_server2 = 0;
#endif

static void t1_mqtt_connlost(void *context, char *cause);
static int t1_message_arrived(void* context, char* topicName, int topicLen, MQTTClient_message* message);
static void t1_on_message_delivered(void* context, MQTTClient_deliveryToken dt);

static int t1_mqtt_connect(MQTTClient* client_p)
{
    int rc;

    MQTTClient_connectOptions conn_opts = MQTTClient_connectOptions_initializer;
    conn_opts.username = mqtt_username;
    conn_opts.password = mqtt_password;
#ifndef QRZL_APP_CUSTOMIZATION_SHAYIN
    conn_opts.keepAliveInterval = 60;
#else
    conn_opts.keepAliveInterval = 30;
#endif 
    conn_opts.cleansession = 1;

    if (client_p == NULL) {
        qrzl_err("client_p is NULL");
    }

    int retry_times = 0;
    while ((rc = MQTTClient_connect(*client_p, &conn_opts)) != MQTTCLIENT_SUCCESS) {
        qrzl_log("MQTT connect failed with code %d. Retrying in 10 seconds...", rc);
        sleep(10);
        retry_times += 10;
#ifndef QRZL_APP_CUSTOMIZATION_HMM_NOT_TWO_SERVER
#ifdef QRZL_APP_CUSTOMIZATION_HMM
        //重试200秒后，切换到另一个服务器。只需要切换一次。客户说10分钟还没连上就换到第二个服务器，但是在尝试连接时也会花时间，所以sleep 200s
        if (retry_times >= 200 && is_enable_hmm_server2 == 0)
        {
            is_enable_hmm_server2 = 1;
            qrzl_log("retry_times: %d, change to hmm server2", retry_times);
            MQTTClient_destroy(client_p);
            MQTTClient_create(client_p, HMM_MQTT_SERVER2, g_qrzl_device_static_data.imei, MQTTCLIENT_PERSISTENCE_NONE, NULL);
            MQTTClient_setCallbacks(*client_p, client_p, t1_mqtt_connlost, t1_message_arrived, t1_on_message_delivered);
        }
#endif
#endif
    }

    char receive_topic[256] = {0};
#ifdef QRZL_APP_CUSTOMIZATION_HMM
    snprintf(receive_topic, sizeof(receive_topic), "iotv3/%s/%s/%s", "ZXIC", "803P42U1701", g_qrzl_device_static_data.imei);
#elif QRZL_APP_CUSTOMIZATION_YITONG
    snprintf(receive_topic, sizeof(receive_topic), "appprod_signTopic/%s", g_qrzl_device_static_data.imei);
#elif QRZL_APP_CUSTOMIZATION_JS
    snprintf(receive_topic, sizeof(receive_topic), "device/jsqr/in/%s", g_qrzl_device_static_data.imei);
#elif QRZL_APP_CUSTOMIZATION_SHAYIN
    snprintf(receive_topic, sizeof(receive_topic), "iotv3/%s/%s/%s", "ZXIC", "803P42U1701", g_qrzl_device_static_data.imei);
#else
    snprintf(receive_topic, sizeof(receive_topic), "device/%s", g_qrzl_device_static_data.imei);
#endif
    qrzl_log("mqtt connected successfully!");
    if ((rc = MQTTClient_subscribe(*client_p, receive_topic, 1)) != MQTTCLIENT_SUCCESS)
    {
        qrzl_log("Failed to subscribe to topic, return code %d", rc);
    }

    qrzl_log("Subscribed to topic: %s", receive_topic);
    return rc;
}

static void t1_on_message_delivered(void* context, MQTTClient_deliveryToken dt)
{
    qrzl_log("Message with token %d delivered", dt);
}

static int t1_publish_device_info(MQTTClient* client_p)
{
    if (!MQTTClient_isConnected(*client_p)) 
    {
        qrzl_log("MQTT not connected, don't publish device info");
        return -1;
    }
    update_device_dynamic_data();
    qrzl_log("start send msg to mqtt broker");
    char topic[256] = {0};
#ifdef QRZL_APP_CUSTOMIZATION_HMM
    snprintf(topic, sizeof(topic), "iotv3/%s/%s/push", "ZXIC", "803P42U1701");
#elif QRZL_APP_CUSTOMIZATION_YITONG
    snprintf(topic, sizeof(topic), "appprod_statusTopic/%s", g_qrzl_device_static_data.imei);
#elif QRZL_APP_CUSTOMIZATION_JS
    snprintf(topic, sizeof(topic), "device/jsqr/out");
#elif QRZL_APP_CUSTOMIZATION_SHAYIN
    snprintf(topic, sizeof(topic), "iotv3/%s/%s/push", "ZXIC", "803P42U1701");
#endif

    uint64_t t1_flow = 0;
    if (g_qrzl_device_dynamic_data.realtime_total_bytes - t1_last_publish_flux_bytes > 0) {
        t1_flow = (g_qrzl_device_dynamic_data.realtime_total_bytes - t1_last_publish_flux_bytes) / 1024;
    } else {
        t1_flow = g_qrzl_device_dynamic_data.realtime_total_bytes / 1024;
    }


#ifdef QRZL_APP_CUSTOMIZATION_HMM
    char payload[2048] = {0};
    snprintf(payload, sizeof(payload), "{");
    snprintf(payload, sizeof(payload), "%s\"imei\":\"%s\"", payload, g_qrzl_device_static_data.imei);

    if(mqtt_server != NULL && strlen(mqtt_server) > 6)
    {
        char *url = mqtt_server+6;
        snprintf(payload, sizeof(payload), "%s,\"url\":\"%s\"", payload, url);
    }

    snprintf(payload, sizeof(payload), "%s,\"iccid\":\"%s\"", payload, g_qrzl_device_dynamic_data.iccid);

    snprintf(payload, sizeof(payload), "%s,\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
    
    snprintf(payload, sizeof(payload), "%s,\"simNum\":%d", payload, 2);
    snprintf(payload, sizeof(payload), "%s,\"remainPwr\":\"%s\"", payload, g_qrzl_device_dynamic_data.remain_power);
    
    char currentTime[64] = {0};
    get_local_time("%Y-%m-%d %H:%M:%S", currentTime, sizeof(currentTime));
    snprintf(payload, sizeof(payload), "%s,\"currentTime\":\"%s\"", payload, currentTime);
    
    snprintf(payload, sizeof(payload), "%s,\"ssid\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_ssid);
    snprintf(payload, sizeof(payload), "%s,\"password\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_key);
    snprintf(payload, sizeof(payload), "%s,\"hidden\":%d", payload, g_qrzl_device_dynamic_data.wifi_hide == 0 ? 1 : 0);
    snprintf(payload, sizeof(payload), "%s,\"connCnt\":%d", payload, g_qrzl_device_dynamic_data.conn_num);
    snprintf(payload, sizeof(payload), "%s,\"rssi\":\"%s\"", payload, g_qrzl_device_dynamic_data.rssi);
    snprintf(payload, sizeof(payload), "%s,\"dayFlow\":%lld", payload, g_qrzl_device_dynamic_data.flux_day_total_bytes/1024); // 这个服务商的流量单位是KB
    snprintf(payload, sizeof(payload), "%s,\"flow\":%lld", payload, t1_flow); // 此次上报用了多少流量
    snprintf(payload, sizeof(payload), "%s,\"monthFlow\":%lld", payload, g_qrzl_device_dynamic_data.flux_month_total_bytes/1024);
    snprintf(payload, sizeof(payload), "%s,\"version\":\"%s\"", payload, g_qrzl_device_static_data.soft_version);
    snprintf(payload, sizeof(payload), "%s,\"speedLimit\":%lld", payload, get_down_limit_net_speed());
    snprintf(payload, sizeof(payload), "%s,\"heartbeat\":%d", payload, t1_publish_interval);
    snprintf(payload, sizeof(payload), "%s,\"disconn\":%d", payload, g_qrzl_device_dynamic_data.user_net_disconn);
    
    snprintf(payload, sizeof(payload), "%s,\"mcc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mcc);    
    snprintf(payload, sizeof(payload), "%s,\"mnc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mnc);    

    snprintf(payload, sizeof(payload), "%s,\"cellID\":\"%s\"", payload, g_qrzl_device_dynamic_data.cid);
    snprintf(payload, sizeof(payload), "%s,\"tac\":\"%s\"", payload, g_qrzl_device_dynamic_data.tac);
    snprintf(payload, sizeof(payload), "%s,\"lac\":\"%s\"", payload, g_qrzl_device_dynamic_data.tac);
    snprintf(payload, sizeof(payload), "%s,\"charging\":%d", payload, get_device_charge_status());
    
    // 以下是simList 列表
    snprintf(payload, sizeof(payload), "%s,\"simList\":[", payload);
    EsimFluxStat esim_fluxstat = get_esim_fluxstat();
    int current_sim_index = get_device_current_sim_index();


    // 真正的外卡
    // 如果有外卡就上传外卡
    // 暂时没有外卡，所以注释掉
    // if (strcmp("RSIM_only", g_qrzl_device_dynamic_data.current_sim) == 0)
    // {
    //     snprintf(payload, sizeof(payload), "%s,{\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
    //     snprintf(payload, sizeof(payload), "%s,\"iccid\":\"%s\"", payload, g_qrzl_device_dynamic_data.iccid);
    //     snprintf(payload, sizeof(payload), "%s,\"isLine\":true}", payload);
    // }
    // else
    // {
    //     snprintf(payload, sizeof(payload), "%s,{\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
    //     snprintf(payload, sizeof(payload), "%s,\"iccid\":\"\"", payload);
    //     snprintf(payload, sizeof(payload), "%s,\"isLine\":false}", payload);
    // }

    // esim1
    snprintf(payload, sizeof(payload), "%s{\"id\":%d", payload, 1);
    snprintf(payload, sizeof(payload), "%s,\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
    snprintf(payload, sizeof(payload), "%s,\"iccid\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim1_iccid);
    char esim1_mno[16] = {0};
    get_isp_name_cn_by_imsi(g_qrzl_device_static_data.nvro_esim1_imsi, esim1_mno, sizeof(esim1_mno));
    snprintf(payload, sizeof(payload), "%s,\"operatorName\":\"%s\"", payload, esim1_mno);
    snprintf(payload, sizeof(payload), "%s,\"flow\":%lld", payload, current_sim_index == 1 ? t1_flow : 0);
    snprintf(payload, sizeof(payload), "%s,\"dayFlow\":%lld", payload, esim_fluxstat.esim1_flux_day_total/1024);
    snprintf(payload, sizeof(payload), "%s,\"monthFlow\":%lld", payload, esim_fluxstat.esim1_flux_month_total/1024);
    snprintf(payload, sizeof(payload), "%s,\"isLine\":%s}", payload, current_sim_index == 1 ? "true" : "false");
    

    // esim2
    snprintf(payload, sizeof(payload), "%s,{\"id\":%d", payload, 2);
    snprintf(payload, sizeof(payload), "%s,\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
    snprintf(payload, sizeof(payload), "%s,\"iccid\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim2_iccid);
    char esim2_mno[16] = {0};
    get_isp_name_cn_by_imsi(g_qrzl_device_static_data.nvro_esim2_imsi, esim2_mno, sizeof(esim2_mno));
    snprintf(payload, sizeof(payload), "%s,\"operatorName\":\"%s\"", payload, esim2_mno);

    snprintf(payload, sizeof(payload), "%s,\"flow\":%lld", payload, current_sim_index == 2 ? t1_flow : 0);
    snprintf(payload, sizeof(payload), "%s,\"dayFlow\":%lld", payload, esim_fluxstat.esim2_flux_day_total/1024);
    snprintf(payload, sizeof(payload), "%s,\"monthFlow\":%lld", payload, esim_fluxstat.esim2_flux_month_total/1024);
    snprintf(payload, sizeof(payload), "%s,\"isLine\":%s}", payload, current_sim_index == 2 ? "true" : "false");
    

    // esim3
    if (strlen(g_qrzl_device_static_data.nvro_esim3_iccid) > 0) {
        snprintf(payload, sizeof(payload), "%s,{\"id\":%d", payload, 3);
        snprintf(payload, sizeof(payload), "%s,\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
        snprintf(payload, sizeof(payload), "%s,\"iccid\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim3_iccid);
        char esim3_mno[16] = {0};
        get_isp_name_cn_by_imsi(g_qrzl_device_static_data.nvro_esim3_imsi, esim3_mno, sizeof(esim3_mno));
        snprintf(payload, sizeof(payload), "%s,\"operatorName\":\"%s\"", payload, esim3_mno);

        snprintf(payload, sizeof(payload), "%s,\"flow\":%lld", payload, current_sim_index == 0 ? t1_flow : 0);
        snprintf(payload, sizeof(payload), "%s,\"dayFlow\":%lld", payload, esim_fluxstat.rsim_flux_day_total/1024);
        snprintf(payload, sizeof(payload), "%s,\"monthFlow\":%lld", payload, esim_fluxstat.rsim_flux_month_total/1024);

        snprintf(payload, sizeof(payload), "%s,\"isLine\":%s}", payload, current_sim_index == 0 ? "true" : "false");
    }
    
    snprintf(payload, sizeof(payload), "%s]", payload);
    // 以上是simList 列表
#elif QRZL_APP_CUSTOMIZATION_YITONG
        char payload[2048] = {0};
        snprintf(payload, sizeof(payload), "{");
        snprintf(payload, sizeof(payload), "%s\"imei\":\"%s\"", payload, g_qrzl_device_static_data.imei);

        snprintf(payload, sizeof(payload), "%s,\"iccid\":\"%s\"", payload, g_qrzl_device_dynamic_data.iccid);

        snprintf(payload, sizeof(payload), "%s,\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);

        snprintf(payload, sizeof(payload), "%s,\"simNum\":%d", payload, 2);
        snprintf(payload, sizeof(payload), "%s,\"remainPwr\":\"%s\"", payload, g_qrzl_device_dynamic_data.remain_power);

        char currentTime[64] = {0};
        get_local_time("%Y%m%d%H%M%S", currentTime, sizeof(currentTime));
        snprintf(payload, sizeof(payload), "%s,\"currentTime\":\"%s\"", payload, currentTime);

        snprintf(payload, sizeof(payload), "%s,\"ssid\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_ssid);
        snprintf(payload, sizeof(payload), "%s,\"password\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_key);
        snprintf(payload, sizeof(payload), "%s,\"hidden\":%d", payload, g_qrzl_device_dynamic_data.wifi_hide == 0 ? 1 : 0);
        snprintf(payload, sizeof(payload), "%s,\"connCnt\":%d", payload, g_qrzl_device_dynamic_data.conn_num);
        snprintf(payload, sizeof(payload), "%s,\"rssi\":\"%s\"", payload, g_qrzl_device_dynamic_data.rssi);
        snprintf(payload, sizeof(payload), "%s,\"dayFlow\":%lld", payload, g_qrzl_device_dynamic_data.flux_day_total_bytes/1024); // 这个服务商的流量单位是KB
        snprintf(payload, sizeof(payload), "%s,\"flow\":%lld", payload, t1_flow); // 此次上报用了多少流量
        snprintf(payload, sizeof(payload), "%s,\"monthFlow\":%lld", payload, g_qrzl_device_dynamic_data.flux_month_total_bytes/1024);
        snprintf(payload, sizeof(payload), "%s,\"version\":\"%s\"", payload, g_qrzl_device_static_data.soft_version);
        snprintf(payload, sizeof(payload), "%s,\"limitspeed\":%lld", payload, get_down_limit_net_speed());
        snprintf(payload, sizeof(payload), "%s,\"heartbeat\":%d", payload, t1_publish_interval);
        snprintf(payload, sizeof(payload), "%s,\"disconn\":%d", payload, g_qrzl_device_dynamic_data.user_net_disconn);

        snprintf(payload, sizeof(payload), "%s,\"cellID\":\"%s\"", payload, g_qrzl_device_dynamic_data.cid);
        snprintf(payload, sizeof(payload), "%s,\"tac\":\"%s\"", payload, g_qrzl_device_dynamic_data.tac);
        snprintf(payload, sizeof(payload), "%s,\"charging\":%d", payload, get_device_charge_status());

        int current_sim_index = get_device_current_sim_index();
        snprintf(payload, sizeof(payload), "%s,\"simcurrent\":\"%d\"", payload, current_sim_index == 0 ? 1 : current_sim_index+1); // 这里没有考虑有实卡的情况

        // 以下是simList 列表
        snprintf(payload, sizeof(payload), "%s,\"simList\":[", payload);
        EsimFluxStat esim_fluxstat = get_esim_fluxstat();


        // 真正的外卡
        // 如果有外卡就上传外卡
        // 暂时没有外卡，所以注释掉
        // if (strcmp("RSIM_only", g_qrzl_device_dynamic_data.current_sim) == 0)
        // {
        //     snprintf(payload, sizeof(payload), "%s,{\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
        //     snprintf(payload, sizeof(payload), "%s,\"iccid\":\"%s\"", payload, g_qrzl_device_dynamic_data.iccid);
        //     snprintf(payload, sizeof(payload), "%s,\"isLine\":true}", payload);
        // }
        // else
        // {
        //     snprintf(payload, sizeof(payload), "%s,{\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
        //     snprintf(payload, sizeof(payload), "%s,\"iccid\":\"\"", payload);
        //     snprintf(payload, sizeof(payload), "%s,\"isLine\":false}", payload);
        // }

        // 之前阿乐卡瞎搞的版本，卡一是外卡，卡二是esim1，卡三是esim2

        // esim3
        if (strlen(g_qrzl_device_static_data.nvro_esim3_iccid) > 0) {
            snprintf(payload, sizeof(payload), "%s{\"id\":%d", payload, 1);
            snprintf(payload, sizeof(payload), "%s,\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
            snprintf(payload, sizeof(payload), "%s,\"iccid1\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim3_iccid);
            snprintf(payload, sizeof(payload), "%s,\"isLine\":%s}", payload, current_sim_index == 0 ? "true" : "false");
        } else {
            snprintf(payload, sizeof(payload), "%s{\"id\":%d", payload, 1);
            snprintf(payload, sizeof(payload), "%s,\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
            snprintf(payload, sizeof(payload), "%s,\"iccid1\":\"%s\"", payload, "00000000000000000000");
            snprintf(payload, sizeof(payload), "%s,\"isLine\":%s}", payload, current_sim_index == 0 ? "true" : "false");
        }

        // esim1 
        snprintf(payload, sizeof(payload), "%s,{\"id\":%d", payload, 2);
        snprintf(payload, sizeof(payload), "%s,\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
        snprintf(payload, sizeof(payload), "%s,\"iccid2\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim1_iccid);
        snprintf(payload, sizeof(payload), "%s,\"isLine\":%s}", payload, current_sim_index == 1 ? "true" : "false");


        // esim2
        snprintf(payload, sizeof(payload), "%s,{\"id\":%d", payload, 3);
        snprintf(payload, sizeof(payload), "%s,\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
        snprintf(payload, sizeof(payload), "%s,\"iccid3\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim2_iccid);
        snprintf(payload, sizeof(payload), "%s,\"isLine\":%s}", payload, current_sim_index == 2 ? "true" : "false");


        snprintf(payload, sizeof(payload), "%s]", payload);
#else
        char payload[2048] = {0};
        snprintf(payload, sizeof(payload), "{");
        snprintf(payload, sizeof(payload), "%s\"imei\":\"%s\"", payload, g_qrzl_device_static_data.imei);

        snprintf(payload, sizeof(payload), "%s,\"iccid\":\"%s\"", payload, g_qrzl_device_dynamic_data.iccid);

        snprintf(payload, sizeof(payload), "%s,\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);

        snprintf(payload, sizeof(payload), "%s,\"simNum\":%d", payload, 2);
        snprintf(payload, sizeof(payload), "%s,\"remainPwr\":\"%s\"", payload, g_qrzl_device_dynamic_data.remain_power);

        char currentTime[64] = {0};
        get_local_time("%Y-%m-%d %H:%M:%S", currentTime, sizeof(currentTime));
        snprintf(payload, sizeof(payload), "%s,\"currentTime\":\"%s\"", payload, currentTime);

        snprintf(payload, sizeof(payload), "%s,\"ssid\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_ssid);
        snprintf(payload, sizeof(payload), "%s,\"password\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_key);
        snprintf(payload, sizeof(payload), "%s,\"hidden\":%d", payload, g_qrzl_device_dynamic_data.wifi_hide == 0 ? 1 : 0);
        snprintf(payload, sizeof(payload), "%s,\"connCnt\":%d", payload, g_qrzl_device_dynamic_data.conn_num);
        snprintf(payload, sizeof(payload), "%s,\"rssi\":\"%s\"", payload, g_qrzl_device_dynamic_data.rssi);
        snprintf(payload, sizeof(payload), "%s,\"dayFlow\":%lld", payload, g_qrzl_device_dynamic_data.flux_day_total_bytes/1024); // 这个服务商的流量单位是KB
        snprintf(payload, sizeof(payload), "%s,\"monthFlow\":%lld", payload, g_qrzl_device_dynamic_data.flux_month_total_bytes/1024);
        snprintf(payload, sizeof(payload), "%s,\"version\":\"%s\"", payload, g_qrzl_device_static_data.soft_version);
        snprintf(payload, sizeof(payload), "%s,\"speedLimit\":%lld", payload, get_down_limit_net_speed());
        snprintf(payload, sizeof(payload), "%s,\"heartbeat\":%d", payload, t1_publish_interval);
        snprintf(payload, sizeof(payload), "%s,\"disconn\":%d", payload, g_qrzl_device_dynamic_data.user_net_disconn);

        snprintf(payload, sizeof(payload), "%s,\"cellID\":\"%s\"", payload, g_qrzl_device_dynamic_data.cid);
        snprintf(payload, sizeof(payload), "%s,\"tac\":\"%s\"", payload, g_qrzl_device_dynamic_data.tac);
        snprintf(payload, sizeof(payload), "%s,\"charging\":%d", payload, get_device_charge_status());

        // 以下是simList 列表
        snprintf(payload, sizeof(payload), "%s,\"simList\":[", payload);
        EsimFluxStat esim_fluxstat = get_esim_fluxstat();
        int current_sim_index = get_device_current_sim_index();


        // 真正的外卡
        // 如果有外卡就上传外卡
        // 暂时没有外卡，所以注释掉
        // if (strcmp("RSIM_only", g_qrzl_device_dynamic_data.current_sim) == 0)
        // {
        //     snprintf(payload, sizeof(payload), "%s,{\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
        //     snprintf(payload, sizeof(payload), "%s,\"iccid\":\"%s\"", payload, g_qrzl_device_dynamic_data.iccid);
        //     snprintf(payload, sizeof(payload), "%s,\"isLine\":true}", payload);
        // }
        // else
        // {
        //     snprintf(payload, sizeof(payload), "%s,{\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
        //     snprintf(payload, sizeof(payload), "%s,\"iccid\":\"\"", payload);
        //     snprintf(payload, sizeof(payload), "%s,\"isLine\":false}", payload);
        // }

        // esim1
        snprintf(payload, sizeof(payload), "%s{\"id\":%d", payload, 1);
        snprintf(payload, sizeof(payload), "%s,\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
        snprintf(payload, sizeof(payload), "%s,\"iccid\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim1_iccid);
        snprintf(payload, sizeof(payload), "%s,\"isLine\":%s}", payload, current_sim_index == 1 ? "true" : "false");


        // esim2
        snprintf(payload, sizeof(payload), "%s,{\"id\":%d", payload, 2);
        snprintf(payload, sizeof(payload), "%s,\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
        snprintf(payload, sizeof(payload), "%s,\"iccid\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim2_iccid);
        snprintf(payload, sizeof(payload), "%s,\"isLine\":%s}", payload, current_sim_index == 2 ? "true" : "false");


        // esim3
        if (strlen(g_qrzl_device_static_data.nvro_esim3_iccid) > 0) {
            snprintf(payload, sizeof(payload), "%s,{\"id\":%d", payload, 3);
            snprintf(payload, sizeof(payload), "%s,\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
            snprintf(payload, sizeof(payload), "%s,\"iccid\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim3_iccid);
            snprintf(payload, sizeof(payload), "%s,\"isLine\":%s}", payload, current_sim_index == 0 ? "true" : "false");
        }

        snprintf(payload, sizeof(payload), "%s]", payload);
#endif



    snprintf(payload, sizeof(payload), "%s}", payload);

    qrzl_log("mqtt payload: %s", payload);

    printf("\n printf -> mqtt payload: %s\n", payload);

    MQTTClient_message pubmsg = MQTTClient_message_initializer;
    pubmsg.payload = payload;
    pubmsg.payloadlen = (int)strlen(payload);
    pubmsg.qos = 0;
    pubmsg.retained = 0;

    MQTTClient_deliveryToken token;
    int rc = MQTTClient_publishMessage(*client_p, topic, &pubmsg, &token);
    if (rc != MQTTCLIENT_SUCCESS) {
        qrzl_err("Failed to publish message, return code %d", rc);
    } else {
        qrzl_log("Message published!");
        t1_last_publish_flux_bytes = g_qrzl_device_dynamic_data.realtime_total_bytes;
    }
    return 0;
}

static void *mqtt_disconnect_reconn(void *arg)
{
    sleep(30);
    MQTTClient *client_p = (MQTTClient *)arg;  // 进行类型转换

    qrzl_log("in thread disconn MQTT ...");
    MQTTClient_disconnect(*client_p, 0);
    t1_mqtt_connect(client_p);
    t1_publish_device_info(client_p);
    return NULL;
}

static void *mqtt_disconnect_update_server(void *arg)
{
    sleep(10);
    MQTTClient *client_p = (MQTTClient *)arg;  // 进行类型转换

    qrzl_log("in thread disconn MQTT ...");
    MQTTClient_disconnect(*client_p, 0);
    MQTTClient_destroy(client_p);
    // 重新设置MQTT的服务器信息
    MQTTClient_create(client_p, mqtt_server, g_qrzl_device_static_data.imei, MQTTCLIENT_PERSISTENCE_NONE, NULL);
    MQTTClient_setCallbacks(*client_p, client_p, t1_mqtt_connlost, t1_message_arrived, t1_on_message_delivered);
    // 连接MQTT服务器
    t1_mqtt_connect(client_p);

    t1_publish_device_info(client_p);
    return NULL;
}

static int t1_order_msg_handler(cJSON* j_value, MQTTClient* client_p)
{
    int ret;

    cJSON *j_mq_url = cJSON_GetObjectItem(j_value, "url");
    cJSON *j_mq_username = cJSON_GetObjectItem(j_value, "userName");
    cJSON *j_mq_password = cJSON_GetObjectItem(j_value, "password");
    if (j_mq_url != NULL && j_mq_username != NULL && j_mq_password != NULL &&
        cJSON_IsString(j_mq_url) && cJSON_IsString(j_mq_username) && cJSON_IsString(j_mq_password))
    {
        snprintf(mqtt_server, sizeof(mqtt_server), "tcp://%s", j_mq_url->valuestring);
        snprintf(mqtt_username, sizeof(mqtt_username), "%s", j_mq_username->valuestring);
        snprintf(mqtt_password, sizeof(mqtt_password), "%s", j_mq_password->valuestring);
        cfg_set("qrzl_cloud_mqtt_server", mqtt_server);
        cfg_set("qrzl_cloud_mqtt_username", mqtt_username);
        cfg_set("qrzl_cloud_mqtt_password", mqtt_password);

        // 创建断开线程
        pthread_t thread;
        pthread_create(&thread, NULL, mqtt_disconnect_update_server, (void *)client_p);
        pthread_detach(thread);  // 让线程自动回收

        return 0;
    }

    cJSON *j_reset = cJSON_GetObjectItem(j_value, "reSet");
    if (j_reset != NULL && cJSON_IsNumber(j_reset))
    {
        if (j_reset->valueint == 1)
        {
            ret = reset_device();
            return ret;
        }
    }

    cJSON *j_reboot = cJSON_GetObjectItem(j_value, "reboot");
    if (j_reboot != NULL && cJSON_IsNumber(j_reboot))
    {
        if (j_reboot->valueint == 1)
        {
            ret = restart_device();
            return ret;
        }
    }

    cJSON *j_ssid = cJSON_GetObjectItem(j_value, "ssid");
    cJSON *j_password = cJSON_GetObjectItem(j_value, "password");
    if (j_ssid != NULL && cJSON_IsString(j_ssid) && j_password != NULL && cJSON_IsString(j_password))
    {
        struct wifi_config_t wifi_config = {};
        init_wifi_config_value(&wifi_config);
        snprintf(wifi_config.ssid, sizeof(wifi_config.ssid), "%s", j_ssid->valuestring);
        snprintf(wifi_config.key, sizeof(wifi_config.key), "%s", j_password->valuestring);
        update_wifi_by_config(&wifi_config);
        t1_publish_device_info(client_p);
        return 0;
    }

    cJSON *j_hidden = cJSON_GetObjectItem(j_value, "hidden");
    if (j_hidden != NULL && cJSON_IsNumber(j_hidden))
    {
        struct wifi_config_t wifi_config = {};
        init_wifi_config_value(&wifi_config);
        // 不知道哪个神人客户写的文档 hidden 1 是显示, 0是隐藏
        if (j_hidden->valueint == 1) {
            wifi_config.hide = 0;
        } else{
            wifi_config.hide = 1;
        }
        update_wifi_by_config(&wifi_config);
        t1_publish_device_info(client_p);
        return 0;
    }

    cJSON* j_get = cJSON_GetObjectItem(j_value, "get");
    if (j_get != NULL && cJSON_IsBool(j_get))
    {
        if (cJSON_IsTrue(j_get))
        {
            ret = t1_publish_device_info(client_p);
            return ret;
        }
    }

    cJSON* j_disconn = cJSON_GetObjectItem(j_value, "disconn");
    if (j_disconn != NULL && cJSON_IsNumber(j_disconn))
    {
        if (j_disconn->valueint == 0)
        {
            set_network_br0_disconnect(0);
        }
        else if (j_disconn->valueint == 1)
        {
            set_network_br0_disconnect(1);
        }
        t1_publish_device_info(client_p);
        return -1;
    }

    cJSON* j_speed_limit = cJSON_GetObjectItem(j_value, "speedLimit");
    if (j_speed_limit != NULL && cJSON_IsNumber(j_speed_limit))
    {
        limit_net_speed(j_speed_limit->valueint, j_speed_limit->valueint);
        t1_publish_device_info(client_p);
        return 0;
    }

    cJSON* j_day_flow = cJSON_GetObjectItem(j_value, "dayFlow");
    if (j_day_flow != NULL && cJSON_IsNumber(j_day_flow) && j_day_flow->valueint >= 0)
    {
        char flux_day_total[21] = {0};
        uint64_t day_flow = j_day_flow->valueint * 1024;
        qrzl_log("hmm -> uint64 day_flow vlaue :%lld", day_flow);
        snprintf(flux_day_total, sizeof(flux_day_total), "%lld", day_flow);
        cfg_set("flux_day_total", flux_day_total);
        t1_publish_device_info(client_p);
        return 0;
    }

    cJSON* j_month_flow = cJSON_GetObjectItem(j_value, "monthFlow");
    if (j_month_flow != NULL && cJSON_IsNumber(j_month_flow) && j_month_flow->valueint >= 0)
    {
        char flux_month_total[21] = {0};
        uint64_t month_flow = j_month_flow->valueint * 1024;
        qrzl_log("hmm -> uint64 mothFlow vlaue :%lld", month_flow);
        snprintf(flux_month_total, sizeof(flux_month_total), "%lld", month_flow);
        cfg_set("flux_month_total", flux_month_total);
        t1_publish_device_info(client_p);
        return 0;
    }


    // 获取指令值
    cJSON* j_sim_day_flow = cJSON_GetObjectItem(j_value, "dayFlowSIM");
    cJSON* j_day_sim_id = cJSON_GetObjectItem(j_value, "id");
    if(j_sim_day_flow != NULL && cJSON_IsNumber(j_sim_day_flow) && j_sim_day_flow->valueint >= 0
        && j_day_sim_id != NULL && cJSON_IsNumber(j_day_sim_id)
    ) {
        qrzl_log("hmm -> user into dayFlowSIM value :%d", j_sim_day_flow->valueint);
        qrzl_log("hmm -> user into sim_id value :%d", j_day_sim_id->valueint);
        // KB 转 bytes
        uint64_t day_flow = j_sim_day_flow->valueint * 1024;
        // 调用流量设置函数
        if(j_day_sim_id->valueint == 3) {
            set_month_day_flow_by_sim(day_flow, 0, 0);
        } else {
            set_month_day_flow_by_sim(day_flow, j_day_sim_id->valueint, 0);
        }
        qrzl_log("hmm -> dayflow set ok!");
        // 立即推送一次
        t1_publish_device_info(client_p);
        return 0;
    }

    // 获取指令值
    cJSON* j_sim_month_flow = cJSON_GetObjectItem(j_value, "monthFlowSIM");
    cJSON* j_sim_id = cJSON_GetObjectItem(j_value, "id");
    if(j_sim_month_flow != NULL && cJSON_IsNumber(j_sim_month_flow) && j_sim_month_flow->valueint >= 0
        && j_sim_id != NULL && cJSON_IsNumber(j_sim_id)
    ) {
        qrzl_log("hmm -> user into monthFlowSIM value :%d", j_sim_month_flow->valueint);
        qrzl_log("hmm -> user into sim_id value :%d", j_sim_id->valueint);
        // KB 转 bytes
        uint64_t month_flow = j_sim_month_flow->valueint * 1024;
        // 调用流量设置函数
        if(j_sim_id->valueint == 3) {
            set_month_day_flow_by_sim(month_flow, 0, 1);
        } else {
            set_month_day_flow_by_sim(month_flow, j_sim_id->valueint, 1);
        }
        qrzl_log("hmm -> monthflow set ok!");
        // 立即推送一次
        t1_publish_device_info(client_p);
        return 0;
    }
    
    cJSON* j_switch = cJSON_GetObjectItem(j_value, "switch");
    if (j_switch != NULL && cJSON_IsString(j_switch))
    {

        int switch_crad_count = 0;
        while (g_qrzl_device_dynamic_data.is_test_net == 1 && switch_crad_count < 30)
        {
            qrzl_log("正在测网，不能切卡");
            sleep(5);
            switch_crad_count++;
        }


        int ret;
        int tmp = atoi(j_switch->valuestring);

        if (tmp > 0 && tmp < 4) {
            //在二次认证中，从开启认证的卡切到没开启认证的卡会重启（SHAYIN移动->联通）
            //这里关掉让回调去重连
#ifndef QRZL_APP_CUSTOMIZATION_SHAYIN
            // 创建断开线程
            pthread_t thread;
            pthread_create(&thread, NULL, mqtt_disconnect_reconn, (void *)client_p);
            pthread_detach(thread);  // 让线程自动回收
#endif
        }

#ifdef QRZL_APP_CUSTOMIZATION_YITONG
        if (tmp == 1)
        {
            ret = switch_sim_card_not_restart(0);
        }
        else if (tmp == 2)
        {
            ret = switch_sim_card_not_restart(2);
        }
        else if (tmp == 3)
        {
            ret = switch_sim_card_not_restart(1);
        }
#else
        if (tmp == 1)
        {
            ret = switch_sim_card_not_restart(1);
        }
        else if (tmp == 2)
        {
            ret = switch_sim_card_not_restart(2);
        }
        else if (tmp == 3)
        {
            ret = switch_sim_card_not_restart(0);
        }
#endif
        return 0;
    }
    
    cJSON* j_heartbeat = cJSON_GetObjectItem(j_value, "heartbeat");
    if (j_heartbeat != NULL && cJSON_IsString(j_heartbeat))
    {
        int tmp = atoi(j_heartbeat->valuestring);
        if (tmp < 30)
        {
            qrzl_err("心跳间隔时间不能小于30秒");
        }
        else
        {
            t1_publish_interval = tmp;
        }
        
        t1_publish_device_info(client_p);
        return 0;
    }
    return 0;
}

static int t1_message_arrived(void* context, char* topicName, int topicLen, MQTTClient_message* message)
{
    qrzl_log("Message arrived on topic: %s", topicName);
    qrzl_log("Message: %.*s", message->payloadlen, (char*)message->payload);
    MQTTClient* client_p = (MQTTClient*)context;

    cJSON *j_value = cJSON_Parse((char*)message->payload);
    if (j_value != NULL)
    {
        if (cJSON_IsObject(j_value))
        {
            qrzl_err("json object normal");
            update_device_dynamic_data();
            t1_order_msg_handler(j_value, client_p);
        }
        // 释放 JSON 解析结果
        cJSON_Delete(j_value);
    }
    
    MQTTClient_freeMessage(&message);
    MQTTClient_free(topicName);

    return 1;
}

static void t1_mqtt_connlost(void *context, char *cause)
{
    qrzl_log("MQTT Connection lost. cause: %s", cause);

    qrzl_log("Attempting to reconnect...");
    MQTTClient* client_p = (MQTTClient*)context;
    t1_mqtt_connect(client_p);
    t1_publish_device_info(client_p);
}

static void* t1_publish_loop(void* client_ptr) {
    qrzl_log("t1_publish_loop start");
    MQTTClient* client_p = (MQTTClient*)client_ptr;
    uint32_t now_sleep_time_total = 600;
    uint32_t sleep_time = 3;
    int first_ten_count = 0;
    int current_conn_num = 10; // 默认给10，这是为了后面判断方便
    char sta_count[3] = {0};
    int ret;

    while (1) {
        now_sleep_time_total += sleep_time;
#ifdef JCV_HW_MZ804_V1_4
    ret = cfg_get_item("sta_count", sta_count, 3);
    if (ret == 0) {
        current_conn_num = atoi(sta_count);
    }
    if (current_conn_num > 0) {
        if (now_sleep_time_total >= t1_publish_interval) {
            if(first_ten_count < 10) {
                first_ten_count++;
                qrzl_log("first_ten_count: %d", first_ten_count);
            } else {
                t1_publish_interval = 300;
            }
            t1_publish_device_info(client_p);
            now_sleep_time_total = 0;
        }
    } else {
        if (now_sleep_time_total >= t1_publish_interval) {
            if(first_ten_count < 10) {
                first_ten_count++;
                qrzl_log("first_ten_count: %d", first_ten_count);
            } else {
                t1_publish_interval = 600;
            }
            t1_publish_device_info(client_p);
            now_sleep_time_total = 0;
        }
    }
#else
    if (now_sleep_time_total >= t1_publish_interval) {
        if(first_ten_count < 10) {
            first_ten_count++;
            qrzl_log("first_ten_count: %d", first_ten_count);
        } else {
            t1_publish_interval = 300;
        }
        t1_publish_device_info(client_p);
        now_sleep_time_total = 0;
    }
#endif

        sleep(sleep_time);
    }
    return NULL;
}

static void t1_cloud_client_start()
{    
    if (pthread_mutex_init(&t1_msg_handler_lock, NULL) != 0) {
        qrzl_log("t1_msg_handler_lock init failed\n");
    }

    MQTTClient client;

    char client_id[128] = {0};
#ifdef QRZL_APP_CUSTOMIZATION_JS
    snprintf(client_id, sizeof(client_id), "jsqr_%s", g_qrzl_device_static_data.imei);
#else
    snprintf(client_id, sizeof(client_id), "%s", g_qrzl_device_static_data.imei);
#endif
    MQTTClient_create(&client, mqtt_server, client_id, MQTTCLIENT_PERSISTENCE_NONE, NULL);
    MQTTClient_setCallbacks(client, &client, t1_mqtt_connlost, t1_message_arrived, t1_on_message_delivered);

    t1_mqtt_connect(&client);

    pthread_t pub_thread;
    if (pthread_create(&pub_thread, NULL, t1_publish_loop, &client) != 0) {
        qrzl_err("Failed to create publish thread");
    }

    while (1)
    {
        sleep(1); // 主循环间隔
    }

    MQTTClient_disconnect(client, 10000);
    MQTTClient_destroy(&client);
    pthread_mutex_destroy(&t1_msg_handler_lock);
    return;
}

/* ========================================= end mqtt type 1类型的处理 ==================================================================== */


/* ========================================= end mqtt ky 类型的处理 ==================================================================== */

void* hmm_control_start()
{
    qrzl_log("hmm_control_start");

    update_device_static_data();

    char mqtt_request_type[20] = {0};
    cfg_get_item("qrzl_cloud_mqtt_server", mqtt_server, sizeof(mqtt_server));
    cfg_get_item("qrzl_cloud_mqtt_username", mqtt_username, sizeof(mqtt_username));
    cfg_get_item("qrzl_cloud_mqtt_password", mqtt_password, sizeof(mqtt_password));

#ifdef QRZL_APP_CUSTOMIZATION_JS
    qrzl_log("QRZL_APP_CUSTOMIZATION_JS");
    memset(mqtt_username, 0, sizeof(mqtt_username));
    snprintf(mqtt_username, sizeof(mqtt_username), "jsqr_%s", g_qrzl_device_static_data.imei);
#endif

    qrzl_log("qrzl_cloud_mqtt_type: %s, server: %s, username: %s, password: %s", mqtt_request_type, mqtt_server, mqtt_username, mqtt_password);

    t1_cloud_client_start();
    
    return NULL;
}