#ifdef QRZL_CS_FOTA
#include "fota_common.h"
#include "fota_utils.h"
#include "os_type_def.h"
#include "../qrzl_utils.h"
#include "../common_utils/md5.h"
#include "../qrzl_utils.h"


extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;


/**
 * 检查指定路径的文件是否存在且可读
 * 
 * 该函数验证文件路径的有效性，并通过系统调用检查文件的可访问性。
 * 
 * @param path 指向文件路径字符串的指针，路径不能为空指针或空字符串
 * @return 返回布尔值表示文件状态：
 *         TRUE  - 文件存在且可读
 *         FALSE - 路径无效或文件不可访问（包括不存在、无权限等情况）
 */
int fota_is_file_exist(const char* path)
{
    // 验证路径有效性：非空指针且非空字符串
    if((path == NULL) || (*path == '\0'))
        return FALSE;
    
    // 使用access系统调用检查文件可读权限
    if(access(path, R_OK) != 0)
        return FALSE;
    
    return TRUE;
}

int fota_write_file(const char*path, const char*value, int size)
{
    int fd = open(path, O_WRONLY | O_CREAT | O_SYNC, 777);
    if(fd < 0) {
        qrzl_log("failed to open %s: %s\n", path, strerror(errno));
        return -1;
    }
    if(write(fd, value, size) != size) {
        qrzl_log("failed to write %s:%s\n", path, strerror(errno));
        close(fd);
        return -1;
    }
    close(fd);
    return 0;
}

int fota_write_file_int(const char*path, int value)
{
    char buffer[32]  = {0};
    sprintf(buffer, "%d", value);
    return fota_write_file(path, buffer, strlen(buffer));

}

int fota_mkdirs(const char* path, mode_t mode)
{
    char buf[128];
    const char *slash;
    const char *p = path;
    int width;
    int ret;

    struct stat info;
    while((slash = strchr(p, '/')) != NULL) {
        width = slash - path;
        p = slash + 1;
        if(width < 0)
            break;
        if(width == 0)
            continue;
        if((unsigned int)width > sizeof(buf) - 1) {
            assert(0);
            return -1;
        }
        memcpy(buf, path, width);
        buf[width] = 0;
        if(stat(buf, &info) != 0) {
            ret = mkdir(buf, mode);
            if(ret && errno != EEXIST)
                return ret;
        }
    }
    ret = mkdir(path, mode);
    if(ret && errno != EEXIST)
        return ret;
    return 0;
}

int fota_check_and_make_fota_dir(void)
{
    int bexist = FALSE;
    int ret = -1;
    bexist = fota_is_file_exist(ZTE_FOTA_MAIN_PATH);
    if(!bexist) {
        ret = fota_mkdirs(ZTE_FOTA_MAIN_PATH, S_IRWXU);
        if(ret < 0) {
            //LOGE("failed to mkdir %s\n", ZTE_FOTA_MAIN_PATH);
            qrzl_log("failed to mkdir %s\n", ZTE_FOTA_MAIN_PATH);
            return -1;
        }
    }
    if(!fota_is_file_exist(FOTA_UPDATE_STATUS_FILE)) {
        /*file not exist, just create it*/
        fota_write_file_int(FOTA_UPDATE_STATUS_FILE, ZTE_DUA_NO_NEED_UPDATE);
    }
    return 0;
}


/*
*fota通用升级函数，当函数执行到最后时是不会有返回值
*返回1则fota状态文件打开失败
*返回2则fota状态文件读取失败
*返回3则差分包fota_upi校验失败
*/
int fota_update(void)
{
    int fota_fd;
    char status_char[16];
    fota_fd = open(FOTA_UPDATE_STATUS_FILE,O_RDONLY);
    if(fota_fd<=0){
        return 1;
    }
    qrzl_log("fota状态文件打开成功");
    lock_system;
    qrzl_log("差分包fota_upi校验开始");
    system("fota_upi -u verify");
    while(1)
    {
        lseek(fota_fd,0,SEEK_SET);
        memset(status_char,0,sizeof(status_char));
        if(read(fota_fd,status_char,sizeof(status_char))<=0){
            close(fota_fd);
            unlock_system;
            return 2;
        }
        else{
            if(atoi(status_char) == ZTE_DUA_VERIFY_FAIL){
                close(fota_fd);
                unlock_system;
                return 3;
            }
            else if(atoi(status_char) == ZTE_DUA_VERIFY_SUCCESS){
                qrzl_log("差分包fota_upi校验成功");
                break;
            }
        }
    }
    qrzl_log("fota升级开始，进入recovery系统");
    system("fota_upi -u recovery");
    qrzl_log("foat");
    unlock_system;
    return 0;
}

/**
 * @brief 计算文件的MD5哈希值
 * 
 * @param filepath 文件路径
 * @param hash_output 存储哈希值的输出缓冲区，至少需要33字节(32字符+结束符)
 * @param output_len 输出缓冲区长度
 * @return int 0表示成功，-1表示失败
 */
int fota_get_file_hash_from_path(const char *filepath, char *hash_output, size_t output_len) {
    FILE *file = NULL;
    lpa_MD5_CTX md5_ctx;
    unsigned char buffer[1024];
    unsigned char digest[16];
    size_t bytes_read;
    int i;

    if(!filepath){

    }else{
        qrzl_log("filepath:%s",filepath);
    }

    if(!hash_output){
    }else{
        qrzl_log("hash_output");
    }
    
    if(output_len < (16 * 2 + 1)){
    }
    else{
        qrzl_log("output_len:%d",output_len);
    }    
    
    // 检查参数
    if (!filepath || !hash_output || output_len < (16 * 2 + 1)) {
        qrzl_err("Invalid parameters for get_file_hash_from_path");
        return -1;
    }
    
    // 打开文件
    file = fopen(filepath, "rb");
    if (!file) {
        qrzl_err("Failed to open file: %s", filepath);
        return -1;
    }
    
    // 初始化MD5上下文
    lpa_MD5_Init(&md5_ctx);
    
    // 读取文件并更新MD5
    while ((bytes_read = fread(buffer, 1, sizeof(buffer), file)) != 0) {
        lpa_MD5_Update(&md5_ctx, buffer, bytes_read);
    }
    
    // 完成MD5计算
    lpa_MD5_Final(digest, &md5_ctx);
    
    // 关闭文件
    fclose(file);
    
    // 将二进制摘要转换为十六进制字符串
    for (i = 0; i < 16; i++) {
        sprintf(&hash_output[i*2], "%02x", digest[i]);
    }
    
    hash_output[16 * 2] = '\0'; // 添加字符串结束符
    
    qrzl_log("File %s MD5: %s", filepath, hash_output);
    
    return 0; // 成功
}
#endif
