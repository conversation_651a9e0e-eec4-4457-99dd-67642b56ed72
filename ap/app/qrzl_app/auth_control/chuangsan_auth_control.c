#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <signal.h>
#include <errno.h>
#include <pthread.h>
#include <curl/curl.h>
#include <ctype.h>
#include <sys/time.h>

#include "../common_utils/cjson.h"
#include "../qrzl_utils.h"
#include "softap_api.h"
#include "nv_api.h"

extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

#define QRZL_HTTP_RESPONSE_MAX 5120
#define QRZL_HTTP_REQUEST_BODY_MAX 2048

#define KEY_ONE_LINK_GET_TOKEN_URL       "ONE_LINK_customers_get_token_url"

static char one_link_customers_get_token_url[256] = {0};

static size_t http_write_callback(void *contents, size_t size, size_t nmemb, void *userp) {
    size_t totalSize = size * nmemb;
    char *response = (char *)userp;

    size_t currentLen = strlen(response);
    size_t remaining = QRZL_HTTP_RESPONSE_MAX - currentLen - 1; // 预留 '\0'

    if (remaining <= 0) {
        qrzl_err("http响应缓冲区已满，截断");
        return totalSize;  // 避免 libcurl 报错
    }

    size_t copyLen = totalSize < remaining ? totalSize : remaining;
    memcpy(response + currentLen, contents, copyLen);
    response[currentLen + copyLen] = '\0';

    return totalSize;
}


static int https_send_post_request(const char *url, const char *body, char *response)
{
    CURL *curl;
    CURLcode res;
    qrzl_log("http request url: %s", url);
    qrzl_log("http request body: %s", body);

    curl = curl_easy_init();
    if (curl) {
        curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
        curl_easy_setopt(curl, CURLOPT_URL, url);
        curl_easy_setopt(curl, CURLOPT_POST, 1L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

        // 设置POST请求的内容
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, body);

        // 设置HTTP头（告诉服务器这是JSON数据）
        struct curl_slist *headers = NULL;
        headers = curl_slist_append(headers, "Content-Type: application/json");
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        // 设置 Basic 认证的用户名和密码
        curl_easy_setopt(curl, CURLOPT_USERPWD, "qrui:Qr@2025");

        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L); 
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
        res = curl_easy_perform(curl);
        if (res != CURLE_OK) {
            qrzl_err("curl_easy_perform() failed: %s", curl_easy_strerror(res));
        } else {
            qrzl_log("http Response: %s\n", response);
        }
        // 清理
        curl_slist_free_all(headers);
        curl_easy_cleanup(curl);
        if (res != CURLE_OK)
        {
            return -1;
        }
        return 0;
    }
    return -1;
}

/**
 * 初始化配置信息
 */
int init_customers_info()
{
    int res_url = cfg_get_item(KEY_ONE_LINK_GET_TOKEN_URL, one_link_customers_get_token_url, sizeof(one_link_customers_get_token_url));
    if (res_url != 0) {
        qrzl_log("Configuration item %s get error!", KEY_ONE_LINK_GET_TOKEN_URL);
    }
}

// 获取当前本地时间字符串，格式："YYYY-MM-DD HH:MM:SS"
static void get_local_datetime(char *buffer, size_t size) {
    if (!buffer || size < 20) return;  // 安全检查：最小需 20 字节

    time_t t = time(NULL);
    struct tm *tm_info = localtime(&t);
    strftime(buffer, size, "%Y-%m-%d %H:%M:%S", tm_info);
}

// =============================== 移动认证相关接口 =============================================

/**
 * 发送短信
 * @param iccid 
 * @param mac 设备mac
 * @param terminalMac 终端mac
 * @param phoneNum 手机号码
 * @return -1 请求失败， 0 请求成功
 */
int cs_one_link_send_sms(char *iccid, char *mac, char *terminalMac, char *phoneNum) {

    init_customers_info();

    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char response_body[QRZL_HTTP_RESPONSE_MAX] = {0};
    char *inteface_str = "/access/send_code";
    
    // 拼接请求地址
    snprintf(request_url, sizeof(request_url), "%s%s", one_link_customers_get_token_url, inteface_str);

    char convered_mac[33] = {0};
    char convered_terminalMac[33] = {0};
    convert_mac_format(mac, convered_mac, sizeof(convered_mac), ':');
    convert_mac_format(terminalMac, convered_terminalMac, sizeof(convered_terminalMac), ':');

    // 构造json
    cJSON *root = cJSON_CreateObject();
    cJSON_AddStringToObject(root, "simIccid", iccid);
    cJSON_AddStringToObject(root, "mac", convered_mac);
    cJSON_AddStringToObject(root, "terminalMac", convered_terminalMac);
    cJSON_AddStringToObject(root, "phoneNum", phoneNum);

    char *json_str = cJSON_PrintUnformatted(root);  // 分配内存

    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};

    if (json_str) {
        strncpy(request_body, json_str, sizeof(request_body) - 1);
        free(json_str);
    }

    cJSON_Delete(root); // 及时释放

    // 发送 POST 请求
    if (https_send_post_request(request_url, request_body, response_body) != 0) {
        qrzl_log("发送短信请求失败!");
        return -1;
    }

    // 解析 JSON 响应
    cJSON *json_response = cJSON_Parse(response_body);
    if (json_response == NULL) {
        const char *error_ptr = cJSON_GetErrorPtr();
        if (error_ptr != NULL) {
            qrzl_log("Error before: %s\n", error_ptr);
        }
        return -1;
    }

    if (!json_response || json_response->type != cJSON_Object) {
        qrzl_log("响应格式错误或为空.");
        cJSON_Delete(json_response);
        return -1;
    }
    
    cJSON *message = cJSON_GetObjectItem(json_response, "message");
    if (message || message->type == cJSON_String) {
        if (strlen(message->valuestring) > 0){
            qrzl_log("%s", message->valuestring);
            cJSON_Delete(json_response);
            return -1;
        }
    }

    return 0;
}

/**
 * 获取已认证信息
 */
char *cs_one_link_get_authed() {

    init_customers_info();

    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char response_body[QRZL_HTTP_RESPONSE_MAX] = {0};
    char *inteface_str = "/access/auth_terminal";
    
    // 拼接请求地址
    snprintf(request_url, sizeof(request_url), "%s%s", one_link_customers_get_token_url, inteface_str);

    cfg_get_item("ziccid", g_qrzl_device_dynamic_data.iccid, 21);

    char convered_mac[33] = {0};
    convert_mac_format(g_qrzl_device_static_data.mac, convered_mac, sizeof(convered_mac), ':');

    // 构造json
    cJSON *root = cJSON_CreateObject();
    cJSON_AddStringToObject(root, "simIccid", g_qrzl_device_dynamic_data.iccid);
    cJSON_AddStringToObject(root, "mac", convered_mac);

    char *json_str = cJSON_PrintUnformatted(root);  // 分配内存

    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};

    if (json_str) {
        strncpy(request_body, json_str, sizeof(request_body) - 1);
        free(json_str);
    }

    cJSON_Delete(root); // 及时释放

    // 获取时间
    char datetime[20] = {0};
    get_local_datetime(datetime, sizeof(datetime)); 

    // 发送 POST 请求
    if (https_send_post_request(request_url, request_body, response_body) != 0) {
        qrzl_log("获取已认证信息请求失败!");
        return NULL;
    }

    // 解析 JSON 响应
    cJSON *json_response = cJSON_Parse(response_body);
    if (json_response == NULL) {
        const char *error_ptr = cJSON_GetErrorPtr();
        if (error_ptr != NULL) {
            qrzl_log("Error before: %s\n", error_ptr);
        }
        return NULL;
    }

    if (!json_response || json_response->type != cJSON_Object) {
        qrzl_log("响应格式错误或为空.");
        cJSON_Delete(json_response);
        return NULL;
    }


    // 动态字符串构造
    size_t buffer_size = 5048;
    char *authed_mac_list = malloc(buffer_size);
    if (!authed_mac_list) {
        cJSON_Delete(json_response);
        return NULL;
    }
    authed_mac_list[0] = '\0';


    cJSON *terminalList = cJSON_GetObjectItem(json_response, "terminalList");
    if (terminalList != NULL && terminalList->type == cJSON_Array) {
        int i;
        for (i = 0; i < cJSON_GetArraySize(terminalList); ++i) {
            cJSON *item = cJSON_GetArrayItem(terminalList, i);
            if (!item) continue;

            cJSON *item_mac = cJSON_GetObjectItem(item, "terminalMac");
            cJSON *expireTime = cJSON_GetObjectItem(item, "expireTime");
            if (!item_mac || item_mac->type != cJSON_String ||
                !expireTime || expireTime->type != cJSON_String) {
                continue;
            }

            if (strcmp(expireTime->valuestring, datetime) < 0) {
                continue;  // 已过期
            }

            // 转换 MAC 格式
            char formatted_mac[32] = {0};
            convert_mac_format(item_mac->valuestring, formatted_mac, sizeof(formatted_mac), ':');

            // 拼接
            size_t used = strlen(authed_mac_list);
            size_t needed = strlen(formatted_mac) + 2;
            if (used + needed >= buffer_size) break;  // 超出，简单跳出

            strcat(authed_mac_list, formatted_mac);
            strcat(authed_mac_list, ";");
        }
    }

    cJSON_Delete(json_response);

    return authed_mac_list; // 需要调用者 free()
}

/**
 * 终端认证
 */
int cs_one_link_verify(char *iccid, char *mac, char *terminalMac, char *phoneNum, char *verifyCode) {

    init_customers_info();

    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char response_body[QRZL_HTTP_RESPONSE_MAX] = {0};
    char *inteface_str = "/access/verify";
    
    // 拼接请求地址
    snprintf(request_url, sizeof(request_url), "%s%s", one_link_customers_get_token_url, inteface_str);

    char convered_mac[33] = {0};
    char convered_terminalMac[33] = {0};
    convert_mac_format(mac, convered_mac, sizeof(convered_mac), ':');
    convert_mac_format(terminalMac, convered_terminalMac, sizeof(convered_terminalMac), ':');

    // 构造json
    cJSON *root = cJSON_CreateObject();
    cJSON_AddStringToObject(root, "simIccid", iccid);
    cJSON_AddStringToObject(root, "mac", convered_mac);
    cJSON_AddStringToObject(root, "terminalMac", convered_terminalMac);
    cJSON_AddStringToObject(root, "phoneNum", phoneNum);
    cJSON_AddStringToObject(root, "verifyCode", verifyCode);

    char *json_str = cJSON_PrintUnformatted(root);  // 分配内存

    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};

    if (json_str) {
        strncpy(request_body, json_str, sizeof(request_body) - 1);
        free(json_str);
    }

    cJSON_Delete(root); // 及时释放

    // 发送 POST 请求
    if (https_send_post_request(request_url, request_body, response_body) != 0) {
        qrzl_log("终端认证请求失败!");
        return -1;
    }

    // 解析 JSON 响应
    cJSON *json_response = cJSON_Parse(response_body);
    if (json_response == NULL) {
        const char *error_ptr = cJSON_GetErrorPtr();
        if (error_ptr != NULL) {
            qrzl_log("Error before: %s\n", error_ptr);
        }
        return -1;
    }

    if (!json_response || json_response->type != cJSON_Object) {
        qrzl_log("响应格式错误或为空.");
        cJSON_Delete(json_response);
        return -1;
    }
    
    cJSON *success = cJSON_GetObjectItem(json_response, "success");
    if (!success && (success->type == cJSON_True || success->type == cJSON_False)) {
        if (success->type == cJSON_False) {
           qrzl_log("响应success字段无效或请求失败.");
            cJSON_Delete(json_response);
            return -1;
        }
    }

    return 0;
}


/**
 * 设备上下线上报
 */
int cs_one_link_on_offline(int push_type, char *iccid, char *mac, char *terminalMac, char *terminalIp) {

    init_customers_info();

    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char response_body[QRZL_HTTP_RESPONSE_MAX] = {0};
    char *inteface_str = push_type == 1 ? "/access/online" : "/access/offline";
    
    // 拼接请求地址
    snprintf(request_url, sizeof(request_url), "%s%s", one_link_customers_get_token_url, inteface_str);

    char convered_mac[33] = {0};
    char convered_terminalMac[33] = {0};
    convert_mac_format(mac, convered_mac, sizeof(convered_mac), ':');
    convert_mac_format(terminalMac, convered_terminalMac, sizeof(convered_terminalMac), ':');

    // 获取时间
    char datetime[20] = {0};
    get_local_datetime(datetime, sizeof(datetime)); 

    // 构造json
    cJSON *root = cJSON_CreateObject();
    cJSON_AddStringToObject(root, "imei", g_qrzl_device_static_data.imei);
    cJSON_AddStringToObject(root, "simIccid", iccid);
    cJSON_AddStringToObject(root, "mac", convered_mac);
    cJSON_AddStringToObject(root, "terminalMac", convered_terminalMac);
    cJSON_AddStringToObject(root, "terminalIp", terminalIp);
    cJSON_AddStringToObject(root, "eventTime", datetime);

    char *json_str = cJSON_PrintUnformatted(root);  // 分配内存

    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};

    if (json_str) {
        strncpy(request_body, json_str, sizeof(request_body) - 1);
        free(json_str);
    }

    cJSON_Delete(root); // 及时释放

    // 发送 POST 请求
    if (https_send_post_request(request_url, request_body, response_body) != 0) {
        qrzl_log("设备上报请求失败!");
        return -1;
    }

    // 解析 JSON 响应
    cJSON *json_response = cJSON_Parse(response_body);
    if (json_response == NULL) {
        const char *error_ptr = cJSON_GetErrorPtr();
        if (error_ptr != NULL) {
            qrzl_log("Error before: %s\n", error_ptr);
        }
        return -1;
    }

    if (!json_response || json_response->type != cJSON_Object) {
        qrzl_log("响应格式错误或为空.");
        cJSON_Delete(json_response);
        return -1;
    }
    
    cJSON *success = cJSON_GetObjectItem(json_response, "success");
    if (!success && (success->type == cJSON_True || success->type == cJSON_False)) {
        if (success->type == cJSON_False) {
           qrzl_log("响应success字段无效或请求失败.");
            cJSON_Delete(json_response);
            return -1;
        }
    }

    qrzl_log("CS 设备 %s 上报成功", push_type == 0 ? "下线":"上线");

    return 0;
}
