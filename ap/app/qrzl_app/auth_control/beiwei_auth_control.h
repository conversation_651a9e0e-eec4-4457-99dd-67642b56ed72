#ifndef __QRZL_BEIWEI_AUTH_CONTROL_H_
#define __QRZL_BEIWEI_AUTH_CONTROL_H_

/**
 * 北纬 上报设备上下线
 * @param push_type 上报类型 0 下线， 1 下线
 * @param terminalMac 终端MAC
 * @return 0 成功  -1 失败
 */
int bw_report_device_line_state(int push_type, char *terminalMac);

/**
 * 北纬 获取设备上网认证终端数据
 */
char *bw_get_authed_list_str();

/**
 * 北纬 发送短信
 * @param terminalMac 终端mac
 * @param phoneNum 手机号码
 * @return -1 请求失败， 0 请求成功
 */
int bw_send_sms(char *terminalMac, char *phoneNum);

/**
 * 北纬 终端设备认证
 * @param terminalMac 终端mac
 * @param verifyCode 验证码
 * @param phoneNum 手机号码
 * @return -1 请求失败， 0 请求成功
 */
int bw_terminal_auth(char *terminalMac, char *verifyCode, char *phoneNum);

/**
 * bw构建第三方认证页面
 * @param url_str 接收完整路径的变量
 * @param url_str_len 接收完整路径的长度
 * @param terminalMac 终端MAC
 * @param isp 运营商 1：移动 2：联通 3：电信
 */
void bw_build_customer_url(char *url_str, int url_str_len, char *terminalMac, int isp);

#endif