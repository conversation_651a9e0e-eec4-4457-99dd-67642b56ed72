#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <signal.h>
#include <errno.h>
#include <pthread.h>
#include <curl/curl.h>
#include <ctype.h>
#include <sys/time.h>

#include "../common_utils/cjson.h"
#include "../qrzl_utils.h"
#include "softap_api.h"
#include "nv_api.h"

extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

#define QRZL_HTTP_RESPONSE_MAX 5120
#define QRZL_HTTP_REQUEST_BODY_MAX 2048

#define KEY_ONE_LINK_GET_TOKEN_URL       "ONE_LINK_customers_get_token_url"

#define CURL_REQUEST_OK 0
#define CURL_REQUEST_ERROR -1

static char one_link_customers_get_token_url[256] = {0};

static size_t http_write_callback(void *contents, size_t size, size_t nmemb, void *userp) {
    size_t totalSize = size * nmemb;
    char *response = (char *)userp;

    size_t currentLen = strlen(response);
    size_t remaining = QRZL_HTTP_RESPONSE_MAX - currentLen - 1; // 预留 '\0'

    if (remaining <= 0) {
        qrzl_log("http返回值已满，不能再写入");
        return 0;
    }

    size_t copyLen = totalSize < remaining ? totalSize : remaining;
    strncat(response, (char *)contents, copyLen);

    return totalSize;
}

static int http_send_post_request(const char *url, const char *body, char *response)
{
    CURL *curl;
    CURLcode res;
    qrzl_log("http request url: %s", url);
    qrzl_log("http request body: %s", body);

    curl = curl_easy_init();
    if (curl) {
        curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
        curl_easy_setopt(curl, CURLOPT_URL, url);
        curl_easy_setopt(curl, CURLOPT_POST, 1L);
        // curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        // curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

        // 设置POST请求的内容
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, body);

        // 设置HTTP头（告诉服务器这是JSON数据）
        struct curl_slist *headers = NULL;
        headers = curl_slist_append(headers, "Content-Type: application/json");
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L); 
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
        res = curl_easy_perform(curl);
        if (res != CURLE_OK) {
            qrzl_err("curl_easy_perform() failed: %s", curl_easy_strerror(res));
        } else {
            qrzl_log("http Response: %s\n", response);
        }
        // 清理
        curl_slist_free_all(headers);
        curl_easy_cleanup(curl);
        if (res != CURLE_OK)
        {
            return -1;
        }
        return 0;
    }
    return -1;
}

/**
 * get请求
 */
static int http_send_get_request(const char *url, char *response) {
    CURL *curl;
    CURLcode res;
    qrzl_log("http request url: %s", url);
    curl = curl_easy_init();
    if (curl) {
        // curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
        curl_easy_setopt(curl, CURLOPT_URL, url);
        // curl_easy_setopt(curl, CURLOPT_POST, 1L);
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L); 
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
        res = curl_easy_perform(curl);
        if (res != CURLE_OK) {
            qrzl_err("curl_easy_perform() failed: %s", curl_easy_strerror(res));
        } else {
            qrzl_log("http Response: %s\n", response);
        }
        curl_easy_cleanup(curl);
        if (res != CURLE_OK) {
            return -1;
        }
        return 0;
    }
    return -1;
}

/**
 * 初始化配置信息
 */
static int init_customers_info()
{
    int res_url = cfg_get_item(KEY_ONE_LINK_GET_TOKEN_URL, one_link_customers_get_token_url, sizeof(one_link_customers_get_token_url));
    if (res_url != 0) {
        qrzl_log("Configuration item %s get error!", KEY_ONE_LINK_GET_TOKEN_URL);
    }
}

// 获取当前本地时间字符串，格式："YYYY-MM-DD HH:MM:SS"
static void get_local_datetime(char *buffer, size_t size) {
    if (!buffer || size < 20) return;  // 安全检查：最小需 20 字节

    time_t t = time(NULL);
    struct tm *tm_info = localtime(&t);
    strftime(buffer, size, "%Y-%m-%d %H:%M:%S", tm_info);
}

// =============================== 移动认证相关接口 =============================================

/**
 * MY 上报设备上下线
 * @param push_type 上报类型 
 * @param terminalMac 终端MAC
 * @return 0 成功  -1 失败
 */
int my_report_device_line_state(int push_type, char *terminalMac)
{   
    init_customers_info();
    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char response_body[QRZL_HTTP_RESPONSE_MAX] = {0};

    // 接口路径
    char *interface_str = push_type == 1 ? "/sim-cloud/ispcommon/api/terminalOnline" : "/sim-cloud/ispcommon/api/terminalOffline";

    char iccid_str[22] = {0};
    char mac_str[33] = {0};
    char terminalMac_str[33] = {0};
    char imei_str[64] = {0};

    cfg_get_item("ziccid", iccid_str, sizeof(iccid_str));
    convert_mac_format(g_qrzl_device_static_data.mac, mac_str, sizeof(mac_str), '\0');
    convert_mac_format(terminalMac, terminalMac_str, sizeof(terminalMac_str), '\0');
    snprintf(imei_str, sizeof(imei_str), "%s", g_qrzl_device_static_data.imei);

    // 完整请求路径拼接
    snprintf(request_url, sizeof(request_url), "%s%s?iccid=%s&mac=%s&terminalMac=%s&imei=%s", 
                    one_link_customers_get_token_url, interface_str, iccid_str, mac_str, terminalMac_str, imei_str);

    // 发起get请求
    int request_res = http_send_get_request(request_url, response_body);

    if (request_res != 0) {
        return CURL_REQUEST_ERROR;
    }

    // 解析 JSON 响应
    cJSON *json_response = cJSON_Parse(response_body);
    if (json_response == NULL) {
        const char *error_ptr = cJSON_GetErrorPtr();
        if (error_ptr != NULL) {
            qrzl_log("Error before: %s\n", error_ptr);
        }
        return CURL_REQUEST_ERROR;
    }

    if (!json_response || json_response->type != cJSON_Object) {
        qrzl_log("响应格式错误或为空.");
        cJSON_Delete(json_response);
        return CURL_REQUEST_ERROR;
    }
    
    cJSON *j_code = cJSON_GetObjectItem(json_response, "code");
    if (!j_code || j_code->type != cJSON_String) {
        qrzl_log("code is NULL or not a string.");
        cJSON_Delete(json_response);
        return CURL_REQUEST_ERROR;
    } else {
        if (strcmp("0", j_code->valuestring) == 0) {
            qrzl_log("设备 %s 上报成功", push_type == 1 ? "上线":"下线");
            return CURL_REQUEST_OK;
        }
    }

    return CURL_REQUEST_OK;
}

/**
 * 获取设备上网认证终端数据
 */
char *my_get_authed_list_str()
{
    init_customers_info();
    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char response_body[QRZL_HTTP_RESPONSE_MAX] = {0};

    // 接口路径
    char *interface_str = "/sim-cloud/ispcommon/api/getAuthTokenToDatabase";

    // 完整请求路径拼接
    snprintf(request_url, sizeof(request_url), "%s%s?sn=%s", 
                    one_link_customers_get_token_url, interface_str, g_qrzl_device_static_data.sn);

    // 发起get请求
    int request_res = http_send_get_request(request_url, response_body);

    if (request_res != 0) {
        return NULL;
    }

    // 解析 JSON 响应
    cJSON *json_response = cJSON_Parse(response_body);
    if (json_response == NULL) {
        const char *error_ptr = cJSON_GetErrorPtr();
        if (error_ptr != NULL) {
            qrzl_log("Error before: %s\n", error_ptr);
        }
        return NULL;
    }

    if (!json_response || json_response->type != cJSON_Object) {
        qrzl_log("响应格式错误或为空.");
        cJSON_Delete(json_response);
        return NULL;
    }

    cJSON *j_code = cJSON_GetObjectItem(json_response, "code");
    if (!j_code || j_code->type != cJSON_Number) {
        qrzl_log("code is NULL or not a number.");
        cJSON_Delete(json_response);
        return NULL;
    }

    cJSON *j_data = cJSON_GetObjectItem(json_response, "data");
    if (!j_data || j_data->type != cJSON_Array) {
        qrzl_log("data is NULL or not a number.");
        cJSON_Delete(json_response);
        return NULL;
    }

    // 获取当前的卡号
    char current_iccid[21] = {0};
    cfg_get_item("ziccid", current_iccid, sizeof(current_iccid));

    // 获取data长度
    int data_arr_len = cJSON_GetArraySize(j_data);
    int d;
    cJSON *terminalList = NULL;
    for (d = 0; d < data_arr_len; ++d) {
        cJSON *item = cJSON_GetArrayItem(j_data, d);
        if (!item) continue;
        cJSON *data_iccid = cJSON_GetObjectItem(item, "iccid");
        if (!data_iccid || data_iccid->type != cJSON_String) continue;
        
        cJSON *data_need_verify = cJSON_GetObjectItem(item, "need_verify");
        if (data_need_verify != NULL && (data_need_verify->type == cJSON_True || data_need_verify->type == cJSON_False)) {
            qrzl_log("need_verify: %s", data_need_verify->type==cJSON_True ? "true":"false");
            if(data_need_verify->type == cJSON_True){
                // cfg_set("qrzl_cloud_authentic_switch", "1");
                set_authentic_switch("1");
            }else if(data_need_verify->type == cJSON_False){
                // cfg_set("qrzl_cloud_authentic_switch", "0");
                set_authentic_switch("0");
            }
        }

        if (strcmp(data_iccid->valuestring, current_iccid) == 0) {
            terminalList = cJSON_GetObjectItem(item, "mac_list");
        }
    }

    // 动态字符串构造
    size_t buffer_size = 5048;
    char *authed_mac_list = malloc(buffer_size);
    if (!authed_mac_list) {
        cJSON_Delete(json_response);
        return NULL;
    }
    authed_mac_list[0] = '\0';

    // 获取时间
    char datetime[20] = {0};
    get_local_datetime(datetime, sizeof(datetime)); 

    if (terminalList != NULL && terminalList->type == cJSON_Array) {
        int i;
        for (i = 0; i < cJSON_GetArraySize(terminalList); ++i) {
            cJSON *item = cJSON_GetArrayItem(terminalList, i);
            if (!item) continue;

            cJSON *item_mac = cJSON_GetObjectItem(item, "terminal_mac");
            cJSON *expireTime = cJSON_GetObjectItem(item, "expired_time");
            if (!item_mac || item_mac->type != cJSON_String ||
                !expireTime || expireTime->type != cJSON_String) {
                continue;
            }

            if (strcmp(expireTime->valuestring, datetime) < 0) {
                continue;  // 已过期
            }

            // 转换 MAC 格式
            char formatted_mac[32] = {0};
            convert_mac_format(item_mac->valuestring, formatted_mac, sizeof(formatted_mac), ':');

            // 拼接
            size_t used = strlen(authed_mac_list);
            size_t needed = strlen(formatted_mac) + 2;
            if (used + needed >= buffer_size) break;  // 超出，简单跳出

            strcat(authed_mac_list, formatted_mac);
            strcat(authed_mac_list, ";");
        }
    }

    cJSON_Delete(json_response);

    return authed_mac_list; // 需要调用者 free()
}
