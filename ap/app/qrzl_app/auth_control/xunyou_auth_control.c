
#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <signal.h>
#include <errno.h>
#include <pthread.h>
#include <curl/curl.h>
#include <wolfssl/wolfcrypt/sha.h>
#include <ctype.h>
#include <sys/time.h>

#include "../common_utils/cjson.h"
#include "../qrzl_utils.h"
#include "../cloud_control/one_link_http_control.h"
#include "softap_api.h"
#include "nv_api.h"


extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

#define QRZL_HTTP_RESPONSE_MAX 5120
#define QRZL_HTTP_REQUEST_BODY_MAX 2048
#define MIN_INTERVAL_MS 2000  // 最小调用间隔：2秒 ，防抖时间（认证线程会频繁探测网络导致一秒内调用多次请求）

static char sign[254] = {0};
static char appKey[64] = {0};
static char timestamp[64] = {0};

static char cmp_auth_http_url[256] = {0};
static char cmp_auth_http_port[10] = {0};
static char cmp_customers_get_token_url[256] = {0};
static char cmp_customer_info[50] = {0};

// 上一次的 MAC 列表（分号分隔）
static char last_station_mac[2048] = {0};  

// 记录上次调用的时间
static unsigned long last_request_time_ms = 0;

#define KEY_CMP_GET_TOKEN_URL       "cmp_customers_get_token_url"
#define KEY_CMP_URL                 "cmp_auth_http_url"
#define KEY_CMP_PORT                "cmp_auth_http_port"
#define KEY_CMP_CUSTOMER_INFO       "cmp_customer_info"

/**
 * 初始化 xunyou的配置信息
 */
void init_xunyou_config_data()
{
    // qrzl_log("Loading XUNYOU - CMP authentication configuration items...");

    cfg_get_item(KEY_CMP_GET_TOKEN_URL, cmp_customers_get_token_url, sizeof(cmp_customers_get_token_url));
    if (strlen(cmp_customers_get_token_url) == 0) {
        qrzl_log("Configuration item %s is missing !", KEY_CMP_GET_TOKEN_URL);
    }

    cfg_get_item(KEY_CMP_URL, cmp_auth_http_url, sizeof(cmp_auth_http_url));
    if (strlen(cmp_auth_http_url) == 0) {
        qrzl_log("Configuration item %s is missing !", KEY_CMP_URL);
    }

    cfg_get_item(KEY_CMP_PORT, cmp_auth_http_port, sizeof(cmp_auth_http_port));
    if (strlen(cmp_auth_http_port) == 0) {
        qrzl_log("Configuration item %s is missing !", KEY_CMP_PORT);
    }
}

// 获取当前本地时间字符串，格式："YYYY-MM-DD HH:MM:SS"
static void get_local_datetime(char *buffer, size_t size) {
    if (!buffer || size < 20) return;  // 安全检查：最小需 20 字节

    time_t t = time(NULL);
    struct tm *tm_info = localtime(&t);
    strftime(buffer, size, "%Y-%m-%d %H:%M:%S", tm_info);
}

static void simple_time_encode(const char *input, char *output, size_t out_size) {
    size_t i = 0, j = 0;

    while (input[i] != '\0' && j < out_size - 1) {
        if (input[i] == ' ') {
            if (j + 1 >= out_size) break;
            output[j++] = '+';
        } else if (input[i] == ':') {
            if (j + 3 >= out_size) break;
            output[j++] = '%';
            output[j++] = '3';
            output[j++] = 'A';
        } else {
            output[j++] = input[i];
        }
        i++;
    }
    output[j] = '\0';
}

// HTTP回调函数，用于处理HTTP响应
static size_t http_write_callback(void *contents, size_t size, size_t nmemb, void *userp) {
    size_t totalSize = size * nmemb;
    char *response = (char *)userp;

    size_t currentLen = strlen(response);
    size_t remaining = QRZL_HTTP_RESPONSE_MAX - currentLen - 1; // 预留 '\0'

    if (remaining <= 0) {
        qrzl_log("http返回值已满，不能再写入");
        return 0;
    }

    size_t copyLen = totalSize < remaining ? totalSize : remaining;
    strncat(response, (char *)contents, copyLen);

    return totalSize;
}

// 发送GET请求
static int http_send_get_request(const char *url, char *response, int type) {
    CURL *curl;
    CURLcode res;
    qrzl_log("ONE LINK -> http request url: %s", url);

    curl = curl_easy_init();
    if (curl) {
        // curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
        curl_easy_setopt(curl, CURLOPT_URL, url);
        // curl_easy_setopt(curl, CURLOPT_POST, 1L);
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L); 
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
        // 设置HTTP头
        struct curl_slist *headers = NULL;
        headers = curl_slist_append(headers, "Content-Type: application/json"); // （告诉服务器这是JSON数据）
        if (type == 1)
        {
            char h_sign[254] = {0};
            char h_appKey[64] = {0};
            char h_timestamp[254] = {0};
            snprintf(h_sign, sizeof(h_sign), "Sign: %s", sign);
            snprintf(h_appKey, sizeof(h_appKey), "AppKey: %s", appKey);
            snprintf(h_timestamp, sizeof(h_timestamp), "Timestamp: %s", timestamp);
            headers = curl_slist_append(headers, h_sign);
            headers = curl_slist_append(headers, h_appKey);
            headers = curl_slist_append(headers, h_timestamp);
        }
        // 设置公共参数
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        res = curl_easy_perform(curl);
        if (res != CURLE_OK) {
            qrzl_log("curl_easy_perform() failed: %s", curl_easy_strerror(res));
        } else {
            qrzl_log("http Response: %s\n", response);
        }
        curl_easy_cleanup(curl);
        if (headers) {
            curl_slist_free_all(headers);
        }
        if (res != CURLE_OK) {
            return -1;
        }
        return 0;
    }
    return -1;
}

// 通用的POST请求函数, type=1表示 将电信签名加入到请求头中
static int https_send_post_request_common(const char *url, const char* body, char *response, int type)
{
    CURL *curl;
    CURLcode res;

    // 初始化curl
    curl = curl_easy_init();

    if(curl == NULL) {
        qrzl_log("init CURL failed!!");
        return -1;
    }

    qrzl_log("request url: %s", url);
    qrzl_log("request body: %s", body);

    // curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
    // 设置SSL
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);
    // 设置请求地址
    curl_easy_setopt(curl, CURLOPT_URL, url);
    // 使用post方式请求
    curl_easy_setopt(curl, CURLOPT_POST, 1L);
    // 设置响应超时时间
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
    // 设置连接超时时间
    curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L);
    // 回调函数
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
    // 设置POST请求的内容
    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, body);
    // 设置HTTP头
    struct curl_slist *headers = NULL;
    headers = curl_slist_append(headers, "Content-Type: application/json"); // （告诉服务器这是JSON数据）
    if (type == 1)
    {
        char h_sign[254] = {0};
        char h_appKey[64] = {0};
        char h_timestamp[254] = {0};
        snprintf(h_sign, sizeof(h_sign), "Sign: %s", sign);
        snprintf(h_appKey, sizeof(h_appKey), "AppKey: %s", appKey);
        snprintf(h_timestamp, sizeof(h_timestamp), "Timestamp: %s", timestamp);
        headers = curl_slist_append(headers, h_sign);
        headers = curl_slist_append(headers, h_appKey);
        headers = curl_slist_append(headers, h_timestamp);
    }
    // 设置公共参数
    curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
    // 设置 接收请求响应的内容
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
    // 正式发起请求
    res = curl_easy_perform(curl);

    // 处理返回值
    if(res != CURLE_OK) {
        qrzl_log("curl_easy_perform request failed!");
    }

    if (headers) {
        curl_slist_free_all(headers);
    }
    // 清理内存
    curl_easy_cleanup(curl);

    if (res != CURLE_OK)
    {
        return -1;
    }

    qrzl_log("request response: %s\n", response);

    return 0;
}


/**
 * 获取xunyou 上报 Token 信息
 * @param mac 设备mac
 * @param terminalMac terminalMac: 终端mac(手机/电脑....)
 * @param phoneNum 手机号码
 * @param time_str 时间戳 %Y-%m-%d %H:%M:%S
 */
int get_report_token_info_xunyou(const char *mac, const char *terminalMac, const char *phoneNum, const char *time_str)
{
    char http_response_content[QRZL_HTTP_RESPONSE_MAX] = {0};
    char http_get_token_request_url[254] = {0};
    snprintf(http_get_token_request_url, sizeof(http_get_token_request_url), "%s", cmp_customers_get_token_url);

    // 构建 JSON 请求体
    cJSON *root = cJSON_CreateObject();
    if (!root) {
        qrzl_log("cJSON_CreateObject failed.");
        return -1;
    }

    char format_mac[33] = {0};
    convert_mac_format(mac, format_mac, sizeof(format_mac), ':');

    char format_terminalMac[33] = {0};
    convert_mac_format(terminalMac, format_terminalMac, sizeof(format_terminalMac), ':');

    cJSON_AddStringToObject(root, "iccid", g_qrzl_device_dynamic_data.iccid);
    cJSON_AddStringToObject(root, "mac", format_mac);
    cJSON_AddStringToObject(root, "terminalMac", format_terminalMac);
    cJSON_AddStringToObject(root, "busiTime", time_str);

    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char *json_str = cJSON_PrintUnformatted(root);  // 分配内存

    if (json_str) {
        strncpy(request_body, json_str, sizeof(request_body) - 1);
        free(json_str);
    }

    cJSON_Delete(root); // 及时释放

    // 发送 POST 请求
    if (https_send_post_request_common(http_get_token_request_url, request_body, http_response_content, 0) != 0) {
        qrzl_log("获取token请求异常，请重试!");
        return -1;
    }

    // 解析 JSON 响应
    cJSON *json_response = cJSON_Parse(http_response_content);
    if (!json_response || json_response->type != cJSON_Object) {
        qrzl_log("响应格式错误或为空.");
        cJSON_Delete(json_response);
        return -1;
    }

    cJSON *json_code = cJSON_GetObjectItem(json_response, "code");
    if (!json_code || json_code->type != cJSON_Number || json_code->valueint != 200) {
        qrzl_log("响应code字段无效或请求失败.");
        cJSON_Delete(json_response);
        return -1;
    }

    cJSON *json_data = cJSON_GetObjectItem(json_response, "data");
    if (!json_data || json_data->type != cJSON_Object) {
        qrzl_log("响应data字段无效.");
        cJSON_Delete(json_response);
        return -1;
    }

    // cJSON *json_operators = cJSON_GetObjectItem(json_data, "operators");
    // if (!json_operators || json_operators->type != cJSON_String || strcmp(json_operators->valuestring, "CT") != 0) {
    //     qrzl_log("运营商不是电信，跳过处理.");
    //     cJSON_Delete(json_response);
    //     return -1;
    // }

    cJSON *json_sign = cJSON_GetObjectItem(json_data, "sign");
    cJSON *json_appKey = cJSON_GetObjectItem(json_data, "appKey");
    cJSON *json_timestamp = cJSON_GetObjectItem(json_data, "timestamp");

    if (!json_sign || json_sign->type != cJSON_String ||
        !json_appKey || json_appKey->type != cJSON_String ||
        !json_timestamp || json_timestamp->type != cJSON_String) {
        qrzl_log("sign/appKey/timestamp 解析失败.");
        cJSON_Delete(json_response);
        return -1;
    }

    // 拷贝最终结果
    snprintf(sign, sizeof(sign), "%s", json_sign->valuestring);
    snprintf(appKey, sizeof(appKey), "%s", json_appKey->valuestring);
    snprintf(timestamp, sizeof(timestamp), "%s", json_timestamp->valuestring);

    cJSON_Delete(json_response);
    qrzl_log("获取 上报 sign 成功.");
    return 0;
}


/**
 * 获取xunyou 已认证设备 Token 信息
 * @param mac 设备mac
 * @param iccid 当前卡
 * @param time_str 时间戳 %Y-%m-%d %H:%M:%S
 */
int get_authed_token_info_xunyou(const char *time_str)
{
    char http_response_content[QRZL_HTTP_RESPONSE_MAX] = {0};
    char http_get_token_request_url[254] = {0};
    snprintf(http_get_token_request_url, sizeof(http_get_token_request_url), "%s", cmp_customers_get_token_url);

    // 构建 JSON 请求体
    cJSON *root = cJSON_CreateObject();
    if (!root) {
        qrzl_log("cJSON_CreateObject failed.");
        return -1;
    }

    char format_mac[33] = {0};
    convert_mac_format(g_qrzl_device_static_data.mac, format_mac, sizeof(format_mac), ':');

    cfg_get_item("ziccid", g_qrzl_device_dynamic_data.iccid, 21);

    cJSON_AddStringToObject(root, "iccid", g_qrzl_device_dynamic_data.iccid);
    cJSON_AddStringToObject(root, "mac", format_mac);
    cJSON_AddStringToObject(root, "busiTime", time_str);

    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char *json_str = cJSON_PrintUnformatted(root);  // 分配内存

    if (json_str) {
        strncpy(request_body, json_str, sizeof(request_body) - 1);
        free(json_str);
    }

    cJSON_Delete(root); // 及时释放

    // 发送 POST 请求
    if (https_send_post_request_common(http_get_token_request_url, request_body, http_response_content, 0) != 0) {
        qrzl_log("获取token请求异常，请重试!");
        return -1;
    }

    // 解析 JSON 响应
    cJSON *json_response = cJSON_Parse(http_response_content);
    if (!json_response || json_response->type != cJSON_Object) {
        qrzl_log("响应格式错误或为空.");
        cJSON_Delete(json_response);
        return -1;
    }

    cJSON *json_code = cJSON_GetObjectItem(json_response, "code");
    if (!json_code || json_code->type != cJSON_Number || json_code->valueint != 200) {
        qrzl_log("响应code字段无效或请求失败.");
        cJSON_Delete(json_response);
        return -1;
    }

    cJSON *json_data = cJSON_GetObjectItem(json_response, "data");
    if (!json_data || json_data->type != cJSON_Object) {
        qrzl_log("响应data字段无效.");
        cJSON_Delete(json_response);
        return -1;
    }

    cJSON *isOpenAuth = cJSON_GetObjectItem(json_data, "isOpenAuth");
    if (isOpenAuth != NULL && isOpenAuth->type == cJSON_Number) {
        if (isOpenAuth->valueint == 0) {
            // 不开启认证就没有下面的信息
            // 设置认证开关nv值
            // cfg_set("qrzl_cloud_authentic_switch", "0");
            set_authentic_switch("0");
            return 0;
        } else if (isOpenAuth->valueint == 1) {
            // cfg_set("qrzl_cloud_authentic_switch", "1");
            set_authentic_switch("1");
        }
    }

    // cJSON *json_operators = cJSON_GetObjectItem(json_data, "operators");
    // if (!json_operators || json_operators->type != cJSON_String || strcmp(json_operators->valuestring, "CT") != 0) {
    //     qrzl_log("运营商不是电信，跳过处理.");
    //     cJSON_Delete(json_response);
    //     return -1;
    // }

    cJSON *json_sign = cJSON_GetObjectItem(json_data, "sign");
    cJSON *json_appKey = cJSON_GetObjectItem(json_data, "appKey");
    cJSON *json_timestamp = cJSON_GetObjectItem(json_data, "timestamp");

    if (!json_sign || json_sign->type != cJSON_String ||
        !json_appKey || json_appKey->type != cJSON_String ||
        !json_timestamp || json_timestamp->type != cJSON_String) {
        qrzl_log("sign/appKey/timestamp 解析失败.");
        cJSON_Delete(json_response);
        return -1;
    }

    // 拷贝最终结果
    snprintf(sign, sizeof(sign), "%s", json_sign->valuestring);
    snprintf(appKey, sizeof(appKey), "%s", json_appKey->valuestring);
    snprintf(timestamp, sizeof(timestamp), "%s", json_timestamp->valuestring);

    cJSON_Delete(json_response);
    qrzl_log("获取 已认证设备 sign 成功.");
    return 0;
}


/**
 * XUNYOU 设备上/下线上报
 * @param push_type 上报类型
 * @param mac 设备mac
 * @param terminalMac terminalMac: 终端mac(手机/电脑....)
 * @param phoneNum 手机号码
 */
int xunyou_device_line_type_push(const int push_type, const char *mac, const char *terminalMac, const char *phoneNum)
{
    // 获取时间
    char datetime[20] = {0};
    get_local_datetime(datetime, sizeof(datetime)); 
    get_report_token_info_xunyou(mac, terminalMac, "", datetime);   // 获取上报的token

    char http_response_content[QRZL_HTTP_RESPONSE_MAX] = {0};
    char http_get_request_url[2048] = {0};
    const char *msg_suff = (push_type == 1) ? "ON Line" : "OFF Line";

    snprintf(http_get_request_url, sizeof(http_get_request_url),
             "%s:%s/openapi/v1/device/%s",
             cmp_auth_http_url,
             cmp_auth_http_port,
             (push_type == 1) ? "terminalOnline" : "terminalOffline");

    cfg_get_item("ziccid", g_qrzl_device_dynamic_data.iccid, 21);

    // 格式化，与获取token的传参一致
    char format_mac[33] = {0};
    convert_mac_format(mac, format_mac, sizeof(format_mac), ':');
    char terminalMac_fomted[33] = {0};
    convert_mac_format(terminalMac, terminalMac_fomted, sizeof(terminalMac_fomted), ':');

    // url编码
    char tm_encoded_str[50] = {0};
    simple_time_encode(datetime, tm_encoded_str, sizeof(tm_encoded_str));
    char mac_encoded_str[33] = {0};
    char terminalMac_encoded_str[33] = {0};
    simple_time_encode(format_mac, mac_encoded_str, sizeof(mac_encoded_str));
    simple_time_encode(terminalMac_fomted, terminalMac_encoded_str, sizeof(terminalMac_encoded_str));

    snprintf(http_get_request_url, sizeof(http_get_request_url), "%s?iccid=%s&mac=%s&terminalMac=%s&busiTime=%s",
            http_get_request_url, g_qrzl_device_dynamic_data.iccid , mac_encoded_str, terminalMac_encoded_str, tm_encoded_str);

    // 发送 POST 请求
    int res_spr = https_send_post_request_common(http_get_request_url, "", http_response_content, 1);
    if (res_spr != 0) {
        qrzl_log("设备 %s 上报请求失败", msg_suff);
        return -1;
    }

    // 解析响应
    cJSON *json_response = cJSON_Parse(http_response_content);
    if (!json_response || json_response->type != cJSON_Object) {
        qrzl_log("设备 %s 上报响应格式错误", msg_suff);
        cJSON_Delete(json_response);
        return -1;
    }

    cJSON *json_code = cJSON_GetObjectItem(json_response, "code");
    int success = 0;

    if (json_code) {
        if (json_code->type == cJSON_String && strcmp(json_code->valuestring, "0") == 0) {
            success = 1;
        } else if (json_code->type == cJSON_Number && json_code->valueint == 0) {
            success = 1;
        }
    }

    if (!success) {
        qrzl_log("设备 %s 上报失败，返回 code 非 0", msg_suff);
        cJSON_Delete(json_response);
        return -1;
    }

    qrzl_log("设备 %s 上报成功", msg_suff);
    cJSON_Delete(json_response);
    return 0;
}

/**
 * XUNYOU 获取已认证设备， 处理成字符
 * @return 已认证mac字符串
 */
char *xunyou_check_device_authed()
{
    // 获取时间
    char datetime[20] = {0};
    get_local_datetime(datetime, sizeof(datetime)); 
    get_authed_token_info_xunyou(datetime);   // 获取已认证设备的token

    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char response_body[QRZL_HTTP_RESPONSE_MAX] = {0};

    // 通过nv读取卡，不使用动态数据更新的函数，这个函数会频繁更新不必要的数据
    cfg_get_item("ziccid", g_qrzl_device_dynamic_data.iccid, 21);

    snprintf(request_url, sizeof(request_url),
             "%s:%s/openapi/v1/device/getTerminalList",
             cmp_auth_http_url,
             cmp_auth_http_port);

    // time url编码
    char tm_encoded_str[50] = {0};
    simple_time_encode(datetime, tm_encoded_str, sizeof(tm_encoded_str));

    // 与获取token参数对齐
    char format_mac[33] = {0};
    convert_mac_format(g_qrzl_device_static_data.mac, format_mac, sizeof(format_mac), ':');
    // mac url编码
    char mac_encoded_str[50] = {0};
    simple_time_encode(format_mac, mac_encoded_str, sizeof(mac_encoded_str));

    // 构造请求
    snprintf(request_url, sizeof(request_url), "%s?iccid=%s&mac=%s&busiTime=%s",
            request_url, g_qrzl_device_dynamic_data.iccid , mac_encoded_str, tm_encoded_str);

    // 发起请求
    int request_result = https_send_post_request_common(request_url, "", response_body, 1);

    if (request_result != 0) return NULL;

    // 解析 JSON
    cJSON *response_json = cJSON_Parse(response_body);
    if (!response_json || response_json->type != cJSON_Object) {
        cJSON_Delete(response_json);
        return NULL;
    }

    cJSON *result = cJSON_GetObjectItem(response_json, "result");
    cJSON *terminalList = result ? cJSON_GetObjectItem(result, "terminalList") : NULL;
    if (!terminalList || terminalList->type != cJSON_Array) {
        cJSON_Delete(response_json);
        return NULL;
    }

    // 动态字符串构造
    size_t buffer_size = 5048;
    char *authed_mac_list = malloc(buffer_size);
    if (!authed_mac_list) {
        cJSON_Delete(response_json);
        return NULL;
    }
    authed_mac_list[0] = '\0';
    int i;
    for (i = 0; i < cJSON_GetArraySize(terminalList); ++i) {
        cJSON *item = cJSON_GetArrayItem(terminalList, i);
        if (!item) continue;

        cJSON *item_mac = cJSON_GetObjectItem(item, "terminalMac");
        cJSON *expireTime = cJSON_GetObjectItem(item, "expireTime");
        if (!item_mac || item_mac->type != cJSON_String ||
            !expireTime || expireTime->type != cJSON_String) {
            continue;
        }

        if (strcmp(expireTime->valuestring, datetime) < 0) {
            continue;  // 已过期
        }

        // 转换 MAC 格式
        char formatted_mac[32] = {0};
        convert_mac_format(item_mac->valuestring, formatted_mac, sizeof(formatted_mac), ':');

        // 拼接
        size_t used = strlen(authed_mac_list);
        size_t needed = strlen(formatted_mac) + 2;
        if (used + needed >= buffer_size) break;  // 超出，简单跳出

        strcat(authed_mac_list, formatted_mac);
        strcat(authed_mac_list, ";");
    }

    cJSON_Delete(response_json);
    return authed_mac_list;  // 需要调用者 free()
}

/**
 * XUNYOU 判断mac是否已认证
 * @param mac 终端mac
 * @return 0 未认证； 1 已认证
 */
int xunyou_is_mac_authed(const char *mac)
{   
    int ret = 0;

    if (!mac || strlen(mac) == 0) {
        return ret;
    }

    char *authed_list = xunyou_check_device_authed();
    if (!authed_list) {
        qrzl_err("无法获取已认证 MAC 列表");
        return ret;
    }

    // 格式化传入的 mac
    char formatted_mac[32] = {0};
    convert_mac_format(mac, formatted_mac, sizeof(formatted_mac), ':');

    // 构造 ";xx:xx:xx:xx:xx:xx;" 格式，避免部分匹配问题
    char search_key[40] = {0};
    snprintf(search_key, sizeof(search_key), ";%s;", formatted_mac);

    // 为了统一处理，首尾补上 ';'
    size_t list_len = strlen(authed_list);
    char *wrapped_list = malloc(list_len + 3);
    if (!wrapped_list) {
        free(authed_list);
        return ret;
    }
    snprintf(wrapped_list, list_len + 3, ";%s", authed_list);

    qrzl_log("search_key: %s", search_key);
    qrzl_log("wrapped_list: %s", wrapped_list);

    if (strstr(wrapped_list, search_key) != NULL) {
        ret = 1;
    }

    free(authed_list);
    free(wrapped_list);
    return ret;
}



// =================================== 移动 =====================================================

/**
 * xunyou  获取 移动token 信息
 */
int xunyou_get_token_code(char *appid_str, size_t appid_len, char *token_str, size_t token_len)
{
    char http_response_content[QRZL_HTTP_RESPONSE_MAX] = {0};
    char http_get_token_request_url[254] = {0};
    snprintf(http_get_token_request_url, sizeof(http_get_token_request_url), "%s", cmp_customers_get_token_url);

    // 构建 JSON 请求体
    cJSON *root = cJSON_CreateObject();
    if (!root) {
        qrzl_log("cJSON_CreateObject failed.");
        return -1;
    }

    char format_mac[33] = {0};
    convert_mac_format(g_qrzl_device_static_data.mac, format_mac, sizeof(format_mac), ':');

    cfg_get_item("ziccid", g_qrzl_device_dynamic_data.iccid, 21);

    cJSON_AddStringToObject(root, "iccid", g_qrzl_device_dynamic_data.iccid);
    cJSON_AddStringToObject(root, "mac", format_mac);

    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char *json_str = cJSON_PrintUnformatted(root);  // 分配内存

    if (json_str) {
        strncpy(request_body, json_str, sizeof(request_body) - 1);
        free(json_str);
    }

    cJSON_Delete(root); // 及时释放

    // 发送 POST 请求
    if (https_send_post_request_common(http_get_token_request_url, request_body, http_response_content, 0) != 0) {
        qrzl_log("获取token请求异常，请重试!");
        return -1;
    }

    // 解析 JSON 响应
    cJSON *json_response = cJSON_Parse(http_response_content);
    if (!json_response || json_response->type != cJSON_Object) {
        qrzl_log("响应格式错误或为空.");
        cJSON_Delete(json_response);
        return -1;
    }

    cJSON *json_code = cJSON_GetObjectItem(json_response, "code");
    if (!json_code || json_code->type != cJSON_Number || json_code->valueint != 200) {
        qrzl_log("响应code字段无效或请求失败.");
        cJSON_Delete(json_response);
        return -1;
    }

    cJSON *json_data = cJSON_GetObjectItem(json_response, "data");
    if (!json_data || json_data->type != cJSON_Object) {
        qrzl_log("响应data字段无效.");
        cJSON_Delete(json_response);
        return -1;
    }

    cJSON *isOpenAuth = cJSON_GetObjectItem(json_data, "isOpenAuth");
    if (isOpenAuth != NULL && isOpenAuth->type == cJSON_Number) {
        if (isOpenAuth->valueint == 0) {
            // 不开启认证就没有下面的信息
            // 设置认证开关nv值
            // cfg_set("qrzl_cloud_authentic_switch", "0");
            set_authentic_switch("0");
            return 0;
        } else if (isOpenAuth->valueint == 1) {
            // cfg_set("qrzl_cloud_authentic_switch", "1");
            set_authentic_switch("1");
        }
    }

    cJSON *json_token = cJSON_GetObjectItem(json_data, "token");
    cJSON *json_appid = cJSON_GetObjectItem(json_data, "appid");

    if (!json_token || json_token->type != cJSON_String ||
        !json_appid || json_appid->type != cJSON_String ) {
        qrzl_log("token/appid 解析失败.");
        cJSON_Delete(json_response);
        return -1;
    }

    // 拷贝最终结果
    snprintf(appid_str, appid_len, "%s", json_appid->valuestring);
    snprintf(token_str, token_len, "%s", json_token->valuestring);

    cJSON_Delete(json_response);
    qrzl_log("获取 已认证设备 token 成功.");
    return 0;
}


/**
 * XUNYOU 获取已认证设备, 不特殊处理
 * @param rev_response_str 接收curl结果
 */
void xunyou_check_device_authed_origin(char *rev_response_str)
{   
    // 判断当前卡的运营商信息
    char sim_imsi[32] = {0};
    cfg_get_item("sim_imsi", sim_imsi, sizeof(sim_imsi));
    // 0无匹配, 1移动, 2联通, 3电信
    int isp = get_isp_by_imsi(sim_imsi);

    if (isp == 1) {
      one_link_get_auth_list_origin(rev_response_str);
    } else if (isp == 2) {

    } else if (isp == 3) {
        // 获取时间
        char datetime[20] = {0};
        get_local_datetime(datetime, sizeof(datetime)); 
        get_authed_token_info_xunyou(datetime);   // 获取已认证设备的token

        char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};

        // 通过nv读取卡，不使用动态数据更新的函数，这个函数会频繁更新不必要的数据
        cfg_get_item("ziccid", g_qrzl_device_dynamic_data.iccid, 21);

        snprintf(request_url, sizeof(request_url),
                "%s:%s/openapi/v1/device/getTerminalList",
                cmp_auth_http_url,
                cmp_auth_http_port);

        // time url编码
        char tm_encoded_str[50] = {0};
        simple_time_encode(datetime, tm_encoded_str, sizeof(tm_encoded_str));

        // 与获取token参数对齐
        char format_mac[33] = {0};
        convert_mac_format(g_qrzl_device_static_data.mac, format_mac, sizeof(format_mac), ':');
        // mac url编码
        char mac_encoded_str[50] = {0};
        simple_time_encode(format_mac, mac_encoded_str, sizeof(mac_encoded_str));

        // 构造请求
        snprintf(request_url, sizeof(request_url), "%s?iccid=%s&mac=%s&busiTime=%s",
                request_url, g_qrzl_device_dynamic_data.iccid , mac_encoded_str, tm_encoded_str);

        // 发起请求
        int request_result = https_send_post_request_common(request_url, "", rev_response_str, 1);
    } else {

    }

    
}