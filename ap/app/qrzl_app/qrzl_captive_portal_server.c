#ifdef JCV_FEATURE_CAPTIVE_PORTAL_SERVER
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <pthread.h>
#include <string.h>

#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/msg.h>

#include "qrzl_captive_portal_server.h"
#include "cloud_control/one_link_http_control.h"
#include "cloud_control/cmp_auth_control.h"
#include "softap_api.h"
#include "qrzl_utils.h"
#include "nv_api.h"


/* 新增头文件 */
//#define SO_REUSEPORT 15  // 在包含系统头文件前定义
#include <sys/socket.h>
#include <netinet/in.h>
#ifdef CONFIG_IPV6_PORTAL
#include <arpa/inet.h>      // inet_ntop, inet_pton
// Note: IPv6 structures are already defined in netinet/in.h via arpa/inet.h
// Don't include linux/in6.h to avoid redefinition conflicts
#endif
#define LOCAL_GATEWAY_PORT 9000
#define PROC_AUTH_PATH "/proc/cjportal/auth"
#define BUFFER_SIZE 1024

#ifdef CONFIG_DNS_REDIRECT
/* IPv6 UDP DNS 处理相关定义 */
#define IPV6_UDP_BUFFER_SIZE 512
#define DNS_PORT 53
#define DNS_SERVER_PORT 9053
#endif

/********************* 异步HTTP服务器实现 *********************/
// 在代码头部添加
#define _GNU_SOURCE         // 启用accept4等特性
#include <sys/epoll.h>      // epoll核心功能
#include <fcntl.h>          // 非阻塞IO设置

#define MAX_EVENTS 1024

/************** 客户定制认证信息 **************/
#ifdef QRZL_AUTH_WUXING
#include "auth_control/wuxing_auth_control.h"
#define CLIENT_WUXING "WUXING"
#endif
#ifdef QRZL_AUTH_JIUYAO
#include "auth_control/jiuyao_auth_control.h"
#define CLIENT_JIUYAO "JIUYAO"
#endif

/************** 认证结果页路径 **************/
#define AUTH_SUCCESS "/auth_success.html"
#define AUTH_FAILED "/auth_failed.html"

/************** IPv6 UDP DNS 处理函数声明 **************/
#ifdef CONFIG_DNS_REDIRECT
void* ipv6_udp_dns_server_thread(void* arg);
void handle_ipv6_dns_request(int udp_fd, struct sockaddr_in6* client_addr, char* buffer, int len);
int check_mac_authentication_from_ipv6(struct in6_addr* client_ipv6);
void send_captive_portal_dns_response(int udp_fd, struct sockaddr_in6* client_addr, char* original_request, int request_len, int dns_type);
int get_dns_query_type(char* buffer, int len);
#endif



struct client_info {
    int fd;
    char buffer[BUFFER_SIZE];
    size_t bytes_read;
};


#ifdef JCV_FEATURE_INTER_AD_PAGE_AUTH
// 广告页面
// 修改广告页面的定义（删除固定的 Content-Length）
const char *g_ad_page_header = 
    "HTTP/1.1 200 OK\r\n"
    "Content-Type: text/html; charset=utf-8\r\n"
    "Connection: close\r\n"
    "Content-Length: %d\r\n"
    "\r\n";

const char *g_ad_page_body = 
    "<html>"
    "<head><title>免费WiFi认证</title></head>"
    "<body>"
    "<h1>欢迎使用免费WiFi</h1>"
    "<div style='border: 1px solid #ccc; padding: 20px; margin: 20px;'>"
    "<h2>请阅读以下广告以继续：</h2>"
    "<img src='' alt='广告' style='max-width: 600px;'>"
    "<form action='/auth' method='POST'>"
    "<input type='submit' value='同意并继续上网' style='margin-top: 20px; padding: 10px 20px;'>"
    "</form>"
    "</div>"
    "</body>"
    "</html>";

// 认证成功后的重定向页面
const char *g_redirect_page = 
    "HTTP/1.1 302 Found\r\n"
    "Location: http://neverssl.com\r\n"
    "Content-Length: 0\r\n"
    "Connection: close\r\n"
    "\r\n";
#endif


// 获取客户端IP (支持IPv4和IPv6)
static char *get_client_ip(int client_socket) {
#ifdef CONFIG_IPV6_PORTAL
    struct sockaddr_storage client_addr;
    socklen_t addr_len = sizeof(client_addr);
    static char ip_str[INET6_ADDRSTRLEN];

    if (getpeername(client_socket, (struct sockaddr *)&client_addr, &addr_len) != 0) {
        return NULL;
    }

    if (client_addr.ss_family == AF_INET) {
        struct sockaddr_in *addr_in = (struct sockaddr_in *)&client_addr;
        inet_ntop(AF_INET, &addr_in->sin_addr, ip_str, INET_ADDRSTRLEN);
    } else if (client_addr.ss_family == AF_INET6) {
        struct sockaddr_in6 *addr_in6 = (struct sockaddr_in6 *)&client_addr;
        inet_ntop(AF_INET6, &addr_in6->sin6_addr, ip_str, INET6_ADDRSTRLEN);
    } else {
        return NULL;
    }

    return ip_str;
#else
    // IPv4 only version
    struct sockaddr_in client_addr;
    socklen_t addr_len = sizeof(client_addr);
    getpeername(client_socket, (struct sockaddr *)&client_addr, &addr_len);
    return inet_ntoa(client_addr.sin_addr);
#endif
}

#ifdef CONFIG_IPV6_PORTAL
// 检查是否为IPv6地址
static int is_ipv6_address(const char *ip_str) {
    return strchr(ip_str, ':') != NULL;
}
#endif

// 改进的MAC地址获取函数
static char *get_mac_from_ip(const char *ip) {
    FILE *fp = fopen("/proc/net/arp", "r");
    if (!fp) {
        qrzl_err("Failed to open /proc/net/arp");
        return NULL;
    }

    char line[256];
    fgets(line, sizeof(line), fp); // 跳过标题行

    while (fgets(line, sizeof(line), fp)) {
        char arp_ip[16], mac[18], device[16];
        unsigned int hw_type, flags; // 改用 unsigned int 处理十六进制值

        /* 解析六个字段：
         * 1. IP地址         (字符串)
         * 2. 硬件类型       (十六进制)
         * 3. 标志位         (十六进制)
         * 4. MAC地址        (字符串)
         * 5. 掩码           (跳过)
         * 6. 网络接口       (字符串)
         */
        int parsed = sscanf(line, "%15s %x %x %17s %*s %15s",
                          arp_ip, &hw_type, &flags, mac, device);

        if (parsed != 5) { // 需要成功解析5个字段（不包括跳过的掩码）
            continue;
        }

        /* 验证：
         * 1. IP地址匹配
         * 2. 硬件类型为以太网（0x1）
         * 3. 标志位包含有效MAC（0x2）
         */
        if (strcmp(arp_ip, ip) == 0 &&
            hw_type == 1 &&            // 0x1 表示以太网
            (flags & 0x2)) {          // 0x2 表示有效MAC
            fclose(fp);
            return strdup(mac);
        }
    }

    fclose(fp);
    return NULL;
}

#ifdef CONFIG_IPV6_PORTAL
// IPv6版本的MAC地址获取函数
static char *get_mac_from_ipv6(const char *ipv6) {
    FILE *fp = fopen("/proc/net/arp", "r");
    if (!fp) {
        // Try IPv6 neighbor table
        fp = fopen("/proc/net/ndisc_cache", "r");
        if (!fp) {
            qrzl_err("Failed to open /proc/net/arp and /proc/net/ndisc_cache");
            return NULL;
        }

        char line[256];
        fgets(line, sizeof(line), fp); // 跳过标题行

        while (fgets(line, sizeof(line), fp)) {
            char ipv6_addr[64], mac[18], device[16];
            unsigned int flags;

            // IPv6 neighbor cache format: ipv6_addr dev lladdr nud_state
            int parsed = sscanf(line, "%63s %15s %17s %x",
                              ipv6_addr, device, mac, &flags);

            if (parsed >= 3) {
                if (strcmp(ipv6_addr, ipv6) == 0) {
                    fclose(fp);
                    return strdup(mac);
                }
            }
        }

        fclose(fp);
        return NULL;
    }

    // Check IPv4 ARP table first (for dual-stack clients)
    char line[256];
    fgets(line, sizeof(line), fp); // 跳过标题行

    while (fgets(line, sizeof(line), fp)) {
        char arp_ip[16], mac[18], device[16];
        unsigned int hw_type, flags;

        int parsed = sscanf(line, "%15s %x %x %17s %*s %15s",
                          arp_ip, &hw_type, &flags, mac, device);

        if (parsed != 5) {
            continue;
        }

        // For IPv6, we might need to check if this MAC corresponds to the IPv6 address
        // This is a simplified approach - in practice you might want to use
        // neighbor discovery or maintain a mapping table
        if (hw_type == 1 && (flags & 0x2)) {
            // Additional logic could be added here to correlate IPv6 with MAC
            // For now, we'll use the neighbor cache approach above
        }
    }

    fclose(fp);
    return NULL;
}
#endif

// 写入/proc文件系统授权
static int write_mac_to_proc(const char *mac) {
    int fd = open(PROC_AUTH_PATH, O_WRONLY | O_TRUNC);
    if (fd < 0) {
        qrzl_err("Failed to open /proc/cjportal/auth");
        return -1;
    }

    if (write(fd, mac, strlen(mac)) < 0) {
        qrzl_err("Failed to write MAC to /proc/cjportal/auth");
        close(fd);
        return -1;
    }

    close(fd);
    return 0;
}

int nv_add_mac(char *mac_list, const char *new_mac, size_t buf_size) {
    char temp[64];
    size_t len;

    if (!mac_list || !new_mac) return -1;

    // 构造 "mac;" 格式，避免匹配到类似前缀
    snprintf(temp, sizeof(temp), "%s;", new_mac);

    // 如果已存在则直接返回
    if (strstr(mac_list, temp) != NULL) {
        return 0; // 已存在
    }

    len = strlen(mac_list);
    if (len + strlen(temp) >= buf_size) {
        return -1; // 缓冲区不足
    }

    // 追加到末尾
    strcat(mac_list, temp);
    return 1; // 添加成功
}

int is_probe_request(const char *buffer) {
    if (!buffer) return 0;

    // Windows NCSI
    if (strstr(buffer, "GET /connecttest.txt") || strstr(buffer, "Host: www.msftconnecttest.com")) return 1;
    if (strstr(buffer, "GET /ncsi.txt") || strstr(buffer, "Host: www.msftncsi.com")) return 1;

    // Android
    if (strstr(buffer, "GET /generate_204") || strstr(buffer, "Host: connectivitycheck.gstatic.com")) return 1;
    if (strstr(buffer, "GET /generate_204") || strstr(buffer, "Host: connectivitycheck.android.com")) return 1;
    if (strstr(buffer, "GET /generate_204") || strstr(buffer, "Host: clients3.google.com")) return 1; // ChromeOS 也用这个

    // Apple
    if (strstr(buffer, "GET /hotspot-detect.html") || strstr(buffer, "Host: captive.apple.com")) return 1;

    // Linux NetworkManager
    if (strstr(buffer, "Host: network-test.debian.org")) return 1;
    if (strstr(buffer, "Host: conncheck.opensuse.org")) return 1;
    if (strstr(buffer, "/")) return 1;

    return 0;
}

extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

/********************************************************************
 * 
 * 电信认证函数
 * 
 *******************************************************************/
static void  handle_cmp_auth_request(int client_socket, const char *buffer, int bytes_read)
{
    // 使用编译时宏替代运行时配置
#ifdef QRZL_CMP_CUSTOMER_WUXING
    qrzl_log("CMP customer type: WUXING (compile-time)");
#else
    qrzl_log("CMP customer type: DEFAULT (compile-time)");
#endif

    int sent_bytes;
    qrzl_log("Received %zd bytes: \n%.*s", bytes_read, (int)bytes_read, buffer);

    // 获取客户端ip
    char *client_ip = get_client_ip(client_socket);
    if (!client_ip) {
        const char *error_response = "HTTP/1.1 500 Internal Server Error\r\n\r\n";
        sent_bytes = send(client_socket, error_response, strlen(error_response), MSG_NOSIGNAL);
        if (sent_bytes < 0 && errno != EPIPE) {
            qrzl_err("Failed to send Internal Server: %s", strerror(errno));
        }
        close(client_socket);
        return;
    }
    
    // 获取客户端mac (支持IPv4和IPv6)
    char *mac = NULL;
#ifdef CONFIG_IPV6_PORTAL
    if (is_ipv6_address(client_ip)) {
        mac = get_mac_from_ipv6(client_ip);
        qrzl_log("IPv6 client: %s, MAC: %s", client_ip, mac ? mac : "not found");
    } else {
        mac = get_mac_from_ip(client_ip);
        qrzl_log("IPv4 client: %s, MAC: %s", client_ip, mac ? mac : "not found");
    }
#else
    mac = get_mac_from_ip(client_ip);
    qrzl_log("IPv4 client: %s, MAC: %s", client_ip, mac ? mac : "not found");
#endif

    if (!mac) {
        const char *error_response = "HTTP/1.1 400 Bad Request\r\n\r\nFailed to get MAC address";
        sent_bytes = send(client_socket, error_response, strlen(error_response), MSG_NOSIGNAL);
        if (sent_bytes < 0 && errno != EPIPE) {
            qrzl_err("Failed to send get MAC address: %s", strerror(errno));
        }
        close(client_socket);
        return;
    }

    if (is_probe_request(buffer)) {

        // 便携式联网设备(手机)mac
        char terminalMac[32] = {0};
        convert_mac_format(mac, terminalMac, sizeof(terminalMac), '\0');
        
        // 获取本机mac
        char device_mac[32] = {0};
        convert_mac_format(g_qrzl_device_static_data.mac, device_mac, sizeof(device_mac), '\0');

        // terminalMac：本机mac， mac: 用户设备mac， servAddr ：认证完成跳转页面
        //获取网关
        char lan_ipaddr[30] = {0};
        cfg_get_item("lan_ipaddr", lan_ipaddr, sizeof(lan_ipaddr));

        // 对servAddr进行编码
        char servAddr[128] = {0};
        char servAddr_encoded_str[256] = {0};
        snprintf(servAddr, sizeof(servAddr), "http://%s:80/cmp_auth", lan_ipaddr);
        if (url_encode(servAddr, servAddr_encoded_str) < 0) {
            qrzl_log("电信回调地址encode 失败!!");
        } else {
            qrzl_log("电信回调地址完成: %s\n", servAddr_encoded_str);
        }

#ifdef QRZL_CMP_CUSTOMER_WUXING
        {
            // 获取认证跳转地址
            CurlResult curl_res = wuxing_get_sign_info(g_qrzl_device_static_data.mac, mac, servAddr);

            if (strcmp(curl_res.code, "200") != 0) {
                // 接口未正常返回，返回异常页面
                char *html_page = wuxing_exception_page(curl_res.msg, curl_res.code);
                if (html_page) {
                    // 发送
                    sent_bytes = send(client_socket, html_page, strlen(html_page), MSG_NOSIGNAL);
                    if (sent_bytes < 0) {
                        qrzl_err("Failed to send html_page: %s", strerror(errno));
                    } else {
                        qrzl_log("Successfully sent auth page, sent_bytes: %d", sent_bytes);
                    }
                    free(html_page);
                    free(mac);
                    free(client_ip);
                    close(client_socket);
                    return;
                }
            }
        }
#endif

        if(cmp_terminal_is_authed(device_mac, terminalMac, servAddr)) {
            qrzl_log("当前 MAC: %s, 已认证！", mac);
            write_mac_to_proc(mac);
            if(cmp_check_mac_in_last_station(mac) !=1){
                // 上报终端信息
                device_line_type_push(1, device_mac, terminalMac, client_ip);
                cmp_change_last_station_mac(mac);
            }
            free(mac);
            free(client_ip);
            close(client_socket);
            return;
        }

        qrzl_log("当前 MAC: %s, 未认证, 开始进行认证..", mac);
        
        // 构建电信认证地址
        char cmp_auth_url[5048] = {0};

        // // 拼接完整的URL
        // snprintf(cmp_auth_url, sizeof(cmp_auth_url), "https://cmp-authportal.ctwing.cn:20153/#/?terminalMac=%s&mac=%s&servAddr=%s", terminalMac, device_mac, servAddr_encoded_str);

        // if (strncmp(cmp_customer_info, "WUXING", sizeof(cmp_customer_info)) == 0) {
        //     // 尝试获取客户自定义的认证页面
        //     int try_count = 3;
        //     char customers_page_url[125] = {0};

        //     while (try_count > 0 && cfg_get_item("cmp_customers_auth_page_url", customers_page_url, sizeof(customers_page_url)) == 0)
        //     {   
        //         try_count --;
        //         if (strcmp("", customers_page_url) != 0) {
        //             snprintf(cmp_auth_url, sizeof(cmp_auth_url), "%s", customers_page_url);
        //             break;
        //         }
        //         sleep(1);
        //     }
        // }
        
        char custom_response[6096];

        // 获取第三方认证页面地址(可能没有)
        if (cmp_get_customers_page_url(cmp_auth_url, sizeof(cmp_auth_url), device_mac, terminalMac, client_ip, servAddr_encoded_str) != 0) {
            qrzl_log("customers_page_url构建失败!");
        }

        // 构造 HTTP 302 响应
        // snprintf(custom_response, sizeof(custom_response),
        //     "HTTP/1.1 302 Found\r\n"
        //     "Location: %s\r\n"
        //     "Content-Length: 0\r\n"
        //     "Connection: close\r\n\r\n",
        //     cmp_auth_url);

        // 构造 200 响应，兼容性更强
        snprintf(custom_response, sizeof(custom_response),
            "HTTP/1.1 200 OK\r\n"
            "Content-Type: text/html\r\n"
            "Content-Length: %zu\r\n"
            "Connection: close\r\n\r\n"
            "<html><head><meta http-equiv='refresh' content='0;url=%s'></head></html>",
            strlen(cmp_auth_url) + 70,  // 简单估算长度
            cmp_auth_url);

        qrzl_log("准备跳转到认证地址: %s", custom_response);

        // 发送字节给socket
        sent_bytes = send(client_socket, custom_response, strlen(custom_response), MSG_NOSIGNAL);

        if (sent_bytes < 0) {
            qrzl_err("Failed to send cmp url: %s", strerror(errno));
        } else {
            qrzl_log("Successfully sent auth page, sent_bytes: %d\n\n", sent_bytes);
        }
        
        free(mac);
        free(client_ip);
    }
    close(client_socket);
    return;
}

// ============================== 移动认证业务代码 start===============================================

void extract_item_for_response(char *key, const char *request, char *out_phone, size_t max_len) {
    // qrzl_log("extract_phone 请求http_request : %s\n", request);
    const char *start = strstr(request, key);
    if (start) {
        start += strlen(key);  // 跳过 "phoneNumber=" 部分
        size_t i = 0;
        while (i < max_len - 1 && start[i] != '\0' && start[i] != '&' && start[i] != ' ' && start[i] != '\r' && start[i] != '\n') {
            out_phone[i] = start[i];
            i++;
        }
        out_phone[i] = '\0';
    } else {
        out_phone[0] = '\0'; // 未找到时返回空字符串
    }
}


#ifdef QRZL_ONE_LINK_AUTH
static void handle_one_link_auth_request(int client_socket, const char *buffer, int bytes_read)
{
    // 使用编译时宏替代运行时配置
#ifdef QRZL_ONE_LINK_CUSTOMER_WUXING
    qrzl_log("ONE_LINK customer type: WUXING (compile-time)");
#elif defined(QRZL_ONE_LINK_CUSTOMER_JIUYAO)
    qrzl_log("ONE_LINK customer type: JIUYAO (compile-time)");
#else
    qrzl_log("ONE_LINK customer type: DEFAULT (compile-time)");
#endif

    int sent_bytes;
    qrzl_log("Received %zd bytes: \n%.*s", bytes_read, (int)bytes_read, buffer);

    // 内置html页面， 用于 socket 发送的格式
    const char *one_link_auth_html_str = get_auth_html();
    if (strlen(one_link_auth_html_str) < 0) {
        qrzl_err("ONE LINK Auth Page init Failed!!");
        return;
    }

    // 获取客户端ip
    char *client_ip = get_client_ip(client_socket);
    if (!client_ip) {
        const char *error_response = "HTTP/1.1 500 Internal Server Error\r\n\r\n";
        sent_bytes = send(client_socket, error_response, strlen(error_response), MSG_NOSIGNAL);
        if (sent_bytes < 0 && errno != EPIPE) {
            qrzl_err("Failed to send Internal Server: %s", strerror(errno));
        }
        close(client_socket);
        return;
    }
    
    // 获取客户端mac (支持IPv4和IPv6)
    char *mac = NULL;
#ifdef CONFIG_IPV6_PORTAL
    if (is_ipv6_address(client_ip)) {
        mac = get_mac_from_ipv6(client_ip);
        qrzl_log("IPv6 client: %s, MAC: %s", client_ip, mac ? mac : "not found");
    } else {
        mac = get_mac_from_ip(client_ip);
        qrzl_log("IPv4 client: %s, MAC: %s", client_ip, mac ? mac : "not found");
    }
#else
    mac = get_mac_from_ip(client_ip);
    qrzl_log("IPv4 client: %s, MAC: %s", client_ip, mac ? mac : "not found");
#endif

    if (!mac) {
        const char *error_response = "HTTP/1.1 400 Bad Request\r\n\r\nFailed to get MAC address";
        sent_bytes = send(client_socket, error_response, strlen(error_response), MSG_NOSIGNAL);
        if (sent_bytes < 0 && errno != EPIPE) {
            qrzl_err("Failed to send get MAC address: %s", strerror(errno));
        }
        close(client_socket);
        return;
    }

    // 便携式联网设备(手机)mac
    char terminalMac[32] = {0};
    convert_mac_format(mac, terminalMac, sizeof(terminalMac), '\0');
    
    // 获取本机mac
    char device_mac[32] = {0};
    convert_mac_format(g_qrzl_device_static_data.mac, device_mac, sizeof(device_mac), '\0');


    if(strstr(buffer, "GET /get_code") != NULL )
    {
        qrzl_log("client_ip=%s mac=%s", client_ip, mac);

        // 获取手机号
        char phone[32] = {0};
        extract_item_for_response("phoneNumber=", buffer, phone, sizeof(phone));

        qrzl_log("用户请求验证码，手机号为: %s\n", phone);
        if(is_valid_phone(phone) != 1) {
            const char *json_error_response =
            "HTTP/1.1 200 OK\r\n"
            "Content-Type: application/json\r\n"
            "Connection: close\r\n"
            "\r\n"
            "{\"code\":\"1\", \"msg\":\"手机号不合法\"}";
            sent_bytes = send(client_socket, json_error_response, strlen(json_error_response), MSG_NOSIGNAL);
            if (sent_bytes < 0 && errno != EPIPE) {
                qrzl_err("Failed to send Internal Server: %s", strerror(errno));
            }
            free(mac);
            free(client_ip);
            close(client_socket);
            return;
        }
        
        // 发送验证码
        if (get_sms_code(mac, phone) == 0) {
            const char *json_success_response =
            "HTTP/1.1 200 OK\r\n"
            "Content-Type: application/json\r\n"
            "Connection: close\r\n"
            "\r\n"
            "{\"code\":\"0\", \"msg\":\"验证码已发送\"}";
            sent_bytes = send(client_socket, json_success_response, strlen(json_success_response), MSG_NOSIGNAL);
        } else {
            const char *json_success_response =
            "HTTP/1.1 200 OK\r\n"
            "Content-Type: application/json\r\n"
            "Connection: close\r\n"
            "\r\n"
            "{\"code\":\"500\", \"msg\":\"验证码发送失败\"}";
            sent_bytes = send(client_socket, json_success_response, strlen(json_success_response), MSG_NOSIGNAL);
        }
    //以表单submit方式提交认证信息
    // } else if(strstr(buffer, "POST /auth") != NULL )
    } else if(strstr(buffer, "GET /send_verify") != NULL )
    {
        // 获取手机号
        char phone[32] = {0};
        extract_item_for_response("phoneNumber=", buffer, phone, sizeof(phone));

        // 获取验证码
        char verify_code[12] = {0};
        extract_item_for_response("code=" ,buffer, verify_code, sizeof(verify_code));
        qrzl_log("用户请求认证, phoneNumber 为: %s\n", phone);
        qrzl_log("用户请求认证, verify_code 为: %s\n", verify_code);

        //获取网关
        char lan_ipaddr[30] = {0};
        cfg_get_item("lan_ipaddr", lan_ipaddr, sizeof(lan_ipaddr));

        // 构造 HTTP 302 响应
        char custom_response[2048];
        
        // 发起认证
        if(one_link_terminal_auth(mac, phone, verify_code) != 0) {
            // * 认证失败
            qrzl_err("One Link auth Failed. Phone: %s", phone);

            // 对servAddr进行编码
            // char servAddr[128] = {0};
            // char servAddr_encoded_str[256] = {0};
            // snprintf(servAddr, sizeof(servAddr), "http://%s:80/one_link_auth", lan_ipaddr);
            // if (url_encode(servAddr, servAddr_encoded_str) < 0) {
            //     qrzl_log("移动回调地址encode 失败!!");
            // } else {
            //     qrzl_log("移动回调地址完成: %s\n", servAddr_encoded_str);
            // }

            snprintf(custom_response, sizeof(custom_response),
            "HTTP/1.1 200 OK\r\n"
            "Content-Type: text/html; charset=utf-8\r\n"
            "Cache-Control: no-store\r\n"
            "Pragma: no-cache\r\n"
            "Connection: close\r\n\r\n"
            "<!DOCTYPE html>\n"
            "<html lang=\"zh-CN\">\n"
            "<head>\n"
            "  <meta charset=\"UTF-8\" />\n"
            "  <meta http-equiv=\"refresh\" content=\"0;url=http://%s:80%s\" />\n"
            "  <title>认证跳转</title>\n"
            "</head>\n"
            "<body>\n"
            "  <p>正在跳转，请稍候...</p>\n"
            "</body>\n"
            "</html>\n",
            lan_ipaddr, AUTH_FAILED);

            // snprintf(custom_response, sizeof(custom_response),
            // "HTTP/1.1 302 Found\r\n"
            // "Location: http://%s:80%s\r\n"
            // "Content-Length: 0\r\n"
            // "Connection: close\r\n\r\n", lan_ipaddr, AUTH_FAILED);

            sent_bytes = send(client_socket, custom_response, strlen(custom_response), MSG_NOSIGNAL);
            if (sent_bytes < 0) {
                qrzl_err("Failed to send one_link url: %s", strerror(errno));
            } else {
                qrzl_log("Successfully sent auth page, sent_bytes: \n%d\n", sent_bytes);
            }
        } else {
            // * 认证成功，将mac加入已认证
            if (write_mac_to_proc(mac) < 0) {
                const char *error_response = "HTTP/1.1 500 Internal Server Error\r\n\r\n";
                sent_bytes = send(client_socket, error_response, strlen(error_response), MSG_NOSIGNAL);
                if (sent_bytes < 0 && errno != EPIPE) {
                    qrzl_err("Failed to send authorize: %s", strerror(errno));
                }
            }

            // 将终端mac更新到nv中
            // char nv_old[5048] = {0};
            // cfg_get_item("one_link_authed_mac", nv_old, sizeof(nv_old));
            // char mac_nv[32] = {0};
            // convert_mac_format(terminalMac, mac_nv, sizeof(mac_nv), ':');
            // int save_res = nv_add_mac(nv_old, mac_nv, sizeof(nv_old));
            // if (save_res == 1) {
            //     cfg_set("one_link_authed_mac", nv_old);
            //     qrzl_log("移动本地认证信息已更新: %s", nv_old);
            // }

            char mac_nv[32] = {0};
            convert_mac_format(terminalMac, mac_nv, sizeof(mac_nv), ':');
            update_authed_mac_list("one_link_authed_mac", mac_nv);
            
            // 上报终端信息
            one_link_push_device_info(1, mac, client_ip);

            snprintf(custom_response, sizeof(custom_response),
            "HTTP/1.1 200 OK\r\n"
            "Content-Type: text/html; charset=utf-8\r\n"
            "Cache-Control: no-store\r\n"
            "Pragma: no-cache\r\n"
            "Connection: close\r\n\r\n"
            "<!DOCTYPE html>\n"
            "<html lang=\"zh-CN\">\n"
            "<head>\n"
            "  <meta charset=\"UTF-8\" />\n"
            "  <meta http-equiv=\"refresh\" content=\"0;url=http://%s:80%s\" />\n"
            "  <title>认证跳转</title>\n"
            "</head>\n"
            "<body>\n"
            "  <p>正在跳转，请稍候...</p>\n"
            "</body>\n"
            "</html>\n",
            lan_ipaddr, AUTH_SUCCESS);


            sent_bytes = send(client_socket, custom_response, strlen(custom_response), MSG_NOSIGNAL);
            if (sent_bytes < 0) {
                qrzl_err("Failed to send one_link url: %s", strerror(errno));
            } else {
                qrzl_log("Successfully sent auth page, sent_bytes: \n%d\n", sent_bytes);
            }
        }
        
        free(mac);
        free(client_ip);
        close(client_socket);
        return;
        
    } else if (is_probe_request(buffer)) {
        //获取网关
        char lan_ipaddr[30] = {0};
        cfg_get_item("lan_ipaddr", lan_ipaddr, sizeof(lan_ipaddr));

        // 回调地址
        char servAddr[128] = {0};
        snprintf(servAddr, sizeof(servAddr), "http://%s:80/one_link_auth", lan_ipaddr);

#ifdef QRZL_ONE_LINK_CUSTOMER_WUXING
        {
            // 获取认证跳转地址
            CurlResult curl_res = wuxing_get_sign_info(g_qrzl_device_static_data.mac, mac, servAddr);
            if (strcmp(curl_res.code, "200") != 0) {
                // 接口未正常返回，返回异常页面
                char *html_page = wuxing_exception_page(curl_res.msg, curl_res.code);
                if (html_page) {
                    // 发送
                    sent_bytes = send(client_socket, html_page, strlen(html_page), MSG_NOSIGNAL);
                    if (sent_bytes < 0) {
                        qrzl_err("Failed to send html_page: %s", strerror(errno));
                    } else {
                        qrzl_log("Successfully sent auth page, sent_bytes: %d", sent_bytes);
                    }
                    free(html_page);
                    free(mac);
                    free(client_ip);
                    close(client_socket);
                    return;
                }
            }
        }
#endif

#ifdef QRZL_ONE_LINK_CUSTOMER_JIUYAO
        {
            // 获取认证跳转地址
            JiuYaoCurlResult curl_res = jiuyao_get_sign_info(g_qrzl_device_static_data.mac, mac, servAddr);
            if (strcmp(curl_res.code, "200") != 0) {
                // 接口未正常返回，返回异常页面
                char *html_page = jiuyao_exception_page(curl_res.msg, curl_res.code);
                if (html_page) {
                    // 发送
                    sent_bytes = send(client_socket, html_page, strlen(html_page), MSG_NOSIGNAL);
                    if (sent_bytes < 0) {
                        qrzl_err("Failed to send html_page: %s", strerror(errno));
                    } else {
                        qrzl_log("Successfully sent auth page, sent_bytes: %d", sent_bytes);
                    }
                    free(html_page);
                    free(mac);
                    free(client_ip);
                    close(client_socket);
                    return;
                }
            }
        }
#endif

        if(one_link_terminal_is_authed(device_mac, terminalMac, servAddr)) {
            qrzl_log("当前 MAC: %s, 已认证！", mac);
            write_mac_to_proc(mac);
            if(one_link_check_mac_in_last_station(mac) !=1){
                // 上报终端信息
                one_link_push_device_info(1, terminalMac, client_ip);
                one_link_change_last_station_mac(mac);
            }
            free(mac);
            free(client_ip);
            close(client_socket);
            return;
        }

        qrzl_log("当前mac: %s, 未认证, 开始进行认证..", mac);
        
        // 移动认证页面地址
        char customers_page_url[5048] = {0};

        // if (strncmp(one_link_customer_info, "WUXING", sizeof(one_link_customer_info)) == 0) {
        //     // 尝试获取客户自定义的认证页面
        //     int try_count = 3;

        //     while (try_count > 0 && cfg_get_item("ONE_LINK_customers_auth_page_url", customers_page_url, sizeof(customers_page_url)) == 0)
        //     {   
        //         try_count --;
        //         if (strcmp("", customers_page_url) != 0) {
        //             snprintf(one_link_auth_url, sizeof(one_link_auth_url), "%s", customers_page_url);
        //             break;
        //         }
        //         sleep(1);
        //     }
        // }

        // 构建认证页面
        char init_response[8192];
        int html_len = strlen(one_link_auth_html_str);
        int response_len;

        // 获取第三方认证页面地址(可能没有)
        if (one_link_get_customers_page_url(customers_page_url, sizeof(customers_page_url), g_qrzl_device_static_data.mac, terminalMac, client_ip, servAddr) != 0) {
            qrzl_log("customers_page_url构建失败!");
        }

        // 如果自定义页面nv有值则跳转到该页面, 否则使用内置页面
        if (strlen(customers_page_url) > 0) {
            // 构造 200 响应，兼容性更强
            response_len = snprintf(init_response, sizeof(init_response),
                "HTTP/1.1 200 OK\r\n"
                "Content-Type: text/html\r\n"
                "Content-Length: %zu\r\n"
                "Connection: close\r\n\r\n"
                "<html><head><meta http-equiv='refresh' content='0;url=%s'></head></html>",
                strlen(customers_page_url) + 70,  // 简单估算长度
                customers_page_url);
        } else {
            // 构造 200 响应，兼容性更强
            response_len = snprintf(init_response, sizeof(init_response),
                "HTTP/1.1 200 OK\r\n"
                "Content-Type: text/html; charset=UTF-8\r\n"
                "Content-Length: %d\r\n"
                "Cache-Control: no-cache, no-store, must-revalidate\r\n"
                "Pragma: no-cache\r\n"
                "Expires: 0\r\n"
                "Connection: close\r\n"
                "\r\n"
                "%s",
                html_len, one_link_auth_html_str);
        }

        // if (strncmp(one_link_customer_info, CLIENT_WUXING, sizeof(one_link_customer_info)) == 0) {
        //     // 构造 HTTP 302 响应
        //     // snprintf(init_response, sizeof(init_response),
        //     //     "HTTP/1.1 302 Found\r\n"
        //     //     "Location: %s\r\n"
        //     //     "Content-Length: 0\r\n"
        //     //     "Connection: close\r\n\r\n",
        //     //     one_link_auth_url);

        //     // 构造 200 响应，兼容性更强
        //     snprintf(init_response, sizeof(init_response),
        //         "HTTP/1.1 200 OK\r\n"
        //         "Content-Type: text/html\r\n"
        //         "Content-Length: %zu\r\n"
        //         "Connection: close\r\n\r\n"
        //         "<html><head><meta http-equiv='refresh' content='0;url=%s'></head></html>",
        //         strlen(one_link_auth_url) + 70,  // 简单估算长度
        //         one_link_auth_url);
        // } else {
        //     response_len = snprintf(init_response, sizeof(init_response),
        //         "HTTP/1.1 200 OK\r\n"
        //         "Content-Type: text/html; charset=UTF-8\r\n"
        //         "Content-Length: %d\r\n"
        //         "Cache-Control: no-cache, no-store, must-revalidate\r\n"
        //         "Pragma: no-cache\r\n"
        //         "Expires: 0\r\n"
        //         "Connection: close\r\n"
        //         "\r\n"
        //         "%s",
        //         html_len, one_link_auth_html_str);
        // }

        // qrzl_log("准备跳转到认证地址: %s", init_response);

        sent_bytes = send(client_socket, init_response, response_len, MSG_NOSIGNAL);
        if (sent_bytes < 0) {
            qrzl_err("Failed to send one_link init_response: %s", strerror(errno));
        } else {
            qrzl_log("Successfully sent auth page, sent_bytes: %d", sent_bytes);
        }
        
        free(mac);
        free(client_ip);
    }
    
    close(client_socket);
}
#endif // QRZL_ONE_LINK_AUTH
// ============================== 移动认证业务代码 end===============================================

// 不在认证范围的客户统一全部放行
static void handle_request_all_pass(int client_socket, const char *buffer, int bytes_read)
{
    int sent_bytes;

    qrzl_log("Received %zd bytes: \n%.*s", bytes_read, (int)bytes_read, buffer);

     char *client_ip = get_client_ip(client_socket);
    if (!client_ip) {
        const char *error_response = "HTTP/1.1 500 Internal Server Error\r\n\r\n";
        sent_bytes = send(client_socket, error_response, strlen(error_response), MSG_NOSIGNAL);
        if (sent_bytes < 0 && errno != EPIPE) {
            qrzl_err("Failed to send Internal Server: %s", strerror(errno));
        }
        close(client_socket);
        return;
    }

    // 获取客户端mac (支持IPv4和IPv6)
    char *mac = NULL;
#ifdef CONFIG_IPV6_PORTAL
    if (is_ipv6_address(client_ip)) {
        mac = get_mac_from_ipv6(client_ip);
        qrzl_log("IPv6 client: %s, MAC: %s", client_ip, mac ? mac : "not found");
    } else {
        mac = get_mac_from_ip(client_ip);
        qrzl_log("IPv4 client: %s, MAC: %s", client_ip, mac ? mac : "not found");
    }
#else
    mac = get_mac_from_ip(client_ip);
    qrzl_log("IPv4 client: %s, MAC: %s", client_ip, mac ? mac : "not found");
#endif

    if (!mac) {
        const char *error_response = "HTTP/1.1 400 Bad Request\r\n\r\nFailed to get MAC address";
        sent_bytes = send(client_socket, error_response, strlen(error_response), MSG_NOSIGNAL);
        if (sent_bytes < 0 && errno != EPIPE) {
            qrzl_err("Failed to send get MAC address: %s", strerror(errno));
        }
        close(client_socket);
        return;
    }
	qrzl_log("client_ip=%s mac=%s", client_ip, mac);

    if (write_mac_to_proc(mac) < 0) {
        const char *error_response = "HTTP/1.1 500 Internal Server Error\r\n\r\nFailed to authorize";
        sent_bytes = send(client_socket, error_response, strlen(error_response), MSG_NOSIGNAL);
        if (sent_bytes < 0 && errno != EPIPE) {
            qrzl_err("Failed to send authorize: %s", strerror(errno));
        }
        free(mac);
        close(client_socket);
        return;
    }
}

// 处理HTTP请求
#ifdef JCV_FEATURE_INTER_AD_PAGE_AUTH
static void handle_request(int client_socket, const char *buffer, int bytes_read) {
    int sent_bytes;

    qrzl_log("Received %zd bytes: \n%.*s", bytes_read, (int)bytes_read, buffer);

#ifdef JCV_FEATURE_INTER_AD_PAGE_AUTH
    // 解析请求路径
    if (strstr(buffer, "GET /") != NULL) {
        // 返回广告页面
        // 在代码中动态生成响应
        char full_response[2048];
        int body_len = strlen(g_ad_page_body);
        
        snprintf(full_response, sizeof(full_response), g_ad_page_header, body_len);
        strcat(full_response, g_ad_page_body);
        sent_bytes = send(client_socket, full_response, strlen(full_response), MSG_NOSIGNAL);
        if (sent_bytes < 0 && errno != EPIPE) {
            qrzl_err("Failed to send full_response: %s", strerror(errno));
        }
    } else if (strstr(buffer, "POST /auth ") != NULL) {
        // 处理认证请求
        char *client_ip = get_client_ip(client_socket);
        if (!client_ip) {
            const char *error_response = "HTTP/1.1 500 Internal Server Error\r\n\r\n";
            sent_bytes = send(client_socket, error_response, strlen(error_response), MSG_NOSIGNAL);
            if (sent_bytes < 0 && errno != EPIPE) {
                qrzl_err("Failed to send Internal Server: %s", strerror(errno));
            }
            close(client_socket);
            return;
        }

        // 获取客户端mac (支持IPv4和IPv6)
        char *mac = NULL;
#ifdef CONFIG_IPV6_PORTAL
        if (is_ipv6_address(client_ip)) {
            mac = get_mac_from_ipv6(client_ip);
            qrzl_log("IPv6 client: %s, MAC: %s", client_ip, mac ? mac : "not found");
        } else {
            mac = get_mac_from_ip(client_ip);
            qrzl_log("IPv4 client: %s, MAC: %s", client_ip, mac ? mac : "not found");
        }
#else
        mac = get_mac_from_ip(client_ip);
        qrzl_log("IPv4 client: %s, MAC: %s", client_ip, mac ? mac : "not found");
#endif

        if (!mac) {
            const char *error_response = "HTTP/1.1 400 Bad Request\r\n\r\nFailed to get MAC address";
            sent_bytes = send(client_socket, error_response, strlen(error_response), MSG_NOSIGNAL);
            if (sent_bytes < 0 && errno != EPIPE) {
                qrzl_err("Failed to send get MAC address: %s", strerror(errno));
            }
            close(client_socket);
            return;
        }
        qrzl_log("client_ip=%s mac=%s", client_ip, mac);

        if (write_mac_to_proc(mac) < 0) {
            const char *error_response = "HTTP/1.1 500 Internal Server Error\r\n\r\nFailed to authorize";
            sent_bytes = send(client_socket, error_response, strlen(error_response), MSG_NOSIGNAL);
            if (sent_bytes < 0 && errno != EPIPE) {
                qrzl_err("Failed to send authorize: %s", strerror(errno));
            }
            free(mac);
            close(client_socket);
            return;
        }

        // 返回重定向响应
        sent_bytes = send(client_socket, g_redirect_page, strlen(g_redirect_page), MSG_NOSIGNAL);
        if (sent_bytes < 0 && errno != EPIPE) {
            qrzl_err("Failed to send g_redirect_page: %s", strerror(errno));
        }
        free(mac);
    } else if (strstr(buffer, "GET /favicon.ico") != NULL) {
        //增加通用响应 处理浏览器可能发送的额外请求
        const char *not_found = "HTTP/1.1 404 Not Found\r\n\r\n";
        sent_bytes = send(client_socket, not_found, strlen(not_found), MSG_NOSIGNAL);
        if (sent_bytes < 0 && errno != EPIPE) {
            qrzl_err("Failed to send 404 Not Found: %s", strerror(errno));
        }
    } else {
        // 返回404错误
        const char *not_found_response = "HTTP/1.1 404 Not Found\r\n\r\n";
        sent_bytes = send(client_socket, not_found_response, strlen(not_found_response), MSG_NOSIGNAL);
        if (sent_bytes < 0 && errno != EPIPE) {
            qrzl_err("Failed to send not_found_response: %s", strerror(errno));
        }
    }
#else
    char *client_ip = get_client_ip(client_socket);
    if (!client_ip) {
        const char *error_response = "HTTP/1.1 500 Internal Server Error\r\n\r\n";
        sent_bytes = send(client_socket, error_response, strlen(error_response), MSG_NOSIGNAL);
        if (sent_bytes < 0 && errno != EPIPE) {
            qrzl_err("Failed to send Internal Server: %s", strerror(errno));
        }
        close(client_socket);
        return;
    }

    // 获取客户端mac (支持IPv4和IPv6)
    char *mac = NULL;
#ifdef CONFIG_IPV6_PORTAL
    if (is_ipv6_address(client_ip)) {
        mac = get_mac_from_ipv6(client_ip);
        qrzl_log("IPv6 client: %s, MAC: %s", client_ip, mac ? mac : "not found");
    } else {
        mac = get_mac_from_ip(client_ip);
        qrzl_log("IPv4 client: %s, MAC: %s", client_ip, mac ? mac : "not found");
    }
#else
    mac = get_mac_from_ip(client_ip);
    qrzl_log("IPv4 client: %s, MAC: %s", client_ip, mac ? mac : "not found");
#endif

    if (!mac) {
        const char *error_response = "HTTP/1.1 400 Bad Request\r\n\r\nFailed to get MAC address";
        sent_bytes = send(client_socket, error_response, strlen(error_response), MSG_NOSIGNAL);
        if (sent_bytes < 0 && errno != EPIPE) {
            qrzl_err("Failed to send get MAC address: %s", strerror(errno));
        }
        close(client_socket);
        return;
    }
	qrzl_log("client_ip=%s mac=%s", client_ip, mac);

    if (write_mac_to_proc(mac) < 0) {
        const char *error_response = "HTTP/1.1 500 Internal Server Error\r\n\r\nFailed to authorize";
        sent_bytes = send(client_socket, error_response, strlen(error_response), MSG_NOSIGNAL);
        if (sent_bytes < 0 && errno != EPIPE) {
            qrzl_err("Failed to send authorize: %s", strerror(errno));
        }
        free(mac);
        close(client_socket);
        return;
    }

	// 返回302重定向响应
	int cfg_ret;
	char ad_server_url[64] = {0};
	char redirect_response[256];
	cfg_ret = cfg_get_item("qrzl_ad_server_url", ad_server_url, sizeof(ad_server_url));
	qrzl_log("cfg_get_item qrzl_ad_server_url=%s", ad_server_url);
	if(cfg_ret != 0 || strlen(ad_server_url) ==0 || strstr(ad_server_url, "http://") == NULL)
	{
		snprintf(redirect_response, sizeof(redirect_response),
				"HTTP/1.1 302 Found\r\n"
				"Location: http://neverssl.com\r\n"
				"\r\n");
	}
	else
	{
		snprintf(redirect_response, sizeof(redirect_response),
				"HTTP/1.1 302 Found\r\n"
				"Location: %s\r\n"
				"\r\n", ad_server_url);
	}

    sent_bytes = send(client_socket, redirect_response, strlen(redirect_response), MSG_NOSIGNAL);
    if (sent_bytes < 0 && errno != EPIPE) {
        qrzl_err("Failed to send redirect: %s", strerror(errno));
    } else {
        qrzl_log("success send client_socket: %d sent_bytes: %d", client_socket, sent_bytes);
    }
    free(mac);
#endif
    close(client_socket);
}
#endif // JCV_FEATURE_INTER_AD_PAGE_AUTH

// 异步处理HTTP请求
static void async_handle_request(struct client_info *client) {

    char qrzl_user_net_disconn[2] = {0};
    cfg_get_item("qrzl_user_net_disconn", qrzl_user_net_disconn, sizeof(qrzl_user_net_disconn));
    qrzl_log("设备 qrzl_user_net_disconn : %s", qrzl_user_net_disconn);
    if (qrzl_user_net_disconn && strcmp(qrzl_user_net_disconn, "1") == 0) {
        qrzl_log("设备网络被停用，取消本次sokect连接....");
        return;
    }

    // 简化示例，实际需要处理不完整数据
    qrzl_log("Processing async request from fd:%d buffer len: %d", client->fd, client->bytes_read);

    if (strstr(client->buffer, "\r\n\r\n") != NULL) {
        int handled = 0;

        // 获取运营商
        char sim_imsi[32] = {0};
        cfg_get_item("sim_imsi", sim_imsi, sizeof(sim_imsi));
        int isp = get_isp_by_imsi(sim_imsi);
        qrzl_log("current card isp is %d (0无匹配, 1移动, 2联通, 3电信)", isp);

        // 读取 qrzl_cloud_authentic_switch 认证开关
        char authentic_switch[8] = {0};
        if(cfg_get_item("qrzl_cloud_authentic_switch", authentic_switch, sizeof(authentic_switch)) != 0) {
            qrzl_log("qrzl_cloud_authentic_switch is miss!");
            // 没定义默认开启
            snprintf(authentic_switch, sizeof(authentic_switch), "%s", "1");
        }
        
        if (strncmp(authentic_switch, "1", 1) != 0) {
            qrzl_log("未开启认证开关，走默认处理逻辑，终端统一全部放行");
            handle_request_all_pass(client->fd, client->buffer, client->bytes_read);
            return;
        }

        // 只有运营商匹配 + 对应宏定义存在，才处理专属认证
        if (isp == 1) {
#ifdef QRZL_ONE_LINK_AUTH
            qrzl_log("当前是移动卡，启用 OneLink 认证处理");
            handle_one_link_auth_request(client->fd, client->buffer, client->bytes_read);
            handled = 1;
#endif
        } else if (isp == 3) {
#ifdef QRZL_CMP_AUTH
            qrzl_log("当前是电信卡，启用 CMP 认证处理");
            handle_cmp_auth_request(client->fd, client->buffer, client->bytes_read);
            handled = 1;
#endif
        }

        // 如果未处理，统一走默认处理逻辑
        if (handled == 0) {
            qrzl_log("未命中认证条件，走默认处理逻辑");
            qrzl_log("卡片未开启认证，终端统一全部放行");
            handle_request_all_pass(client->fd, client->buffer, client->bytes_read);
        }

        // 清理状态
        client->bytes_read = 0;
        memset(client->buffer, 0, BUFFER_SIZE);
    } 
    // else if (client->bytes_read == -1) {
    //     //没接收到数据 直接给客户端发 广告页面, 不过用户可能都感知不到广告页面弹出
    //     handle_request(client->fd, client->buffer, 0); 
    // }
}

static void *async_http_server_thread_handler(void *arg) {
    int server_fd, epoll_fd;
#ifdef CONFIG_IPV6_PORTAL
    int server_fd_v6;
    struct sockaddr_in6 server_addr_v6;
    struct sockaddr_storage client_addr;
#else
    struct sockaddr_in client_addr;
#endif
    struct epoll_event ev, events[MAX_EVENTS];
    struct sockaddr_in server_addr;
    socklen_t client_len = sizeof(client_addr);

    // 创建IPv4监听套接字 非阻塞
    if ((server_fd = socket(AF_INET, SOCK_STREAM | SOCK_NONBLOCK, 0)) < 0) {
        qrzl_err("IPv4 HTTP socket creation failed");
        return NULL;
    }

#ifdef CONFIG_IPV6_PORTAL
    // 创建IPv6监听套接字 非阻塞
    if ((server_fd_v6 = socket(AF_INET6, SOCK_STREAM | SOCK_NONBLOCK, 0)) < 0) {
        qrzl_err("IPv6 HTTP socket creation failed");
        close(server_fd);
        return NULL;
    }
#endif

    // 允许重用处于 TIME_WAIT 状态的端口
    int optval = 1;
    setsockopt(server_fd, SOL_SOCKET, SO_REUSEADDR, &optval, sizeof(optval));
#ifdef CONFIG_IPV6_PORTAL
    setsockopt(server_fd_v6, SOL_SOCKET, SO_REUSEADDR, &optval, sizeof(optval));

    // 设置IPv6 socket只接受IPv6连接（不接受IPv4映射）
    setsockopt(server_fd_v6, IPPROTO_IPV6, IPV6_V6ONLY, &optval, sizeof(optval));
#endif

    // 绑定IPv4端口
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_addr.s_addr = INADDR_ANY;
    server_addr.sin_port = htons(LOCAL_GATEWAY_PORT);

    if (bind(server_fd, (struct sockaddr *)&server_addr, sizeof(server_addr)) < 0) {
        qrzl_err("Failed to bind IPv4 socket");
        close(server_fd);
#ifdef CONFIG_IPV6_PORTAL
        close(server_fd_v6);
#endif
        return NULL;
    }

#ifdef CONFIG_IPV6_PORTAL
    // 绑定IPv6端口
    memset(&server_addr_v6, 0, sizeof(server_addr_v6));
    server_addr_v6.sin6_family = AF_INET6;
    memcpy(&server_addr_v6.sin6_addr, &in6addr_any, sizeof(struct in6_addr));
    server_addr_v6.sin6_port = htons(LOCAL_GATEWAY_PORT);

    if (bind(server_fd_v6, (struct sockaddr *)&server_addr_v6, sizeof(server_addr_v6)) < 0) {
        qrzl_err("Failed to bind IPv6 socket");
        close(server_fd);
        close(server_fd_v6);
        return NULL;
    }
#endif

    if (listen(server_fd, 10) < 0) {
        qrzl_err("Failed to listen on IPv4 socket");
        close(server_fd);
#ifdef CONFIG_IPV6_PORTAL
        close(server_fd_v6);
#endif
        return NULL;
    }

#ifdef CONFIG_IPV6_PORTAL
    if (listen(server_fd_v6, 10) < 0) {
        qrzl_err("Failed to listen on IPv6 socket");
        close(server_fd);
        close(server_fd_v6);
        return NULL;
    }

    qrzl_log("captive_portal async_http_server is listening on IPv4 and IPv6 port %d...\n", LOCAL_GATEWAY_PORT);
#else
    qrzl_log("captive_portal async_http_server is listening on IPv4 port %d...\n", LOCAL_GATEWAY_PORT);
#endif

    // 创建epoll实例
    epoll_fd = epoll_create1(0);
    if (epoll_fd == -1) {
        qrzl_err("epoll_create1 failed");
        close(server_fd);
#ifdef CONFIG_IPV6_PORTAL
        close(server_fd_v6);
#endif
        return NULL;
    }

    // 添加IPv4监听socket到epoll
    ev.events = EPOLLIN;
    ev.data.fd = server_fd;

    if (epoll_ctl(epoll_fd, EPOLL_CTL_ADD, server_fd, &ev) == -1) {
        qrzl_err("epoll_ctl IPv4 listen_sock failed");
        close(server_fd);
#ifdef CONFIG_IPV6_PORTAL
        close(server_fd_v6);
#endif
        close(epoll_fd);
        return NULL;
    }

#ifdef CONFIG_IPV6_PORTAL
    // 添加IPv6监听socket到epoll
    ev.events = EPOLLIN;
    ev.data.fd = server_fd_v6;

    if (epoll_ctl(epoll_fd, EPOLL_CTL_ADD, server_fd_v6, &ev) == -1) {
        qrzl_err("epoll_ctl IPv6 listen_sock failed");
        close(server_fd);
        close(server_fd_v6);
        close(epoll_fd);
        return NULL;
    }
#endif

    // 主事件循环
    while (1) {
        int nfds = epoll_wait(epoll_fd, events, MAX_EVENTS, -1);
        if (nfds == -1) {
            qrzl_err("epoll_wait error");
            continue;
        }

        int n;
        for (n = 0; n < nfds; ++n) {
#ifdef CONFIG_IPV6_PORTAL
            if (events[n].data.fd == server_fd || events[n].data.fd == server_fd_v6) {
#else
            if (events[n].data.fd == server_fd) {
#endif
                // 接受新连接 (IPv4 或 IPv6)
                int client_fd = accept4(events[n].data.fd, (struct sockaddr*)&client_addr, &client_len, SOCK_NONBLOCK);
                if (client_fd == -1) {
                    qrzl_err("accept error");
                    continue;
                }

                // 添加客户端信息打印 (支持IPv4和IPv6)
#ifdef CONFIG_IPV6_PORTAL
                char client_ip[INET6_ADDRSTRLEN];
                int client_port = 0;

                if (client_addr.ss_family == AF_INET) {
                    struct sockaddr_in *addr_in = (struct sockaddr_in *)&client_addr;
                    inet_ntop(AF_INET, &addr_in->sin_addr, client_ip, sizeof(client_ip));
                    client_port = ntohs(addr_in->sin_port);
                    qrzl_log("Async IPv4 client_fd: %d connected: %s:%d", client_fd, client_ip, client_port);
                } else if (client_addr.ss_family == AF_INET6) {
                    struct sockaddr_in6 *addr_in6 = (struct sockaddr_in6 *)&client_addr;
                    inet_ntop(AF_INET6, &addr_in6->sin6_addr, client_ip, sizeof(client_ip));
                    client_port = ntohs(addr_in6->sin6_port);
                    qrzl_log("Async IPv6 client_fd: %d connected: [%s]:%d", client_fd, client_ip, client_port);
                }
#else
                struct sockaddr_in *addr_in = (struct sockaddr_in *)&client_addr;
                char client_ip[INET_ADDRSTRLEN];
                inet_ntop(AF_INET, &addr_in->sin_addr, client_ip, sizeof(client_ip));
                int client_port = ntohs(addr_in->sin_port);
                qrzl_log("Async IPv4 client_fd: %d connected: %s:%d", client_fd, client_ip, client_port);
#endif

                // 为新连接分配上下文
                struct client_info *client = calloc(1, sizeof(*client));
                client->fd = client_fd;

                // 添加到epoll监控
                ev.events = EPOLLIN | EPOLLET | EPOLLRDHUP; // 增加连接关闭检测
                ev.data.ptr = client;
                if (epoll_ctl(epoll_fd, EPOLL_CTL_ADD, client_fd, &ev) == -1) {
                    qrzl_err("epoll_ctl client_fd failed");
                    close(client_fd);
                    free(client);
                }
            } else {
                // 处理客户端事件
                struct client_info *client = (struct client_info*)events[n].data.ptr;
                ssize_t count;
                
                while ((count = recv(client->fd, 
                                   client->buffer + client->bytes_read,
                                   BUFFER_SIZE - client->bytes_read, 0)) > 0) {
                    client->bytes_read += count;
                    if (client->bytes_read >= BUFFER_SIZE) break;
                }

                //qrzl_log("recv client_fd: %d count: %d", client->fd, count);     
                //errno != EAGAIN  为没有可读数据是，因为是非阻塞的 这种情况下正常的，这种情况下可以直接给客户端发广告页面(服务端先发送)
                if (count == -1 && errno != EAGAIN) {
                //if (count == -1) {
                    // 错误处理
                    qrzl_err("Recv error on fd %d: %s", client->fd, strerror(errno));
                    epoll_ctl(epoll_fd, EPOLL_CTL_DEL, client->fd, NULL);
                    close(client->fd);
                    free(client);
                } else if (count == 0) {
                    // 连接关闭
                    qrzl_err("Recv close on fd %d", client->fd);
                    epoll_ctl(epoll_fd, EPOLL_CTL_DEL, client->fd, NULL);
                    close(client->fd);
                    free(client);
                } else {
                    // 处理请求
                    async_handle_request(client);
                }
            }
        }
    }
    close(server_fd);
#ifdef CONFIG_IPV6_PORTAL
    close(server_fd_v6);
#endif
    close(epoll_fd);
    return NULL;
}

#ifdef CONFIG_DNS_REDIRECT
/**
 * IPv6 UDP DNS 服务器线程处理函数
 */
void* ipv6_udp_dns_server_thread(void* arg)
{
    int udp_fd;
    struct sockaddr_in6 server_addr, client_addr;
    socklen_t client_len = sizeof(client_addr);
    char buffer[IPV6_UDP_BUFFER_SIZE];
    int recv_len;

    qrzl_log("Starting IPv6 UDP DNS server thread...");

    // 创建IPv6 UDP套接字
    if ((udp_fd = socket(AF_INET6, SOCK_DGRAM, 0)) < 0) {
        qrzl_err("IPv6 UDP socket creation failed");
        return NULL;
    }

    // 设置套接字选项
    int optval = 1;
    setsockopt(udp_fd, SOL_SOCKET, SO_REUSEADDR, &optval, sizeof(optval));

    // 绑定到IPv6地址和端口9053
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin6_family = AF_INET6;
    server_addr.sin6_addr = in6addr_any; // 监听所有IPv6地址
    server_addr.sin6_port = htons(DNS_SERVER_PORT);

    if (bind(udp_fd, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
        qrzl_err("IPv6 UDP bind failed");
        close(udp_fd);
        return NULL;
    }

    qrzl_log("IPv6 UDP DNS server listening on port %d", DNS_SERVER_PORT);

    // 主循环：接收和处理DNS请求
    while (1) {
        recv_len = recvfrom(udp_fd, buffer, IPV6_UDP_BUFFER_SIZE - 1, 0,
                           (struct sockaddr*)&client_addr, &client_len);

        if (recv_len > 0) {
            buffer[recv_len] = '\0';

            char client_ip_str[INET6_ADDRSTRLEN];
            inet_ntop(AF_INET6, &client_addr.sin6_addr, client_ip_str, INET6_ADDRSTRLEN);
            qrzl_log("Received IPv6 UDP DNS request from %s, length: %d", client_ip_str, recv_len);

            // 处理DNS请求
            handle_ipv6_dns_request(udp_fd, &client_addr, buffer, recv_len);
        } else if (recv_len < 0) {
            qrzl_err("IPv6 UDP recvfrom error: %s", strerror(errno));
            break;
        }
    }

    close(udp_fd);
    return NULL;
}

/**
 * 处理IPv6 DNS请求
 */
void handle_ipv6_dns_request(int udp_fd, struct sockaddr_in6* client_addr, char* buffer, int len)
{
    char client_ip_str[INET6_ADDRSTRLEN];
    inet_ntop(AF_INET6, &client_addr->sin6_addr, client_ip_str, INET6_ADDRSTRLEN);

    qrzl_log("Processing IPv6 DNS request from %s", client_ip_str);

    // 检查客户端MAC是否已认证
    int is_authenticated = check_mac_authentication_from_ipv6(&client_addr->sin6_addr);

    // 解析DNS请求类型
    int dns_type = get_dns_query_type(buffer, len);

    if (!is_authenticated) {
        qrzl_log("Client %s not authenticated, sending captive portal response", client_ip_str);
        // 发送重定向到认证页面的DNS响应
        send_captive_portal_dns_response(udp_fd, client_addr, buffer, len, dns_type);
    } else {
        qrzl_log("Client %s is authenticated, allowing DNS request", client_ip_str);
        // 已认证的客户端，可以转发DNS请求或返回正常响应
        // 这里简化处理，实际应该转发到真实DNS服务器
    }
}

/**
 * 从IPv6地址检查MAC认证状态
 * 先根据/proc/cjportal/auth_ipv6查询出ipv6对应的mac，再用该mac在/proc/cjportal/auth中检查是否已认证
 */
int check_mac_authentication_from_ipv6(struct in6_addr* client_ipv6)
{
    char client_ipv6_str[INET6_ADDRSTRLEN];
    inet_ntop(AF_INET6, client_ipv6, client_ipv6_str, INET6_ADDRSTRLEN);

    qrzl_log("Checking authentication for IPv6: %s", client_ipv6_str);

    // 第一步：从/proc/cjportal/auth_ipv6中查找IPv6对应的MAC地址
    FILE* ipv6_file = fopen("/proc/cjportal/auth_ipv6", "r");
    if (!ipv6_file) {
        qrzl_log("Cannot open /proc/cjportal/auth_ipv6, assuming not authenticated");
        return 0;
    }

    char line[512];
    char found_mac[32] = {0};
    int mac_found = 0;

    while (fgets(line, sizeof(line), ipv6_file)) {
        char ipv6_addr[INET6_ADDRSTRLEN];
        char mac_addr[32];

        // 解析格式：IPv6地址 MAC地址
        if (sscanf(line, "%s %s", ipv6_addr, mac_addr) == 2) {
            if (strcmp(ipv6_addr, client_ipv6_str) == 0) {
                strcpy(found_mac, mac_addr);
                mac_found = 1;
                qrzl_log("Found MAC %s for IPv6 %s", found_mac, client_ipv6_str);
                break;
            }
        }
    }
    fclose(ipv6_file);

    if (!mac_found) {
        qrzl_log("No MAC found for IPv6 %s", client_ipv6_str);
        return 0;
    }

    // 第二步：检查找到的MAC是否在/proc/cjportal/auth认证列表中
    FILE* auth_file = fopen(PROC_AUTH_PATH, "r");
    if (!auth_file) {
        qrzl_log("Cannot open %s, assuming not authenticated", PROC_AUTH_PATH);
        return 0;
    }

    int authenticated = 0;
    while (fgets(line, sizeof(line), auth_file)) {
        // 检查行中是否包含找到的MAC地址
        if (strstr(line, found_mac) != NULL) {
            authenticated = 1;
            qrzl_log("MAC %s is authenticated", found_mac);
            break;
        }
    }

    fclose(auth_file);

    if (!authenticated) {
        qrzl_log("MAC %s is NOT authenticated", found_mac);
    }

    return authenticated;
}

/**
 * 获取DNS查询类型
 */
int get_dns_query_type(char* buffer, int len)
{
    if (len < 12) return 1; // 默认返回A记录类型

    // 跳过DNS头部(12字节)和查询名称
    int pos = 12;

    // 跳过查询名称（以0结尾的标签序列）
    while (pos < len && buffer[pos] != 0) {
        int label_len = (unsigned char)buffer[pos];
        if (label_len == 0) break;
        pos += label_len + 1;
    }
    pos++; // 跳过结尾的0

    // 读取查询类型（2字节，网络字节序）
    if (pos + 1 < len) {
        int query_type = (((unsigned char)buffer[pos]) << 8) | ((unsigned char)buffer[pos + 1]);
        return query_type;
    }

    return 1; // 默认A记录
}

/**
 * 发送重定向到认证页面的DNS响应
 * 根据DNS记录类型返回不同的IP地址：T_A返回IPv4地址，T_AAAA返回IPv6地址
 */
void send_captive_portal_dns_response(int udp_fd, struct sockaddr_in6* client_addr, char* original_request, int request_len, int dns_type)
{
    char response[IPV6_UDP_BUFFER_SIZE];
    int response_len = 0;

    // 获取配置的IP地址
    char lan_ipaddr[64] = {0};
    char ipv6_lan_ipaddr[128] = {0};

    cfg_get_item("lan_ipaddr", lan_ipaddr, sizeof(lan_ipaddr));
    cfg_get_item("ipv6_lan_ipaddr", ipv6_lan_ipaddr, sizeof(ipv6_lan_ipaddr));

    qrzl_log("DNS query type: %d (1=A, 28=AAAA), lan_ipaddr: %s, ipv6_lan_ipaddr: %s",
             dns_type, lan_ipaddr, ipv6_lan_ipaddr);

    // 复制DNS请求头
    if (request_len >= 12) { // DNS头部至少12字节
        memcpy(response, original_request, 12);

        // 设置响应标志
        response[2] |= 0x80; // QR=1 (响应)
        response[3] |= 0x80; // RA=1 (递归可用)

        // 设置回答数量为1
        response[6] = 0;
        response[7] = 1;

        response_len = 12;

        // 复制查询部分
        if (request_len > 12) {
            int query_len = request_len - 12;
            memcpy(response + response_len, original_request + 12, query_len);
            response_len += query_len;
        }

        // 添加回答部分 - 指向查询名称
        response[response_len++] = 0xc0; // 压缩指针
        response[response_len++] = 0x0c; // 指向偏移12（查询名称开始）

        if (dns_type == 28) { // T_AAAA记录，返回IPv6地址
            // 类型 AAAA (IPv6地址)
            response[response_len++] = 0x00;
            response[response_len++] = 0x1c; // 28 = 0x1c

            // 类别 IN
            response[response_len++] = 0x00;
            response[response_len++] = 0x01;

            // TTL (300秒)
            response[response_len++] = 0x00;
            response[response_len++] = 0x00;
            response[response_len++] = 0x01;
            response[response_len++] = 0x2c;

            // 数据长度 (16字节IPv6地址)
            response[response_len++] = 0x00;
            response[response_len++] = 0x10;

            // 解析IPv6地址并填入响应
            struct in6_addr ipv6_addr;
            if (strlen(ipv6_lan_ipaddr) > 0 && inet_pton(AF_INET6, ipv6_lan_ipaddr, &ipv6_addr) == 1) {
                memcpy(response + response_len, &ipv6_addr, 16);
                response_len += 16;
                qrzl_log("Returning IPv6 address: %s", ipv6_lan_ipaddr);
            } else {
                // 默认IPv6地址 fe80::1
                memset(response + response_len, 0, 16);
                response[response_len] = 0xfe;
                response[response_len + 1] = 0x80;
                response[response_len + 15] = 0x01;
                response_len += 16;
                qrzl_log("Returning default IPv6 address: fe80::1");
            }
        } else { // T_A记录，返回IPv4地址
            // 类型 A (IPv4地址)
            response[response_len++] = 0x00;
            response[response_len++] = 0x01;

            // 类别 IN
            response[response_len++] = 0x00;
            response[response_len++] = 0x01;

            // TTL (300秒)
            response[response_len++] = 0x00;
            response[response_len++] = 0x00;
            response[response_len++] = 0x01;
            response[response_len++] = 0x2c;

            // 数据长度 (4字节IPv4地址)
            response[response_len++] = 0x00;
            response[response_len++] = 0x04;

            // 解析IPv4地址并填入响应
            struct in_addr ipv4_addr;
            if (strlen(lan_ipaddr) > 0 && inet_pton(AF_INET, lan_ipaddr, &ipv4_addr) == 1) {
                memcpy(response + response_len, &ipv4_addr, 4);
                response_len += 4;
                qrzl_log("Returning IPv4 address: %s", lan_ipaddr);
            } else {
                // 默认IPv4地址 *************
                response[response_len++] = 192;
                response[response_len++] = 168;
                response[response_len++] = 100;
                response[response_len++] = 1;
                qrzl_log("Returning default IPv4 address: *************");
            }
        }
    }
}
#endif

/**
 * 创建一个线程启动http server 监听9000 端口
 */
void start_captive_portal_server()
{
	// 启动Captive Portal服务器线程
    pthread_t captive_portal_tid;
    int err = pthread_create(&captive_portal_tid, NULL, async_http_server_thread_handler, NULL);
    if (err != 0) {
        qrzl_err("创建Captive Portal线程失败, error code: %d", err);
    }

#ifdef CONFIG_DNS_REDIRECT
    // 启动IPv6 UDP DNS服务器线程
    pthread_t ipv6_udp_tid;
    err = pthread_create(&ipv6_udp_tid, NULL, ipv6_udp_dns_server_thread, NULL);
    if (err != 0) {
        qrzl_err("创建IPv6 UDP DNS服务器线程失败, error code: %d", err);
    } else {
        qrzl_log("IPv6 UDP DNS服务器线程创建成功");
    }
#endif
}
#endif