
include $(ZTE_PS_LINK_SCRIPT)
-include $(OUTPUT_DIR)/sys_type.mk
-include $(LINUX_DIR)/.config

.PHONY: help all allclean ps psclean kernel kernelclean app appclean lib libclean check

help: check
	@echo "=========================Make help start========================="
	@echo "make [help]           this help"
	@echo "make all[clean]       compile all, include recovery and normal system"
	@echo "make userdata         make userdata for normal system"
	@echo "make normal[clean]    compile normal system,include kernel uClibc lib and app"
	@echo "make normal2          continue to compile normal system"
	@echo "make normal_rootfs    make rootfs for normal system"
	@echo "make normal_copybin   copy bins to project for normal system"
	@echo "make recovery[clean]  compile recovery system,include kernel uClibc lib and app"
	@echo "make recovery2        continue to compile recovery system"
	@echo "make recovery_rootfs  make rootfs for recovery system"
	@echo "make recovery_copybin copy bins to project for recovery system"
	@echo "make kernel[clean]    compile linux kernel"
	@echo "make rootfs           make rootfs for normal or recovery automatically based on config"
	@echo "make copybin          copy bins to project for normal or recovery automatically based on config"
	@echo "make lib[clean]       compile lib"
	@echo "make libc[clean]      compile uClibc"
	@echo "make app[clean]       compile app"
ifeq ($(CONFIG_SINGLECORE),yes)
	@echo "make ps[clean]        compile libps"
endif
	@echo "=========================Make help end========================="

check:
ifeq ($(PRJ_NAME),)
	$(error ONLY execute the make command in the project/CHIP_NAME/prj_xxx/build directory)
endif
	bash $(BUILD_DIR)/env_check.sh

normal_check:
ifneq ($(AP_BUILD_TYPE),normal)
	$(error current config is not normal,make normal first)
endif

recovery_check:
ifneq ($(AP_BUILD_TYPE),recovery)
	$(error current config is not recovery,make recovery first)
endif

copy:
	@bash $(BUILD_DIR)/modcopy.sh

all: 
ifeq ($(USE_FOTA),yes)
ifneq ($(USE_FOTA_AB),yes)
	make recovery
endif
endif
ifeq ($(BUILD_CAP),yes)
	make cap
endif
	make normal

allclean: normalclean

sys: check
ifeq ($(CONFIG_SINGLECORE),yes)
ifeq ($(AP_BUILD_TYPE),normal)
	make ps
endif
endif
	make kernel
ifeq ($(CONFIG_SINGLECORE),yes)
ifeq ($(AP_BUILD_TYPE),normal)
ifeq ($(USE_CPPS_KO),yes)
	make cpko
endif
endif
endif
	make libc
	make lib
	make app
	@echo "all modules build done"

sysclean: check
ifeq ($(CONFIG_SINGLECORE),yes)
ifeq ($(AP_BUILD_TYPE),normal)
	make psclean
endif
endif
	make kerneldistclean
ifeq ($(CONFIG_SINGLECORE),yes)
ifeq ($(AP_BUILD_TYPE),normal)
ifeq ($(USE_CPPS_KO),yes)
	make cpkoclean
endif
endif
endif
	make libcclean
	-make libclean
	-make appclean
	-@rm -fr $(OUTPUT_DIR) $(STAGEDIR)
	@echo "all modules clean done"

ps:
	mkdir -p $(LIBPS_PATH)
ifeq ($(CONFIG_SINGLECORE),yes)
	make -j1 ARCH=arm CROSS_COMPILE=$(CROSS_COMPILE) -C $(LINUX_DIR) prepare
ifneq ($(MK_SDK_VERSION),yes)
	(cd $(PS_BUILD_DIR) && bash $(LIBPS_BUILD_SHELL) all)
else
	(cd $(PS_BUILD_DIR) && bash $(LIBPS_BUILD_SHELL) sdk)
endif
else
	$(error libps in NOT on AP)
endif

psclean:
ifeq ($(CONFIG_SINGLECORE),yes)
ifneq ($(MK_SDK_VERSION),yes)
	(cd $(PS_BUILD_DIR) && bash $(LIBPS_BUILD_SHELL) allclean)
	-rm -fr $(LIBPS_PATH)/*
else
	(cd $(PS_BUILD_DIR) && bash $(LIBPS_BUILD_SHELL) sdkclean)
endif
else
	$(error libps in NOT on AP)
endif

kernel:
	-rm -v  $(LINUX_DIR)/arch/arm/boot/*Image
	-rm -v  $(LINUX_DIR)/vmlinux*
	#make -j1 ARCH=arm KBUILD_VERBOSE=1 CROSS_COMPILE=$(CROSS_COMPILE) -C $(LINUX_DIR) all
	make -j4 ARCH=arm CROSS_COMPILE=$(CROSS_COMPILE) -C $(LINUX_DIR) all
	make -j1 ARCH=arm CROSS_COMPILE=$(CROSS_COMPILE) -C $(LINUX_DIR) uImage
ifeq ($(CONFIG_MODULES), y)
	make -j1 ARCH=arm CROSS_COMPILE=$(CROSS_COMPILE) -C $(LINUX_DIR) modules
	make -j1 ARCH=arm -C $(LINUX_DIR) INSTALL_MOD_PATH=$(ROOTFS_DIR) modules_install
endif
	make -j1 ARCH=arm -C $(LINUX_DIR) INSTALL_HDR_PATH=$(STAGEDIR) headers_install
	cp -v $(LINUX_DIR)/include/generated/autoconf.h  $(LINUX_DIR)/include/linux/
	-cp -v $(LINUX_DIR)/include/generated/autoconf.h  $(LINUX_DIR)/usr/include/linux/
	mkdir -p $(IMAGE_DIR)
	cp -v $(LINUX_DIR)/arch/arm/boot/uImage  $(IMAGE_DIR)/linux_kernel.img
	cp -v $(LINUX_DIR)/vmlinux                $(IMAGE_DIR)/
	cp -v $(LINUX_DIR)/System.map             $(IMAGE_DIR)/
	@echo "kernel build done"

kernelclean:
	make ARCH=arm CROSS_COMPILE=$(CROSS_COMPILE) -C $(LINUX_DIR) clean
	@echo "kernel clean done"

kernel2:
	make kernel
ifeq ($(CONFIG_SINGLECORE),yes)
ifeq ($(AP_BUILD_TYPE),normal)
ifeq ($(USE_CPPS_KO),yes)
	make cpkoclean cpko
endif
endif
endif
	make rootfs
	make copybin

kerneldistclean:
	make ARCH=arm CROSS_COMPILE=$(CROSS_COMPILE) -C $(LINUX_DIR) distclean
	-rm -f $(LINUX_DIR)/include/linux/autoconf.h

kernel_menuconfig:
	make ARCH=arm -C $(LINUX_DIR) menuconfig

kernellink:
	-rm -v  $(LINUX_DIR)/arch/arm/boot/*Image
	-rm -v  $(LINUX_DIR)/vmlinux*
	make -j1 ARCH=arm CROSS_COMPILE=$(CROSS_COMPILE) -C $(LINUX_DIR) uImage
	cp -v $(LINUX_DIR)/arch/arm/boot/uImage  $(IMAGE_DIR)/linux_kernel.img
ifeq ($(CONFIG_SIGN_IMAGE),yes)
	chmod a+x $(SIGN)
	@$(SIGN) -s $(IMAGE_DIR)/linux_kernel.img $(PRJ_BIN_DIR)/allbins/ap_cpuap.bin $(PRIVATE_KEY_PATH)
else
	@cp -v $(IMAGE_DIR)/linux_kernel.img      $(PRJ_BIN_DIR)/allbins/ap_cpuap.bin
endif

busybox_menuconfig:
ifeq ($(AP_BUILD_TYPE),normal)
	make -C $(APP_DIR)/busybox/src O=$(APP_DIR)/busybox/build menuconfig
else
	make -C $(APP_DIR)/busybox/src O=$(APP_DIR)/busybox_recovery/build menuconfig
endif

cpko:
	(cd $(LINUX_DIR) && $(CC) -Wp,-MD,drivers/cpko/.cpko_main.o.d  -nostdinc -isystem $(CROSS_COMPILE_INCLUDE_PATH) -I$(LINUX_DIR)/arch/arm/include -Iarch/arm/include/generated -Iinclude  -include $(LINUX_DIR)/include/linux/kconfig.h -D__KERNEL__ -DDDR_BASE_ADDR_LINUX_VA -mlittle-endian -Iarch/arm/mach-zx297520v3/include -Wall -Wundef -Wstrict-prototypes -Wno-trigraphs -fno-strict-aliasing -fno-common -Werror-implicit-function-declaration -Wno-format-security -fno-delete-null-pointer-checks -O2 -DCONFIG_SINGLECORE -DUSE_CPPS_KO -mlong-calls -marm -fno-dwarf2-cfi-asm -mabi=aapcs-linux -mno-thumb-interwork -funwind-tables $(CPKO_EXT_CFLAGS) -D__LINUX_ARM_ARCH__=7 -march=armv7-a -msoft-float -Uarm -Wframe-larger-than=1024 -fno-stack-protector -Wno-unused-but-set-variable -fomit-frame-pointer -fno-var-tracking-assignments -g -fno-inline-functions-called-once -Wdeclaration-after-statement -Wno-pointer-sign -fno-strict-overflow -fconserve-stack -DCC_HAVE_ASM_GOTO  -DMODULE  -DCPKO -D"KBUILD_STR(s)=\#s" -D"KBUILD_BASENAME=KBUILD_STR(cpko_main)"  -D"KBUILD_MODNAME=KBUILD_STR(cpko)" -c -o drivers/cpko/cpko_main.o drivers/cpko/cpko_main.c)
	#(cd $(LINUX_DIR) && $(LD) -EL    -r -o drivers/cpko/cpko.o drivers/cpko/cpko_main.o --whole-archive $(libps_libs) --no-whole-archive; scripts/mod/modpost drivers/cpko/cpko.o)
ifeq ($(USE_ENABLE_LTO),yes)
	(cd $(LINUX_DIR) && $(LD) -EL -r -o drivers/cpko/cpko.o drivers/cpko/cpko_main.o; scripts/mod/modpost drivers/cpko/cpko.o)
else
	(cd $(LINUX_DIR) && $(LD) -EL    -r -T $(LINUX_DIR)/scripts/module-cpko.lds -o drivers/cpko/cpko.o drivers/cpko/cpko_main.o --start-group $(libps_libs) --end-group; scripts/mod/modpost drivers/cpko/cpko.o)
endif
	(cd $(LINUX_DIR) && $(CC) -Wp,-MD,drivers/cpko/.cpko.mod.o.d  -nostdinc -isystem $(CROSS_COMPILE_INCLUDE_PATH) -I$(LINUX_DIR)/arch/arm/include -Iarch/arm/include/generated -Iinclude  -include $(LINUX_DIR)/include/linux/kconfig.h -D__KERNEL__ -DDDR_BASE_ADDR_LINUX_VA -mlittle-endian -Iarch/arm/mach-zx297520v3/include -Wall -Wundef -Wstrict-prototypes -Wno-trigraphs -fno-strict-aliasing -fno-common -Werror-implicit-function-declaration -Wno-format-security -fno-delete-null-pointer-checks -O2 -DCONFIG_SINGLECORE -DUSE_CPPS_KO -mlong-calls -marm  -fno-dwarf2-cfi-asm -mabi=aapcs-linux -mno-thumb-interwork -funwind-tables $(CPKO_EXT_CFLAGS) -D__LINUX_ARM_ARCH__=7 -march=armv7-a -msoft-float -Uarm -Wframe-larger-than=1024 -fno-stack-protector -Wno-unused-but-set-variable -fomit-frame-pointer -fno-var-tracking-assignments -g -fno-inline-functions-called-once -Wdeclaration-after-statement -Wno-pointer-sign -fno-strict-overflow -fconserve-stack -DCC_HAVE_ASM_GOTO  -D"KBUILD_STR(s)=#s" -D"KBUILD_BASENAME=KBUILD_STR(cpko.mod)"  -D"KBUILD_MODNAME=KBUILD_STR(cpko)" -DMODULE  -c -o drivers/cpko/cpko.mod.o drivers/cpko/cpko.mod.c)
ifeq ($(USE_ENABLE_LTO),yes)
	(cd $(LINUX_DIR) && $(CC) -Wall -Wundef -Wstrict-prototypes -Wno-trigraphs -fno-strict-aliasing -fno-common -Werror-implicit-function-declaration -Wno-format-security -fno-delete-null-pointer-checks -Os -DCONFIG_SINGLECORE -DUSE_CPPS_KO -mlong-calls -marm  -fno-dwarf2-cfi-asm -mabi=aapcs-linux -mno-thumb-interwork -funwind-tables -mthumb -Wa,-mimplicit-it=always -Wa,-mno-warn-deprecated -D__LINUX_ARM_ARCH__=7 -march=armv7-a -msoft-float -Uarm -Wframe-larger-than=1024 -fno-stack-protector -Wno-unused-but-set-variable -fomit-frame-pointer -fno-var-tracking-assignments -g -fno-inline-functions-called-once -Wdeclaration-after-statement -Wno-pointer-sign -fno-strict-overflow -fconserve-stack -DCC_HAVE_ASM_GOTO $(LTO_CFLAGS) -nostdlib $(LTO_FINAL_CFLAGS) -Wl,-r -Wl,-T $(LINUX_DIR)/scripts/module-common.lds -Wl,--build-id -o drivers/cpko/cpko.ko drivers/cpko/cpko.o drivers/cpko/cpko.mod.o -Wl,--whole-archive $(libps_libs_lto) -Wl,--no-whole-archive -Wl,--start-group $(libps_libs_nolto) -Wl,--end-group)
else
#	(cd $(LINUX_DIR) && $(LD) -EL -r -T $(LINUX_DIR)/scripts/module-common.lds --build-id  -o drivers/cpko/cpko.ko drivers/cpko/cpko.o drivers/cpko/cpko.mod.o)
	(cd $(LINUX_DIR) && $(LD) -EL -r --build-id  -o drivers/cpko/cpko.ko drivers/cpko/cpko.o drivers/cpko/cpko.mod.o)
endif
	#(cd $(LINUX_DIR) && scripts/mod/modpost -m -a -o $(LINUX_DIR)/Module.symvers      -c -s  vmlinux drivers/cpko/cpko.ko)
	mkdir -p $(ROOTFS_DIR)/lib/cpko
	$(STRIPTOOL) --strip-debug --strip-unneeded -R .comment -R .ARM.attributes -R .ARM.exidx -R .ARM.extab -R .ARM.extab.ps_static_func -R .ARM.exidx.ps_static_func -R .ARM.extab.ps_comm_func -R .ARM.exidx.ps_comm_func -R  .ARM.extab.ps_4g_func -R .ARM.exidx.ps_4g_func -R  .ARM.extab.ps_3g_func -R .ARM.exidx.ps_3g_func -R  .ARM.extab.ps_2g_func -R .ARM.exidx.ps_2g_func $(LINUX_DIR)/drivers/cpko/cpko.ko -o $(LINUX_DIR)/drivers/cpko/cpko_stripped.ko
	ko_strip $(LINUX_DIR)/drivers/cpko/cpko_stripped.ko $(LINUX_DIR)/System.map $(ROOTFS_DIR)/lib/cpko/cpko_tmp.ko $(ROOTFS_DIR)/lib/cpko/cpko_secinfo.bin > $(OUTPUT_DIR)/ko_strip.txt 2>&1  \
	   && echo "ko_strip success." || { echo "ko_strip failure!"; cat $(OUTPUT_DIR)/ko_strip.txt; exit 1; }
	$(STRIPTOOL) -R .symtab  -R .strtab  $(ROOTFS_DIR)/lib/cpko/cpko_tmp.ko -o $(ROOTFS_DIR)/lib/cpko/cpko.ko
	rm $(ROOTFS_DIR)/lib/cpko/cpko_tmp.ko
	chmod a+x $(ROOTFS_DIR)/lib/cpko/*

ifeq ($(USE_DL_CP),no)
	(cd $(LINUX_DIR) && $(CC) -Wp,-MD,drivers/loadcp/.load_cpko.o.d  -nostdinc -isystem $(CROSS_COMPILE_INCLUDE_PATH) -I$(LINUX_DIR)/arch/arm/include -Iarch/arm/include/generated -Iinclude  -include $(LINUX_DIR)/include/linux/kconfig.h -D__KERNEL__ -mlittle-endian -Iarch/arm/mach-zx297520v3/include -Wall -Wundef -Wstrict-prototypes -Wno-trigraphs -fno-strict-aliasing -fno-common -Werror-implicit-function-declaration -Wno-format-security -fno-delete-null-pointer-checks -O2 -DCONFIG_SINGLECORE -DUSE_CPPS_KO -mlong-calls -marm -fno-dwarf2-cfi-asm -mabi=aapcs-linux -mno-thumb-interwork -funwind-tables -D__LINUX_ARM_ARCH__=7 -march=armv7-a -msoft-float -Uarm -Wframe-larger-than=1024 -fno-stack-protector -Wno-unused-but-set-variable -fomit-frame-pointer -fno-var-tracking-assignments -g -fno-inline-functions-called-once -Wdeclaration-after-statement -Wno-pointer-sign -fno-strict-overflow -fconserve-stack -DCC_HAVE_ASM_GOTO  -DMODULE  -DLOADCP -D"KBUILD_STR(s)=\#s" -D"KBUILD_BASENAME=KBUILD_STR(load_cpko)"  -D"KBUILD_MODNAME=KBUILD_STR(loadcp)" -c -o drivers/loadcp/load_cpko.o drivers/loadcp/load_cpko.c)
	(cd $(LINUX_DIR) && $(LD) -EL  -r -o drivers/loadcp/loadcp.o drivers/loadcp/load_cpko.o; scripts/mod/modpost drivers/loadcp/loadcp.o)
	(cd $(LINUX_DIR) && $(CC) -Wp,-MD,drivers/loadcp/.loadcp.mod.o.d  -nostdinc -isystem $(CROSS_COMPILE_INCLUDE_PATH) -I$(LINUX_DIR)/arch/arm/include -Iarch/arm/include/generated -Iinclude  -include $(LINUX_DIR)/include/linux/kconfig.h -D__KERNEL__ -mlittle-endian -Iarch/arm/mach-zx297520v3/include -Wall -Wundef -Wstrict-prototypes -Wno-trigraphs -fno-strict-aliasing -fno-common -Werror-implicit-function-declaration -Wno-format-security -fno-delete-null-pointer-checks -O2 -DCONFIG_SINGLECORE -DUSE_CPPS_KO -mlong-calls -marm  -fno-dwarf2-cfi-asm -mabi=aapcs-linux -mno-thumb-interwork -funwind-tables -D__LINUX_ARM_ARCH__=7 -march=armv7-a -msoft-float -Uarm -Wframe-larger-than=1024 -fno-stack-protector -Wno-unused-but-set-variable -fomit-frame-pointer -fno-var-tracking-assignments -g -fno-inline-functions-called-once -Wdeclaration-after-statement -Wno-pointer-sign -fno-strict-overflow -fconserve-stack -DCC_HAVE_ASM_GOTO  -D"KBUILD_STR(s)=#s" -D"KBUILD_BASENAME=KBUILD_STR(loadcp.mod)"  -D"KBUILD_MODNAME=KBUILD_STR(loadcp)" -DMODULE  -c -o drivers/loadcp/loadcp.mod.o drivers/loadcp/loadcp.mod.c)
	(cd $(LINUX_DIR) && $(LD) -EL -r --build-id  -o drivers/loadcp/loadcp.ko drivers/loadcp/loadcp.o drivers/loadcp/loadcp.mod.o)
	#(cd $(LINUX_DIR) && scripts/mod/modpost -m -a -o $(LINUX_DIR)/Module.symvers      -c -s  vmlinux drivers/loadcp/loadcp.ko)
	$(STRIPTOOL) --strip-debug --strip-unneeded -R .comment -R .ARM.attributes  $(LINUX_DIR)/drivers/loadcp/loadcp.ko -o $(LINUX_DIR)/drivers/loadcp/loadcp_stripped.ko
	mkdir -p $(ROOTFS_DIR)/lib/loadcp
	cp -v $(LINUX_DIR)/drivers/loadcp/loadcp_stripped.ko $(ROOTFS_DIR)/lib/loadcp/loadcp.ko > $(OUTPUT_DIR)/ko_strip.txt
	chmod a+x $(ROOTFS_DIR)/lib/loadcp/*.ko
	rm -f $(ROOTFS_DIR)/lib/cpko/cpko.ko
	chmod a+x $(ROOTFS_DIR)/lib/cpko/*.bin
endif
ifeq ($(KO_SPLIT_ENABLE),yes)
	rm -fv $(ROOTFS_DIR)/lib/cpko/*.lzma
	(cd $(ROOTFS_DIR)/lib/cpko && split -b $(SPLIT_BLOCK_SIZE) cpko.ko  -d -a 2 cpko_)
	(cd $(ROOTFS_DIR)/lib/cpko && ls -1 cpko_?? | xargs -I {} minilzma -t $(LZMA_DICT)  {})
	rm -fv $(ROOTFS_DIR)/lib/cpko/cpko.ko
endif

cpkoclean:
	make V=1 -j1 ARCH=arm CROSS_COMPILE=$(CROSS_COMPILE) -C $(LINUX_DIR) M=drivers/cpko clean

app:
	mkdir -p $(ROOTFS_DIR)/bin $(ROOTFS_DIR)/sbin $(ROOTFS_DIR)/lib $(ROOTFS_DIR)/recovery/bin
	make -C app

appclean:
	make -C app clean

lib:
	mkdir -p $(ROOTFS_DIR)/bin $(ROOTFS_DIR)/sbin $(ROOTFS_DIR)/lib $(ROOTFS_DIR)/recovery/bin
	make -C lib

libclean:
	make -C lib clean

libc:
	make -C $(LIBC_DIR)
	make -C $(LIBC_DIR) PREFIX=$(STAGEDIR)/uClibc install

libcclean:
	make -C $(LIBC_DIR) clean

normal_conf:
	@cp -v $(PRJ_CONF_DIR)/normal/config.linux $(LINUX_DIR)/arch/arm/configs/zte_defconfig
	make ARCH=arm CROSS_COMPILE=$(CROSS_COMPILE) -C $(LINUX_DIR) zte_defconfig
	$(BUILD_DIR)/config_check.sh $(PRJ_CONF_DIR)/normal/config.linux $(LINUX_DIR)/.config
	@cp -v $(PRJ_CONF_DIR)/normal/config.busybox $(APP_DIR)/busybox/src/configs/zte_defconfig
	@cp -v $(PRJ_CONF_DIR)/normal/config.busybox_recovery $(APP_DIR)/busybox/src/configs/zte_recovery_defconfig
	@cp -v $(PRJ_CONF_DIR)/normal/config.uClibc  $(BUILD_DIR)/uClibc/.config
	@cp -v $(PRJ_CONF_DIR)/normal/config_lib.mk  $(LIB_DIR)/config_lib.mk
	@cp -v $(PRJ_CONF_DIR)/normal/config_app.mk  $(APP_DIR)/config_app.mk
	mkdir -p $(OUTPUT_DIR)
	echo "AP_BUILD_TYPE = normal" > $(OUTPUT_DIR)/sys_type.mk
	echo "AP_BUILD_PRJ = $(PRJ_NAME)" >> $(OUTPUT_DIR)/sys_type.mk

cap_conf:
	@cp -v $(PRJ_CONF_DIR)/cap/config.linux $(LINUX_DIR)/arch/arm/configs/zte_defconfig
	make ARCH=arm CROSS_COMPILE=$(CROSS_COMPILE) -C $(LINUX_DIR) zte_defconfig
	$(BUILD_DIR)/config_check.sh $(PRJ_CONF_DIR)/cap/config.linux $(LINUX_DIR)/.config
	@cp -v $(PRJ_CONF_DIR)/cap/config.busybox $(APP_DIR)/busybox/src/configs/zte_defconfig
	@cp -v $(PRJ_CONF_DIR)/cap/config.uClibc  $(BUILD_DIR)/uClibc/.config
	@cp -v $(PRJ_CONF_DIR)/cap/config_lib.mk  $(LIB_DIR)/config_lib.mk
	@cp -v $(PRJ_CONF_DIR)/cap/config_app.mk  $(APP_DIR)/config_app.mk
	mkdir -p $(OUTPUT_DIR)
	echo "AP_BUILD_TYPE = cap" > $(OUTPUT_DIR)/sys_type.mk
	echo "AP_BUILD_PRJ = $(PRJ_NAME)" >> $(OUTPUT_DIR)/sys_type.mk

userdata:
	@echo "=========> Begin to Make userdata FS Image! <==================="
	rm -rf $(USERDATA_DIR)
	mkdir -p $(USERDATA_DIR) $(PRJ_BIN_DIR)/allbins
	mkdir -p $(USERDATA_DIR)/cache $(USERDATA_DIR)/etc_rw $(USERDATA_DIR)/var
	#@cp -afvp $(PRJ_PRODUCT_DIR)/fs/normal/userdata/*   $(USERDATA_DIR)
	#find $(USERDATA_DIR) -type d -name '.gitkeep' -print0 | xargs -0 rm -fr
ifeq ($(USERDATA_FS_TYPE),ubifs)
	bash $(BUILD_DIR)/ubifs.sh userdata $(USERDATA_SIZE) $(USERDATA_DIR) $(PRJ_BIN_DIR)/allbins/ap_userdata.img
else
ifeq ($(PRJ_IS_MIN),yes)

ifeq ($(QRZL_USERDATA_OTA),yes)
	# ap_userdata.img 会copy到rootfs/etc_ro/目录下再打包成 ap_rootfs.img 超过3M 导致刷不进去除非改分区表
	# ap_userdata.img 通过fs_check 挂载到/mnt/userdata
	# /mnt/userdata 只创建一个目录 从网络下载qrzl_app 到此目录 
	mkdir -p $(USERDATA_DIR)/qrzl_ota
	@cp -afvp $(PRJ_PRODUCT_DIR)/fs/normal/qrzl_ota/*   $(USERDATA_DIR)/qrzl_ota
endif
	bash $(BUILD_DIR)/jffs2_lzma.sh $(USERDATA_DIR) $(PRJ_BIN_DIR)/allbins/ap_userdata.img lzma
else
	bash $(BUILD_DIR)/jffs2_lzma.sh $(USERDATA_DIR) $(PRJ_BIN_DIR)/allbins/ap_userdata.img lzo
endif
endif

	#(cd $(USERDATA_DIR) && find . -type f -printf '"/%P" Regular 14 %04m:%4U:%4G\n' >  ../userdata_attr.txt)
	#(cd $(USERDATA_DIR) && find . -type l -printf '"/%P" SymLink 14 %04m:%4U:%4G\n' >> ../userdata_attr.txt)
	#(cd $(USERDATA_DIR) && find . -type d -printf '"/%P" Regular 14 %04m:%4U:%4G\n' >> ../userdata_attr.txt)
	#(cd $(USERDATA_DIR)/.. && tar -zcvf $(PRJ_BIN_DIR)/allbins/userdata.tgz userdata userdata_attr.txt)
	#@rm $(USERDATA_DIR)/../userdata_attr.txt
	@echo "=========> Make userdata FS Image End! <==================="

capuserdata:
	@echo "=========> Begin to Make cap userdata FS Image! <==================="
	rm -rf $(USERDATA_DIR) $(ROOTFS_DIR)
	mkdir -p $(USERDATA_DIR) $(PRJ_BIN_DIR)/allbins
	mkdir -p $(USERDATA_DIR)/cache $(USERDATA_DIR)/etc_rw $(USERDATA_DIR)/var $(CAP_ROOTFS_DIR)/etc_ro/
	ln -s $(CAP_ROOTFS_DIR) $(ROOTFS_DIR)
	#@cp -afvp $(PRJ_PRODUCT_DIR)/fs/normal/userdata/*   $(USERDATA_DIR)
	#find $(USERDATA_DIR) -type d -name '.gitkeep' -print0 | xargs -0 rm -fr
ifeq ($(USERDATA_FS_TYPE),ubifs)
	bash $(BUILD_DIR)/ubifs.sh userdata $(CAP_USERDATA_SIZE) $(USERDATA_DIR) $(PRJ_BIN_DIR)/allbins/ap_capuserdata.img
else
ifeq ($(PRJ_IS_MIN),yes)
	bash $(BUILD_DIR)/jffs2_lzma.sh $(USERDATA_DIR) $(PRJ_BIN_DIR)/allbins/ap_capuserdata.img lzma
else
	bash $(BUILD_DIR)/jffs2_lzma.sh $(USERDATA_DIR) $(PRJ_BIN_DIR)/allbins/ap_capuserdata.img lzo
endif
endif
	@echo "=========> Make cap userdata FS Image End! <==================="

resource:
ifneq ($(USE_RESOURCE),yes)
	$(error NO USE_RESOURCE)
endif
	@echo "=========> Begin to Make resource FS Image! <==================="
	rm -rf $(RESOURCE_DIR)
	mkdir -p $(RESOURCE_DIR) $(PRJ_BIN_DIR)/allbins
	@cp -afvp $(PRJ_PRODUCT_DIR)/fs/normal/resource/*   $(RESOURCE_DIR)
	find $(RESOURCE_DIR) -type d -name '.gitkeep' -print0 | xargs -0 rm -fr
	for bmpfile in $(LOGO_BMP_FILE); \
	do \
		bmp_to_565 $(RESOURCE_DIR)/$$bmpfile.bmp $(RESOURCE_DIR)/$$bmpfile.bin || exit $$?; \
		rm $(RESOURCE_DIR)/$$bmpfile.bmp || exit $$?; \
	done

ifeq ($(PRJ_IS_MIN),yes)
	bash $(BUILD_DIR)/jffs2_lzma.sh $(RESOURCE_DIR) $(PRJ_BIN_DIR)/allbins/ap_resource.img lzma
else
	bash $(BUILD_DIR)/jffs2_lzma.sh $(RESOURCE_DIR) $(PRJ_BIN_DIR)/allbins/ap_resource.img lzo
endif

	(cd $(RESOURCE_DIR) && find . -type f -printf '"/%P" Regular 14 %04m:%4U:%4G\n' >  ../resource_attr.txt)
	(cd $(RESOURCE_DIR) && find . -type l -printf '"/%P" SymLink 14 %04m:%4U:%4G\n' >> ../resource_attr.txt)
	(cd $(RESOURCE_DIR) && find . -type d -printf '"/%P" Regular 14 %04m:%4U:%4G\n' >> ../resource_attr.txt)
	(cd $(RESOURCE_DIR)/.. && tar -zcvf $(PRJ_BIN_DIR)/allbins/resource.tgz resource resource_attr.txt)
	@rm $(RESOURCE_DIR)/../resource_attr.txt
	@echo "=========> Make resource FS Image End! <==================="

oem_fs:
ifneq ($(USE_OEM_FS),yes)
	$(error NO USE_OEM_FS)
endif
	@echo "=========> Begin to Make oem FS Image! <==================="
	rm -rf $(OEM_FS_DIR)
	mkdir -p $(OEM_FS_DIR) $(PRJ_BIN_DIR)/allbins
	@cp -afvp $(PRJ_PRODUCT_DIR)/fs/normal/oem/*   $(OEM_FS_DIR)
	find $(OEM_FS_DIR) -type d -name '.gitkeep' -print0 | xargs -0 rm -fr
ifeq ($(PRJ_IS_MIN),yes)
	bash $(BUILD_DIR)/jffs2_lzma.sh $(OEM_FS_DIR) $(PRJ_BIN_DIR)/allbins/ap_oem.img lzma
else
	bash $(BUILD_DIR)/jffs2_lzma.sh $(OEM_FS_DIR) $(PRJ_BIN_DIR)/allbins/ap_oem.img lzo
endif
	@echo "=========> Make oem FS Image End! <==================="

normal:
	make AP_BUILD_TYPE=normal sysclean
ifneq ($(QRZL_USERDATA_OTA),yes)
	make userdata
endif
ifeq ($(USE_RESOURCE),yes)
	make resource
endif
ifeq ($(USE_OEM_FS),yes)
	make oem_fs
endif
	make normal_conf
	make AP_BUILD_TYPE=normal sysclean
	make normal_conf
	make AP_BUILD_TYPE=normal sys
	make AP_BUILD_TYPE=normal rootfs
ifeq ($(VERIFY_APP_IN_KERNEL),yes)
	bash $(BUILD_DIR)/gen_app_hash.sh  $(PRJ_CONF_DIR)/normal/verify_app_list.txt $(ROOTFS_DIR)  $(LINUX_DIR)
	make kernel
ifeq ($(CONFIG_SINGLECORE),yes)
ifeq ($(USE_CPPS_KO),yes)
	make cpko
endif
endif
endif
	make AP_BUILD_TYPE=normal copybin
ifeq ($(QRZL_USERDATA_OTA),yes)
	make userdata
endif

normal2:
	make AP_BUILD_TYPE=normal sys
	make AP_BUILD_TYPE=normal rootfs
ifeq ($(VERIFY_APP_IN_KERNEL),yes)
	bash $(BUILD_DIR)/gen_app_hash.sh  $(PRJ_CONF_DIR)/normal/verify_app_list.txt $(ROOTFS_DIR)  $(LINUX_DIR)
	make kernel
ifeq ($(CONFIG_SINGLECORE),yes)
ifeq ($(USE_CPPS_KO),yes)
	make cpko
endif
endif
endif
	make AP_BUILD_TYPE=normal copybin

normalclean:
	-make AP_BUILD_TYPE=normal sysclean
	make normal_conf
	make AP_BUILD_TYPE=normal sysclean

cap:
	make AP_BUILD_TYPE=cap sysclean
	make cap_conf
	make AP_BUILD_TYPE=cap sysclean
	make cap_conf
	make capuserdata
	make AP_BUILD_TYPE=cap sys
	make AP_BUILD_TYPE=cap rootfs
	make AP_BUILD_TYPE=cap copybin

cap2:
	make AP_BUILD_TYPE=cap sys
	make AP_BUILD_TYPE=cap rootfs
	make AP_BUILD_TYPE=cap copybin

capclean:
	make AP_BUILD_TYPE=cap sysclean
	make cap_conf
	make AP_BUILD_TYPE=cap sysclean

recovery_conf:
	@cp -v $(PRJ_CONF_DIR)/recovery/config.linux $(LINUX_DIR)/arch/arm/configs/zte_defconfig
	make ARCH=arm CROSS_COMPILE=$(CROSS_COMPILE) -C $(LINUX_DIR) zte_defconfig
	$(BUILD_DIR)/config_check.sh $(PRJ_CONF_DIR)/recovery/config.linux $(LINUX_DIR)/.config
	mkdir -p $(OUTPUT_DIR)
ifeq ($(USE_RECOVERYFS),yes)
	@cp -v $(PRJ_CONF_DIR)/recovery/config.busybox $(APP_DIR)/busybox/src/configs/zte_defconfig
	@cp -v $(PRJ_CONF_DIR)/recovery/config.uClibc  $(BUILD_DIR)/uClibc/.config
	@cp -v $(PRJ_CONF_DIR)/recovery/config_lib.mk  $(LIB_DIR)/config_lib.mk
	@cp -v $(PRJ_CONF_DIR)/recovery/config_app.mk  $(APP_DIR)/config_app.mk
	mkdir -p $(OUTPUT_DIR)/recoveryfs
	ln -s $(RECOVERYFS_DIR) $(ROOTFS_DIR)
endif
	echo "AP_BUILD_TYPE = recovery" > $(OUTPUT_DIR)/sys_type.mk
	echo "AP_BUILD_PRJ = $(PRJ_NAME)" >> $(OUTPUT_DIR)/sys_type.mk

recovery:
ifeq ($(USE_RECOVERYFS),yes)
	make AP_BUILD_TYPE=recovery sysclean
	make recovery_conf
	make AP_BUILD_TYPE=recovery sysclean
	make recovery_conf
	make AP_BUILD_TYPE=recovery sys
	make AP_BUILD_TYPE=recovery rootfs
else
	make kerneldistclean
	make recovery_conf
	make kerneldistclean
	make recovery_conf
	make kernel
endif
	make AP_BUILD_TYPE=recovery copybin

recovery2:
ifeq ($(USE_RECOVERYFS),yes)
	make AP_BUILD_TYPE=recovery sys
	make AP_BUILD_TYPE=recovery rootfs
else
	make kernel
endif
	make AP_BUILD_TYPE=recovery copybin

recoveryclean:
ifeq ($(USE_RECOVERYFS),yes)
	-make AP_BUILD_TYPE=recovery sysclean
	make recovery_conf
	make AP_BUILD_TYPE=recovery sysclean
else
	make kerneldistclean
	make recovery_conf
	make kerneldistclean
endif


rootfs:
ifeq ($(AP_BUILD_TYPE),)
	$(error AP_BUILD_TYPE is NULL)
endif
ifeq ($(AP_BUILD_TYPE),normal)
	make normal_rootfs
endif
ifeq ($(AP_BUILD_TYPE),cap)
	make cap_rootfs
endif
ifeq ($(USE_RECOVERYFS),yes)
ifeq ($(AP_BUILD_TYPE),recovery)
	make recovery_rootfs
endif
endif

copybin:
ifeq ($(AP_BUILD_TYPE),)
	$(error AP_BUILD_TYPE is NULL)
endif
ifeq ($(AP_BUILD_TYPE),normal)
	make normal_copybin
endif
ifeq ($(AP_BUILD_TYPE),recovery)
	make recovery_copybin
endif
ifeq ($(AP_BUILD_TYPE),cap)
	make cap_copybin
endif

normal_rootfs: normal_check
	mkdir -p $(PRJ_BIN_DIR)/allbins $(PRJ_BIN_DIR)/elfs
	rm -fr $(ROOTFS_DIR)/etc_rw
	cp -afvp $(PRJ_PRODUCT_DIR)/fs/normal/rootfs/*  $(ROOTFS_DIR)/
ifneq ($(QRZL_USERDATA_OTA),yes)
	cp -v $(PRJ_BIN_DIR)/allbins/ap_userdata.img  $(ROOTFS_DIR)/etc_ro/
endif
	@cd $(BUILD_DIR); bash ./install_libc.sh $(ROOTFS_DIR)
	@cd $(BUILD_DIR); bash ./fix_userdata_link.sh $(ROOTFS_DIR)
	@cd $(BUILD_DIR); bash ./check_file_mode.sh $(ROOTFS_DIR)
	find $(ROOTFS_DIR)/ -name '.gitkeep' -print0 | xargs -0 rm -fr
	find $(ROOTFS_DIR)/ -name '.git' -print0 | xargs -0 rm -fr
	find $(ROOTFS_DIR)/ -name '.gitignore' -print0 | xargs -0 rm -fr
	find $(ROOTFS_DIR)/ -name '.svn' -print0 | xargs -0 rm -fr
ifneq ($(USE_FOTA),yes)
	rm -fr $(ROOTFS_DIR)/recovery
else
ifeq ($(USE_FOTA_AB),yes)
	rm -fr $(ROOTFS_DIR)/recovery
endif
endif

ifeq ($(QRZL_USERDATA_OTA),yes)
	rm -fr $(ROOTFS_DIR)/bin/qrzl_app
endif

ifeq ($(ROOT_FS_TYPE),ubifs)
	bash $(BUILD_DIR)/ubifs.sh rootfs $(ROOTFS_SIZE) $(ROOTFS_DIR) $(IMAGE_DIR)/rootfs.img
else
ifeq ($(ROOT_FS_TYPE),jffs2)
ifeq ($(PRJ_IS_MIN),yes)
	bash $(BUILD_DIR)/jffs2_lzma.sh $(ROOTFS_DIR) $(IMAGE_DIR)/rootfs.img lzma
else
	bash $(BUILD_DIR)/jffs2_lzma.sh $(ROOTFS_DIR) $(IMAGE_DIR)/rootfs.img lzo
endif
else
ifeq ($(ROOT_FS_TYPE),squashfs)
	bash $(BUILD_DIR)/squashfs.sh $(ROOTFS_DIR) $(IMAGE_DIR)/rootfs.img
endif
endif
endif
	(cd $(ROOTFS_DIR) && find . -type f -printf '"/%P" Regular 14 %04m:%4U:%4G\n' >  ../rootfs_attr.txt)
	(cd $(ROOTFS_DIR) && find . -type l -printf '"/%P" SymLink 14 %04m:%4U:%4G\n' >> ../rootfs_attr.txt)
	(cd $(ROOTFS_DIR) && find . -type d -printf '"/%P" Regular 14 %04m:%4U:%4G\n' >> ../rootfs_attr.txt)
	(cd $(ROOTFS_DIR)/../ && tar -zcvf $(PRJ_BIN_DIR)/allbins/rootfs.tgz rootfs rootfs_attr.txt)
	@rm $(ROOTFS_DIR)/../rootfs_attr.txt

recovery_rootfs: recovery_check
	mkdir -p $(PRJ_BIN_DIR)/allbins $(PRJ_BIN_DIR)/elfs
	rm -fr $(RECOVERYFS_DIR)/etc_rw $(RECOVERYFS_DIR)/recovery
	cp -afvp $(PRJ_PRODUCT_DIR)/fs/recovery/rootfs/*  $(RECOVERYFS_DIR)/
	@cd $(BUILD_DIR); bash ./install_libc.sh $(RECOVERYFS_DIR)
	@cd $(BUILD_DIR); bash ./fix_userdata_link.sh $(RECOVERYFS_DIR)
	@cd $(BUILD_DIR); bash ./check_file_mode.sh $(RECOVERYFS_DIR)
	find $(RECOVERYFS_DIR)/ -name '.gitkeep' -print0 | xargs -0 rm -fr
	find $(RECOVERYFS_DIR)/ -name '.git' -print0 | xargs -0 rm -fr
	find $(RECOVERYFS_DIR)/ -name '.gitignore' -print0 | xargs -0 rm -fr
	find $(RECOVERYFS_DIR)/ -name '.svn' -print0 | xargs -0 rm -fr

ifeq ($(ROOT_FS_TYPE),ubifs)
	bash $(BUILD_DIR)/ubifs.sh recoveryfs $(RECOVERYFS_SIZE) $(RECOVERYFS_DIR) $(IMAGE_DIR)/recoveryfs.img
else
ifeq ($(ROOT_FS_TYPE),jffs2)
ifeq ($(PRJ_IS_MIN),yes)
	bash $(BUILD_DIR)/jffs2_lzma.sh $(RECOVERYFS_DIR) $(IMAGE_DIR)/recoveryfs.img lzma
else
	bash $(BUILD_DIR)/jffs2_lzma.sh $(RECOVERYFS_DIR) $(IMAGE_DIR)/recoveryfs.img lzo
endif
else
ifeq ($(ROOT_FS_TYPE),squashfs)
	bash $(BUILD_DIR)/squashfs.sh $(RECOVERYFS_DIR) $(IMAGE_DIR)/recoveryfs.img
endif
endif
endif
	(cd $(RECOVERYFS_DIR) && find . -type f -printf '"/%P" Regular 14 %04m:%4U:%4G\n' >  ../recoveryfs_attr.txt)
	(cd $(RECOVERYFS_DIR) && find . -type l -printf '"/%P" SymLink 14 %04m:%4U:%4G\n' >> ../recoveryfs_attr.txt)
	(cd $(RECOVERYFS_DIR) && find . -type d -printf '"/%P" Regular 14 %04m:%4U:%4G\n' >> ../recoveryfs_attr.txt)
	(cd $(RECOVERYFS_DIR)/../ && tar -zcvf $(PRJ_BIN_DIR)/allbins/recoveryfs.tgz recoveryfs recoveryfs_attr.txt)
	@rm $(RECOVERYFS_DIR)/../recoveryfs_attr.txt

cap_rootfs:
	mkdir -p $(PRJ_BIN_DIR)/allbins $(PRJ_BIN_DIR)/elfs
	rm -fr $(CAP_ROOTFS_DIR)/etc_rw
	cp -afvp $(PRJ_PRODUCT_DIR)/fs/cap/rootfs/*  $(CAP_ROOTFS_DIR)/
	cp -v $(PRJ_BIN_DIR)/allbins/ap_capuserdata.img  $(CAP_ROOTFS_DIR)/etc_ro/
	chmod -R a+r $(CAP_ROOTFS_DIR)/etc_ro
	find $(CAP_ROOTFS_DIR)/bin -type f | xargs chmod a+x
	-find $(CAP_ROOTFS_DIR)/sbin -type f | xargs chmod a+x
	chmod a+x $(CAP_ROOTFS_DIR)/etc/rc
	@cd $(BUILD_DIR); bash ./install_libc.sh $(CAP_ROOTFS_DIR)
	find $(CAP_ROOTFS_DIR)/ -name '.gitkeep' -print0 | xargs -0 rm -fr
	find $(CAP_ROOTFS_DIR)/ -name '.git' -print0 | xargs -0 rm -fr
	find $(CAP_ROOTFS_DIR)/ -name '.gitignore' -print0 | xargs -0 rm -fr
	find $(CAP_ROOTFS_DIR)/ -name '.svn' -print0 | xargs -0 rm -fr

ifeq ($(ROOT_FS_TYPE),ubifs)
	bash $(BUILD_DIR)/ubifs.sh rootfs $(CAP_ROOTFS_SIZE) $(CAP_ROOTFS_DIR) $(IMAGE_DIR)/ap_caprootfs.img
else
ifeq ($(ROOT_FS_TYPE),jffs2)
ifeq ($(PRJ_IS_MIN),yes)
	bash $(BUILD_DIR)/jffs2_lzma.sh $(CAP_ROOTFS_DIR) $(IMAGE_DIR)/ap_caprootfs.img lzma
else
	bash $(BUILD_DIR)/jffs2_lzma.sh $(CAP_ROOTFS_DIR) $(IMAGE_DIR)/ap_caprootfs.img lzo
endif
endif
endif

	(cd $(CAP_ROOTFS_DIR) && find . -type f -printf '"/%P" Regular 14 %04m:%4U:%4G\n' >  ../caprootfs_attr.txt)
	(cd $(CAP_ROOTFS_DIR) && find . -type l -printf '"/%P" SymLink 14 %04m:%4U:%4G\n' >> ../caprootfs_attr.txt)
	(cd $(CAP_ROOTFS_DIR) && find . -type d -printf '"/%P" Regular 14 %04m:%4U:%4G\n' >> ../caprootfs_attr.txt)
	(cd $(CAP_ROOTFS_DIR)/../ && tar -zcvf $(PRJ_BIN_DIR)/allbins/caprootfs.tgz caprootfs caprootfs_attr.txt)
	@rm $(CAP_ROOTFS_DIR)/../caprootfs_attr.txt

imagefs:
	mkdir -p $(IMAGEFS_DIR)
	rm -frv $(IMAGEFS_DIR)/*
	cp -v $(PRJ_BIN_DIR)/allbins/evb_cpuphy.bin  $(IMAGEFS_DIR)/
ifeq ($(LARGEFILE_SPLIT_ENABLE),yes)
	(cd $(IMAGEFS_DIR) && split -b $(SPLIT_BLOCK_SIZE) evb_cpuphy.bin  -d -a 2 cpuphy_)
	(cd $(IMAGEFS_DIR) && ls -1 cpuphy_* | xargs -I {} minilzma -t $(LZMA_DICT) {})
	rm -fv $(IMAGEFS_DIR)/evb_cpuphy.bin
endif
	cp -v $(PRJ_BIN_DIR)/allbins/ap_cpuap.bin    $(IMAGEFS_DIR)/
ifeq ($(BUILD_CAP),yes)
	cp -v $(PRJ_BIN_DIR)/allbins/ap_cpucap.bin    $(IMAGEFS_DIR)/
ifeq ($(LARGEFILE_SPLIT_ENABLE),yes)
	(cd $(IMAGEFS_DIR) && split -b $(SPLIT_BLOCK_SIZE) ap_cpucap.bin  -d -a 2 cpucap_)
	(cd $(IMAGEFS_DIR) && ls -1 cpucap_* | xargs -I {} minilzma -t $(LZMA_DICT)  {})
	rm -fv $(IMAGEFS_DIR)/ap_cpucap.bin
endif
endif

ifeq ($(LARGEFILE_SPLIT_ENABLE),yes)
	(cd $(IMAGEFS_DIR) && split -b $(SPLIT_BLOCK_SIZE) ap_cpuap.bin  -d -a 2 cpuap_)
	(cd $(IMAGEFS_DIR) && ls -1 cpuap_* | xargs -I {} minilzma -t $(LZMA_DICT)  {})
	rm -fv $(IMAGEFS_DIR)/ap_cpuap.bin
endif
ifeq ($(USE_FOTA),yes)
ifneq ($(USE_FOTA_AB),yes)
	cp -v $(PRJ_BIN_DIR)/allbins/ap_recovery.bin    $(IMAGEFS_DIR)/
ifeq ($(LARGEFILE_SPLIT_ENABLE),yes)
	(cd $(IMAGEFS_DIR) && split -b $(SPLIT_BLOCK_SIZE) ap_recovery.bin  -d -a 2 recovery_)
	(cd $(IMAGEFS_DIR) && ls -1 recovery_* | xargs -I {} minilzma -t $(LZMA_DICT)  {})
	rm -fv $(IMAGEFS_DIR)/ap_recovery.bin
endif
endif
endif
	cp -v $(PRJ_BIN_DIR)/allbins/evb_cpurpm.img  $(IMAGEFS_DIR)/
	cp -v $(PRJ_BIN_DIR)/allbins/nvrwoall.bin    $(IMAGEFS_DIR)/
	cp -v $(PRJ_BIN_DIR)/allbins/nvrwall.bin     $(IMAGEFS_DIR)/
	md5sum $(IMAGEFS_DIR)/nvrwall.bin | awk '{printf $$1}' > $(IMAGEFS_DIR)/nvrwall.hash
ifeq ($(USE_FOTA),yes)
ifneq ($(USE_FOTA_AB),yes)
	dd if=/dev/zero  bs=1024 count=1 2> /dev/null | tr \\000 \\377 > $(IMAGEFS_DIR)/fotaflag
endif
endif
	#minilzma -t $(LZMA_DICT) $(IMAGEFS_DIR)/nvrwall.bin
ifeq ($(PRJ_IS_MIN),yes)
	bash $(BUILD_DIR)/jffs2_lzma.sh $(IMAGEFS_DIR)    $(IMAGE_DIR)/ap_imagefs.img lzma
else
	bash $(BUILD_DIR)/jffs2_lzma.sh $(IMAGEFS_DIR)    $(IMAGE_DIR)/ap_imagefs.img lzo
endif
	cp -v $(IMAGE_DIR)/ap_imagefs.img            $(PRJ_BIN_DIR)/allbins/
	
	(cd $(IMAGEFS_DIR) && find . -type f -printf '"/%P" Regular 14 %04m:%4U:%4G\n' >  ../imagefs_attr.txt)
	(cd $(IMAGEFS_DIR) && find . -type l -printf '"/%P" SymLink 14 %04m:%4U:%4G\n' >> ../imagefs_attr.txt)
	(cd $(IMAGEFS_DIR) && find . -type d -printf '"/%P" Regular 14 %04m:%4U:%4G\n' >> ../imagefs_attr.txt)
	(cd $(IMAGEFS_DIR)/../ && tar -zcvf $(PRJ_BIN_DIR)/allbins/imagefs.tgz imagefs imagefs_attr.txt)
	@rm $(IMAGEFS_DIR)/../imagefs_attr.txt

nvrofs:
	mkdir -p $(NVROFS_DIR)
	cp -v $(PRJ_BIN_DIR)/allbins/nvroall.bin    $(NVROFS_DIR)/
ifeq ($(PRJ_IS_MIN),yes)
	bash $(BUILD_DIR)/jffs2_lzma.sh $(NVROFS_DIR)    $(IMAGE_DIR)/ap_nvrofs.img lzma
else
	bash $(BUILD_DIR)/jffs2_lzma.sh $(NVROFS_DIR)    $(IMAGE_DIR)/ap_nvrofs.img lzo
endif
	cp -v $(IMAGE_DIR)/ap_nvrofs.img            $(PRJ_BIN_DIR)/allbins/

normal_copybin: normal_check
	mkdir -p $(PRJ_BIN_DIR)/allbins $(PRJ_BIN_DIR)/elfs/normalelfs
	cp -v $(IMAGE_DIR)/rootfs.img $(PRJ_BIN_DIR)/allbins/ap_rootfs.img
ifeq ($(CONFIG_SIGN_IMAGE),yes)
	chmod a+x $(SIGN)
	@$(SIGN) -s $(IMAGE_DIR)/linux_kernel.img $(PRJ_BIN_DIR)/allbins/ap_cpuap.bin $(PRIVATE_KEY_PATH)
else
	@cp -v $(IMAGE_DIR)/linux_kernel.img      $(PRJ_BIN_DIR)/allbins/ap_cpuap.bin
endif
	cp -v $(IMAGE_DIR)/vmlinux $(PRJ_BIN_DIR)/elfs/
	cp -v $(IMAGE_DIR)/System.map $(PRJ_BIN_DIR)/elfs/
	mkdir -p $(PRJ_BIN_DIR)/test_tools/
	cp -afvp $(APP_DIR)/test_tools/*  $(PRJ_BIN_DIR)/test_tools/
	find $(PRJ_BIN_DIR)/test_tools/ -type f  -size +512k -exec ${STRIPTOOL} '{}' ';'

	find   $(APP_DIR)/  -name *.elf  -exec cp {} $(PRJ_BIN_DIR)/elfs/normalelfs \;
	find   $(LIB_DIR)/  -name "l*.so*"  -type f ! -path "*/.libs/*"  -exec cp {} $(PRJ_BIN_DIR)/elfs/normalelfs \;
	find   $(STAGEDIR)/uClibc/lib    -type f -name "*.so*"  -exec cp {} $(PRJ_BIN_DIR)/elfs/normalelfs \;
	find   $(LINUX_DIR)              -type f -name "*.ko"  -exec cp {} $(PRJ_BIN_DIR)/elfs/normalelfs \;

cap_copybin:
	mkdir -p $(PRJ_BIN_DIR)/allbins $(PRJ_BIN_DIR)/elfs/capelfs
	cp -v $(IMAGE_DIR)/ap_caprootfs.img $(PRJ_BIN_DIR)/allbins/
ifeq ($(CONFIG_SIGN_IMAGE),yes)
	chmod a+x $(SIGN)
	@$(SIGN) -s $(IMAGE_DIR)/linux_kernel.img $(PRJ_BIN_DIR)/allbins/ap_cpucap.bin $(PRIVATE_KEY_PATH)
else
	@cp -v $(IMAGE_DIR)/linux_kernel.img      $(PRJ_BIN_DIR)/allbins/ap_cpucap.bin
endif
	cp -v $(IMAGE_DIR)/vmlinux $(PRJ_BIN_DIR)/elfs/vmlinux_cap
	cp -v $(IMAGE_DIR)/System.map $(PRJ_BIN_DIR)/elfs/System_cap.map
	mkdir -p $(PRJ_BIN_DIR)/test_tools/
	cp -afvp $(APP_DIR)/test_tools/*  $(PRJ_BIN_DIR)/test_tools/
	find $(PRJ_BIN_DIR)/test_tools/ -type f  -size +512k -exec ${STRIPTOOL} '{}' ';'

	find   $(APP_DIR)/  -name *.elf  -exec cp {} $(PRJ_BIN_DIR)/elfs/capelfs \;
	find   $(LIB_DIR)/  -type f -name "lib*.so"  -exec cp {} $(PRJ_BIN_DIR)/elfs/capelfs \;
	find   $(STAGEDIR)/uClibc/lib    -type f -name "*.so"  -exec cp {} $(PRJ_BIN_DIR)/elfs/capelfs \;
	find   $(LINUX_DIR)              -type f -name "*.ko"  -exec cp {} $(PRJ_BIN_DIR)/elfs/capelfs \;

recovery_copybin: recovery_check
	mkdir -p $(PRJ_BIN_DIR)/allbins $(PRJ_BIN_DIR)/elfs/recoveryelfs
ifeq ($(USE_RECOVERYFS),yes)
	cp -v $(IMAGE_DIR)/recoveryfs.img $(PRJ_BIN_DIR)/allbins/ap_recoveryfs.img
endif
ifeq ($(CONFIG_SIGN_IMAGE),yes)
	chmod a+x $(SIGN)
	@$(SIGN) -s $(IMAGE_DIR)/linux_kernel.img $(PRJ_BIN_DIR)/allbins/ap_recovery.bin $(PRIVATE_KEY_PATH)
else
	@cp -v $(IMAGE_DIR)/linux_kernel.img      $(PRJ_BIN_DIR)/allbins/ap_recovery.bin
endif
	cp -v $(IMAGE_DIR)/vmlinux $(PRJ_BIN_DIR)/elfs/vmlinux.recovery
	cp -v $(IMAGE_DIR)/System.map $(PRJ_BIN_DIR)/elfs/System.map.recovery
ifeq ($(USE_RECOVERYFS),yes)
	find   $(APP_DIR)/  -name *.elf  -exec cp {} $(PRJ_BIN_DIR)/elfs/recoveryelfs \;
	find   $(LIB_DIR)/  -name "l*.so*"  -type f ! -path "*/.libs/*"  -exec cp {} $(PRJ_BIN_DIR)/elfs/recoveryelfs \;
	find   $(STAGEDIR)/uClibc/lib    -type f -name "*.so*"  -exec cp {} $(PRJ_BIN_DIR)/elfs/recoveryelfs \;
	find   $(LINUX_DIR)              -type f -name "*.ko"  -exec cp {} $(PRJ_BIN_DIR)/elfs/recoveryelfs \;
endif

section_bin:
	echo $(CROSS_COMPILE)
	echo $(UIMAGE_LOADADDR)
	rm -frv $(PRJ_BIN_DIR)/allbins/ap_cpuap.bin
	rm -frv $(PRJ_BIN_DIR)/allbins/modem_code.bin
	rm -frv $(ROOTFS_DIR)/bin/modem_code.bin
	$(CROSS_COMPILE)objcopy -O binary -j .modem.text $(IMAGE_DIR)/vmlinux $(PRJ_BIN_DIR)/allbins/modem_code.bin
	$(CROSS_COMPILE)objcopy -O binary -R .modem.text $(IMAGE_DIR)/vmlinux $(PRJ_BIN_DIR)/allbins/ap_tmp.bin
	./os/linux/linux-3.4.x/scripts/mkuboot.sh -A arm -O linux -C none  -T kernel -a 0x205c8000 -e 0x205c8000 -n 'Linux-3.4.110-rt140' -d $(PRJ_BIN_DIR)/allbins/ap_tmp.bin $(PRJ_BIN_DIR)/allbins/uap_tmp.bin
	@$(SIGN) -s $(PRJ_BIN_DIR)/allbins/uap_tmp.bin $(PRJ_BIN_DIR)/allbins/ap_cpuap.bin $(PRIVATE_KEY_PATH)
	cp -v $(PRJ_BIN_DIR)/allbins/modem_code.bin  $(ROOTFS_DIR)/bin/
	rm -frv $(PRJ_BIN_DIR)/allbins/uap_tmp.bin
	rm -frv $(PRJ_BIN_DIR)/allbins/ap_tmp.bin

obfu:
	bash $(BUILD_DIR)/obfu.sh
